import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { MongoClient, Db } from 'mongodb';

@Injectable()
export class DatabaseService implements OnModuleInit, OnModuleDestroy {
  private client: MongoClient;
  private db: Db;

  async onModuleInit() {
    this.client = new MongoClient(process.env.MONGODB_URI);
    await this.client.connect();
    this.db = this.client.db(process.env.DB_NAME);
    console.log('MongoDB connected');
    // console.log('db', this.db)
  }

  async onModuleDestroy() {
    await this.client.close();
    console.log('MongoDB connection closed');
  }

  getCollection<T>(collectionName: string) {
    return this.db.collection<T>(collectionName);
  }
}