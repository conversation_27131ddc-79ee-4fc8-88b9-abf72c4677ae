import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Permissions } from '../auth/decorators/permissions.decorators';

@Controller('products')
@Permissions('products')

export class ProductsController {
  constructor(private readonly productsService: ProductsService) { }

  @Post('create')
  create(@Body() createProductDto) {
    // console.log(createProductDto);
    return this.productsService.create(createProductDto);
  }

  @Get('list')
  async findAll(@Query() search: string) {
    const result = await this.productsService.findAll(search);
    return {
      code: 0,
      data: result,
      msg: '查询成功',
    }
  }

  @Post('update')
  async update(@Body() updateProductDto) {
    const { _id, ...data } = updateProductDto;
    // return this.productsService.update(_id, data);
    try {
      const result = await this.productsService.update(_id, data);
      if (result.modifiedCount === 1) {
        return {
          code: 0,
          msg: '更新成功',
          data: {
            modifiedCount: result.modifiedCount
          }
        };
      } else {
        throw new Error('更新失败');
      }
    } catch (error) {
      return {
        code: 400,
        msg: error.message || '更新失败'
      };
    }
  }

  @Post('delete')
  async delete(@Body('id') id: string) {
    const result = await this.productsService.delete(id);
    if (result.deletedCount === 1) {
      return {
        code: 0,
        msg: '删除成功',
        data: {
          deletedCount: result.deletedCount
        }
      };
    } else {
      throw new Error('删除失败');
    }
  }
}
