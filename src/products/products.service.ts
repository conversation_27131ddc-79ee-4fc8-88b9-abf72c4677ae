    import { Injectable } from '@nestjs/common';
    import { CreateProductDto } from './dto/create-product.dto';
    import { UpdateProductDto } from './dto/update-product.dto';
    import { DatabaseService } from "../database/database.service";
    import {STATUS} from '../const'
    import { ObjectId } from 'mongodb'; // 引入 ObjectId
    const COLLECTION_NAME = 't_products';
    @Injectable()
    export class ProductsService {
    
      constructor(private readonly databaseService: DatabaseService) {
      }
    
      async create(createProductDto) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const data = {
          created_at: new Date().getTime(),
          updated_at: new Date().getTime(),
          status: STATUS.Active,
          ...createProductDto,
        }
        const result = await collection.insertOne(data);
        return result;
      }
    
      async findAll(query: any) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const pn = parseInt(query.pn, 10) || 1; 
        const ps = parseInt(query.ps, 10) || 10; 
        const skip = (pn - 1) * ps; 
        const filter = {};
        if(query.productName){
          filter['productName'] = {$regex: query.productName};
        }
        // console.log(filter)
        const list = await collection.find(filter).skip(skip).limit(ps).toArray(); 
        const total = await collection.countDocuments(filter);
        return {
          list: list,
          total,
          ps: ps,
          pn: pn,
        }
      }
    
      async update(id: string, updateProductDto: UpdateProductDto) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const objectId = new ObjectId(id); // 将 id 转换为 ObjectId
        const result = await collection.updateOne({ _id: objectId }, { $set: updateProductDto });
        return result;
      }

      async delete(id: string) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const objectId = new ObjectId(id); // 将 id 转换为 ObjectId
        const result = await collection.deleteOne({ _id: objectId });
        return result;
      }
    }