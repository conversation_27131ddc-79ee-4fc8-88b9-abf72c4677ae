export class CreateProductDto {
    productName: String;
    banner: [{
        type: String,
        url: String,
        title: String,
        subTitle: String,
    }];
    HighlightsTitle: String;
    HighlightsProductImgs: [{
        type?: String,
        url: String,
        title: String,
        subTitle: String,
    }];
    threeDModel: {
        type?: String,
        url: String,
        title: String,
        subTitle: String,
    };
    scenTitle: String;
    scenImgs: [{
        type?: String,
        url: String,
        title: String,
        subTitle: String,
    }];
    productVideoTitle: String;
    productVideo: [{
        type?: String,
        url: String,
        title: String,
        subTitle: String,
    }];
    compareTitle: String;
    compareProduct: [
        {
            imgUrl: String,
            name: String,
            title: String,
            subTitle: String,
            productId: String,
        }
    ];
    params: [
        {
            title: String,
            content: [
                {
                    name: String,
                    value: String,
                }
            ]
        }
    ];
    status: String;
    created_at: Number;
    updated_at: Number;
}
