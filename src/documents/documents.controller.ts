import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  Query, 
  UseInterceptors, 
  UploadedFile,
  Res,
  HttpException,
  HttpStatus
} from '@nestjs/common';
import { DocumentsService } from './documents.service';
import { CreateDocumentDto } from './dto/create-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { Response } from 'express';
import { Public } from 'src/auth/decorators/public.decorators';
import { Permissions } from 'src/auth/decorators/permissions.decorators';

@Controller('documents')
@Permissions('documents')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  // 上传文档
  @Post('upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: process.env.FILE_PATH + '/raw/documents',
        filename: (req, file, callback) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
          const ext = extname(file.originalname);
          const filename = `${uniqueSuffix}${ext}`;
          callback(null, filename);
        },
      }),
      fileFilter: (req, file, callback) => {
        // 允许的文件类型
        const allowedTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'text/plain',
          'application/zip',
          'application/x-rar-compressed',
          'application/json',
          'text/csv'
        ];
        
        if (allowedTypes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(new Error('不支持的文件类型'), false);
        }
      },
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB 限制
      },
    }),
  )
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any
  ) {
    if (!file) {
      throw new HttpException('文件上传失败', HttpStatus.BAD_REQUEST);
    }

    // 根据文件扩展名确定文档类型
    const ext = extname(file.originalname).toLowerCase();
    let documentType: 'doc' | 'model' | 'other' = 'other';
    
    if (['.pdf', '.doc', '.docx', '.txt'].includes(ext)) {
      documentType = 'doc';
    } else if (['.xls', '.xlsx', '.json', '.csv'].includes(ext)) {
      documentType = 'model';
    }

    const createDocumentDto: CreateDocumentDto = {
      name: body.name || file.originalname,
      originalName: file.originalname,
      type: body.type || documentType,
      size: file.size,
      url: `/raw/documents/${file.filename}`,
    };

    const result = await this.documentsService.create(createDocumentDto);
    
    return {
      code: 0,
      data: {
        id: result.insertedId,
        url: createDocumentDto.url,
        name: createDocumentDto.name,
        type: createDocumentDto.type,
        size: createDocumentDto.size
      },
      msg: '文档上传成功',
    };
  }

  // 获取文档列表
  @Get('list')
  async findAll(@Query() query: any) {
    const result = await this.documentsService.findAll(query);
    return {
      code: 0,
      data: result,
      msg: '查询成功',
    };
  }

  // 获取单个文档信息
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const result = await this.documentsService.findOne(id);
    if (!result) {
      throw new HttpException('文档不存在', HttpStatus.NOT_FOUND);
    }
    return {
      code: 0,
      data: result,
      msg: '查询成功',
    };
  }

  // 下载文档（增加下载次数）
  @Public()
  @Get('download/:id')
  async downloadDocument(@Param('id') id: string, @Res() res: Response) {
    const document = await this.documentsService.findOne(id);
    if (!document) {
      throw new HttpException('文档不存在', HttpStatus.NOT_FOUND);
    }

    // 增加下载次数
    await this.documentsService.incrementDownloadCount(id);

    // 构建文件路径
    const filePath = join(process.env.FILE_PATH, document.url);
    
    // 设置响应头
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(document.originalName)}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    
    // 发送文件
    res.sendFile(filePath, (err) => {
      if (err) {
        throw new HttpException('文件下载失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    });
  }

  // 更新文档信息
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateDocumentDto: UpdateDocumentDto) {
    const result = await this.documentsService.update(id, updateDocumentDto);
    if (result.matchedCount === 0) {
      throw new HttpException('文档不存在', HttpStatus.NOT_FOUND);
    }
    return {
      code: 0,
      data: result,
      msg: '更新成功',
    };
  }

  // 删除文档
  @Delete(':id')
  async remove(@Param('id') id: string) {
    const result = await this.documentsService.remove(id);
    if (result.deletedCount === 0) {
      throw new HttpException('文档不存在', HttpStatus.NOT_FOUND);
    }
    return {
      code: 0,
      data: result,
      msg: '删除成功',
    };
  }

  // 获取统计信息
  @Get('statistics/overview')
  async getStatistics() {
    const result = await this.documentsService.getStatistics();
    return {
      code: 0,
      data: result,
      msg: '查询成功',
    };
  }
}
