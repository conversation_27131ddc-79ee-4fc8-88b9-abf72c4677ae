import { Injectable } from '@nestjs/common';
import { CreateDocumentDto } from './dto/create-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { DatabaseService } from 'src/database/database.service';
import { ObjectId } from 'mongodb';
import { Document } from './entities/document.entity';

const COLLECTION_NAME = 't_documents';

@Injectable()
export class DocumentsService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(createDocumentDto: CreateDocumentDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = {
      created_at: new Date().getTime(),
      updated_at: new Date().getTime(),
      downloadCount: 0,
      ...createDocumentDto,
    };
    const result = await collection.insertOne(data);
    return result;
  }

  async findAll(query: any): Promise<{ list: Document[], total: number, ps: number, pn: number }> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const pn = parseInt(query.pn, 10) || 1;
    const ps = parseInt(query.ps, 10) || 10;
    const skip = (pn - 1) * ps;
    
    const filter: any = {};
    
    // 按文档名称搜索
    if (query.name) {
      filter['name'] = { $regex: query.name, $options: 'i' };
    }
    
    // 按文档类型过滤
    if (query.type) {
      filter['type'] = query.type;
    }
    
    const list = await collection.find(filter)
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(ps)
      .toArray();
    
    const total = await collection.countDocuments(filter);
    
    return {
      list: list as any[],
      total,
      ps: ps,
      pn: pn,
    };
  }

  async findOne(id: string): Promise<Document> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    return await collection.findOne({ _id: objectId }) as any;
  }

  async update(id: string, updateDocumentDto: UpdateDocumentDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    const data = {
      updated_at: new Date().getTime(),
      ...updateDocumentDto,
    };
    const result = await collection.updateOne({ _id: objectId }, { $set: data });
    return result;
  }

  async remove(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    const result = await collection.deleteOne({ _id: objectId });
    return result;
  }

  // 增加下载次数
  async incrementDownloadCount(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    const result = await collection.updateOne(
      { _id: objectId },
      { 
        $inc: { downloadCount: 1 },
        $set: { updated_at: new Date().getTime() }
      }
    );
    return result;
  }

  // 获取统计信息
  async getStatistics() {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    
    const totalDocuments = await collection.countDocuments();
    const docCount = await collection.countDocuments({ type: 'doc' });
    const modelCount = await collection.countDocuments({ type: 'model' });
    const otherCount = await collection.countDocuments({ type: 'other' });
    
    const totalDownloads = await collection.aggregate([
      { $group: { _id: null, total: { $sum: '$downloadCount' } } }
    ]).toArray();
    
    return {
      totalDocuments,
      docCount,
      modelCount,
      otherCount,
      totalDownloads: totalDownloads[0]?.total || 0
    };
  }
}
