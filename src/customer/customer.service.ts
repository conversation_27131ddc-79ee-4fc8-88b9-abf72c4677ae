import { Injectable } from '@nestjs/common';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { QueryCustomerDto } from './dto/query-customer.dto';
import { CreateSubscribeDto } from './dto/create-subscribe.dto';
import { ObjectId } from 'mongodb';
import { DatabaseService } from 'src/database/database.service';
const COLLECTION_NAME = 't_customer';

@Injectable()
export class CustomerService {

  async findAllSubscribe(query: any): Promise<{ list: any[], total: number, ps: number, pn: number }> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const total = await collection.countDocuments({ type: 'subscribe' });
    const ps = query.ps || 10;
    const pn = query.pn || 1;
    const list = await collection.find({ type: 'subscribe' }).skip((pn - 1) * ps).limit(ps).toArray();
    return {
      list,
      total,
      ps,
      pn,
    }
  }

  constructor(private readonly databaseService: DatabaseService) { }
  async subscribe(createSubscribeDto: CreateSubscribeDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = {
      created_at: new Date().getTime(),
      updated_at: new Date().getTime(),
      type: 'subscribe',
      ...createSubscribeDto,
    };
    const result = await collection.insertOne(data);
    return result;
  }
  async create(createCustomerDto: CreateCustomerDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = {
      created_at: new Date().getTime(),
      updated_at: new Date().getTime(),
      type: 'customer',
      ...createCustomerDto,
    };
    const result = await collection.insertOne(data);
    return result;
  }

  async findAll(query: QueryCustomerDto): Promise<{ list: any[], total: number, ps: number, pn: number }> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);

    // 构建查询条件
    const filter: any = { type: 'customer' };

    if (query.name) {
      filter.name = { $regex: query.name, $options: 'i' };
    }
    if (query.phone) {
      filter.phone = { $regex: query.phone, $options: 'i' };
    }
    if (query.email) {
      filter.email = { $regex: query.email, $options: 'i' };
    }
    if (query.company) {
      filter.company = { $regex: query.company, $options: 'i' };
    }
    if (query.province) {
      filter.province = { $regex: query.province, $options: 'i' };
    }
    if (query.products) {
      filter.products = { $regex: query.products, $options: 'i' };
    }
    if (query.downloadedFile) {
      filter.downloadedFile = { $regex: query.downloadedFile, $options: 'i' };
    }

    // 日期范围查询
    if (query.startDate || query.endDate) {
      filter.created_at = {};
      if (query.startDate) {
        filter.created_at.$gte = new Date(query.startDate).getTime();
      }
      if (query.endDate) {
        filter.created_at.$lte = new Date(query.endDate).getTime();
      }
    }

    const total = await collection.countDocuments(filter);
    const ps = parseInt(query.ps?.toString() || '10', 10);
    const pn = parseInt(query.pn?.toString() || '1', 10);

    // 构建排序条件
    const sort: any = {};
    if (query.sortBy) {
      sort[query.sortBy] = query.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.created_at = -1; // 默认按创建时间倒序
    }

    const list = await collection
      .find(filter)
      .sort(sort)
      .skip((pn - 1) * ps)
      .limit(ps)
      .toArray();

    return {
      list,
      total,
      ps,
      pn,
    };
  }

  async findOne(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const result = await collection.findOne({
      _id: new ObjectId(id),
      type: 'customer'
    });
    return result;
  }

  async update(updateCustomerDto: UpdateCustomerDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const { _id, ...updateData } = updateCustomerDto;

    const data = {
      ...updateData,
      updated_at: new Date().getTime(),
    };

    const result = await collection.updateOne(
      { _id: new ObjectId(_id), type: 'customer' },
      { $set: data }
    );
    return result;
  }

  async remove(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const result = await collection.deleteOne({
      _id: new ObjectId(id),
      type: 'customer'
    });
    return result;
  }

  async exportCustomers(query: QueryCustomerDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);

    // 构建查询条件（与findAll相同的逻辑）
    const filter: any = { type: 'customer' };

    if (query.name) {
      filter.name = { $regex: query.name, $options: 'i' };
    }
    if (query.phone) {
      filter.phone = { $regex: query.phone, $options: 'i' };
    }
    if (query.email) {
      filter.email = { $regex: query.email, $options: 'i' };
    }
    if (query.company) {
      filter.company = { $regex: query.company, $options: 'i' };
    }
    if (query.province) {
      filter.province = { $regex: query.province, $options: 'i' };
    }
    if (query.products) {
      filter.products = { $regex: query.products, $options: 'i' };
    }
    if (query.downloadedFile) {
      filter.downloadedFile = { $regex: query.downloadedFile, $options: 'i' };
    }

    // 日期范围查询
    if (query.startDate || query.endDate) {
      filter.created_at = {};
      if (query.startDate) {
        filter.created_at.$gte = new Date(query.startDate).getTime();
      }
      if (query.endDate) {
        filter.created_at.$lte = new Date(query.endDate).getTime();
      }
    }

    // 构建排序条件
    const sort: any = {};
    if (query.sortBy) {
      sort[query.sortBy] = query.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.created_at = -1; // 默认按创建时间倒序
    }

    const customers = await collection
      .find(filter)
      .sort(sort)
      .toArray();

    return customers;
  }
}
