import { Controller, Get, Post, Body, Patch, Param, Delete, Header, Query, HttpException, HttpStatus, Res } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { QueryCustomerDto } from './dto/query-customer.dto';
import { Permissions } from '../auth/decorators/permissions.decorators';
import { Public } from 'src/auth/decorators/public.decorators';
import { CreateSubscribeDto } from './dto/create-subscribe.dto';
import { Response } from 'express';
import * as XLSX from 'xlsx';
import * as csvWriter from 'csv-writer';

// @Permissions('customer')
@Public()
@Controller('customer')
export class CustomerController {
  constructor(private readonly customerService: CustomerService) {}

  @Post('subscribe')
  async subscribe(@Body() createSubscribeDto: CreateSubscribeDto) {
    if (!createSubscribeDto.email) {
      throw new HttpException('邮箱不能为空', HttpStatus.BAD_REQUEST);
    }
    if (!this.checkEmail(createSubscribeDto.email)) {
      throw new HttpException('邮箱格式错误', HttpStatus.BAD_REQUEST);
    }
    return this.customerService.subscribe(createSubscribeDto);
  }

  @Get('subscribe')
  async findSubscribe(@Query() query: any) {
    return this.customerService.findAllSubscribe(query);
  }

  @Post('create')
  async createCustomer(@Body() createCustomerDto: CreateCustomerDto) {
    // 验证必填字段
    if (!createCustomerDto.name) {
      throw new HttpException('客户姓名不能为空', HttpStatus.BAD_REQUEST);
    }
    if (!createCustomerDto.phone) {
      throw new HttpException('手机号不能为空', HttpStatus.BAD_REQUEST);
    }
    if (!createCustomerDto.email) {
      throw new HttpException('邮箱不能为空', HttpStatus.BAD_REQUEST);
    }
    if (!this.checkEmail(createCustomerDto.email)) {
      throw new HttpException('邮箱格式错误', HttpStatus.BAD_REQUEST);
    }

    const result = await this.customerService.create(createCustomerDto);
    if (result) {
      return {
        code: 0,
        msg: '创建成功',
        data: result
      };
    } else {
      return {
        code: 400,
        msg: '创建失败',
      };
    }
  }

  @Get('list')
  async findAll(@Query() query: QueryCustomerDto) {
    const result = await this.customerService.findAll(query);
    return {
      code: 0,
      data: result,
      msg: '查询成功',
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const result = await this.customerService.findOne(id);
    if (result) {
      return {
        code: 0,
        data: result,
        msg: '查询成功',
      };
    } else {
      throw new HttpException('客户不存在', HttpStatus.NOT_FOUND);
    }
  }

  @Patch('update')
  async update(@Body() updateCustomerDto: UpdateCustomerDto) {
    if (!updateCustomerDto._id) {
      throw new HttpException('客户ID不能为空', HttpStatus.BAD_REQUEST);
    }

    // 验证邮箱格式
    if (updateCustomerDto.email && !this.checkEmail(updateCustomerDto.email)) {
      throw new HttpException('邮箱格式错误', HttpStatus.BAD_REQUEST);
    }

    const result = await this.customerService.update(updateCustomerDto);
    if (result.modifiedCount > 0) {
      return {
        code: 0,
        msg: '更新成功',
        data: result
      };
    } else {
      throw new HttpException('更新失败或客户不存在', HttpStatus.BAD_REQUEST);
    }
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    const result = await this.customerService.remove(id);
    if (result.deletedCount > 0) {
      return {
        code: 0,
        msg: '删除成功',
        data: result
      };
    } else {
      throw new HttpException('删除失败或客户不存在', HttpStatus.BAD_REQUEST);
    }
  }

  @Get('export/excel')
  async exportExcel(@Query() query: QueryCustomerDto, @Res() res: Response) {
    const customers = await this.customerService.exportCustomers(query);

    // 转换数据格式
    const data = customers.map((customer: any) => ({
      '客户姓名': customer.name,
      '公司名称': customer.company || '',
      '手机号': customer.phone,
      '邮箱': customer.email,
      '省份': customer.province || '',
      '产品': customer.products || '',
      '备注': customer.remark || '',
      '下载文件': customer.downloadedFile || '',

      '创建时间': new Date(customer.created_at).toLocaleString('zh-CN'),
      '更新时间': new Date(customer.updated_at).toLocaleString('zh-CN'),
    }));

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(wb, ws, '客户列表');

    // 生成Excel文件
    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=customers_${new Date().getTime()}.xlsx`);

    res.send(buffer);
  }

  @Get('export/csv')
  async exportCsv(@Query() query: QueryCustomerDto, @Res() res: Response) {
    const customers = await this.customerService.exportCustomers(query);

    // 转换数据格式
    const data = customers.map((customer: any) => ({
      name: customer.name,
      company: customer.company || '',
      phone: customer.phone,
      email: customer.email,
      province: customer.province || '',
      products: customer.products || '',
      remark: customer.remark || '',
      downloadedFile: customer.downloadedFile || '',

      created_at: new Date(customer.created_at).toLocaleString('zh-CN'),
      updated_at: new Date(customer.updated_at).toLocaleString('zh-CN'),
    }));

    // 创建CSV内容
    const createCsvWriter = csvWriter.createObjectCsvStringifier({
      header: [
        { id: 'name', title: '客户姓名' },
        { id: 'company', title: '公司名称' },
        { id: 'phone', title: '手机号' },
        { id: 'email', title: '邮箱' },
        { id: 'province', title: '省份' },
        { id: 'products', title: '产品' },
        { id: 'remark', title: '备注' },
        { id: 'downloadedFile', title: '下载文件' },
        { id: 'created_at', title: '创建时间' },
        { id: 'updated_at', title: '更新时间' },
      ]
    });

    const csvString = createCsvWriter.getHeaderString() + createCsvWriter.stringifyRecords(data);

    // 设置响应头
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename=customers_${new Date().getTime()}.csv`);

    // 添加BOM以支持中文
    res.send('\uFEFF' + csvString);
  }

  private checkEmail(email: string) {
    const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;
    return reg.test(email);
  }
}
