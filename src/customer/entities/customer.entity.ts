export class Customer {
  _id?: string;           // MongoDB ObjectId
  name: string;           // 客户姓名
  company: string;        // 公司名称
  phone: string;          // 手机号
  email: string;          // 邮箱
  province: string;       // 省份
  products: string;       // 产品
  remark?: string;        // 备注
  downloadedFile?: string; // 下载的文件名
  type: string;           // 类型：customer-客户，subscribe-订阅
  created_at: number;     // 创建时间戳
  updated_at: number;     // 更新时间戳
}
