import { Controller, Get, Post, Body, Patch, Param, Delete, Query, NotFoundException, Res } from '@nestjs/common';
import { NewsService } from './news.service';
import { CreateNewsDto } from './dto/create-news.dto';
import { UpdateNewsDto } from './dto/update-news.dto';
import { ImportFromUrlDto } from './dto/import-from-url.dto';
import { Permissions } from '../auth/decorators/permissions.decorators';
import { Public } from 'src/auth/decorators/public.decorators';
import { Response } from 'express';
@Controller('news')
@Permissions('news')
export class NewsController {
  constructor(private readonly newsService: NewsService) { }

  @Post('create')
  async create(@Body() createNewsDto: CreateNewsDto) {
    const result = await this.newsService.create(createNewsDto);
    return {
      code: 0,
      data: result,
      msg: '创建成功',
    }
  }

  @Public()
  @Get('list')
  async findAll(@Query() query: any) {
    const result = await this.newsService.findAll(query);
    return {
      code: 0,
      data: result,
      msg: '查询成功',
    }
  }

  @Public()
  @Get('news-type')
  async newsType() {
    return {
      code: 0,
      data: [
        {
          label: '产品新闻',
          value: 'productNews'
        },
        {
          label: '赛事活动',
          value: 'activity'
        },
        {
          label: '展会资讯',
          value: 'exhibition'
        }
      ],
      msg: '查询成功',
    }
  }

  @Public()
  @Get('preview2/:id')
  async preview(@Param('id') id: string) {
    const result:any = await this.newsService.findOne(id);
    if (!result) {
      throw new NotFoundException('新闻不存在');
    }

    // 生成简单的HTML页面展示新闻内容
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>${result.title}</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .header {
              border-bottom: 1px solid #eee;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .title {
              font-size: 2em;
              margin-bottom: 10px;
              color: #222;
            }
            .meta {
              color: #666;
              font-size: 0.9em;
            }
            .content {
              font-size: 1.1em;
            }
            img {
              max-width: 100%;
              height: auto;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1 class="title">${result.title}</h1>
            <div class="meta">
              <span>发布时间: ${new Date(result.created_at).toLocaleString()}</span>
            </div>
          </div>
          <div class="content">
            ${result.content || '暂无内容'}
          </div>
        </body>
      </html>
    `;

    //返回html渲染
     
  }

  @Get('preview/:id')
  async previewNews(@Param('id') id: string, @Res() res: Response) {
    try {
      const news:any = await this.newsService.findOne(id);
      if (!news) {
        return res.status(404).send('News not found');
      }

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${news.title}</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .news-container { max-width: 800px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
            .news-title { font-size: 28px; font-weight: bold; margin-bottom: 10px; color: #333; }
            .news-subtitle { font-size: 18px; color: #666; margin-bottom: 20px; }
            .news-meta { color: #999; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 20px; margin-bottom: 20px; }
            .news-content { line-height: 1.6; color: #444; }
            *{box-sizing: border-box;margin:0;}
          </style>
        </head>
        <body>
          <div class="news-container">
            <h1 class="news-title">${news.title}</h1>
            <div class="news-subtitle">${news.subTitle}</div>
            <div class="news-meta">
              <span>发布时间: ${new Date(news.created_at).toLocaleString()}</span>
            </div>
            <div class="news-content">${news.content}</div>
          </div>
        </body>
        </html>
      `;

      res.setHeader('Content-Type', 'text/html');
      return res.send(htmlContent);
    } catch (error) {
      return res.status(500).send('Error generating preview');
    }
  }

  @Post('update')
  async update(@Body() UpdateNewsDto: UpdateNewsDto) {
    try {
      const result = await this.newsService.update(UpdateNewsDto);
      if (result.modifiedCount === 1) {
        return {
          code: 0,
          msg: '更新成功',
          data: {
            modifiedCount: result.modifiedCount
          }
        };
      } else {
        throw new Error('更新失败');
      }
    } catch (error) {
      return {
        code: 400,
        msg: error.message || '更新失败'
      };
    }
  }

  @Post('delete')
  async delete(@Body('id') id: string) {
    const result = await this.newsService.delete(id);
    if (result.deletedCount === 1) {
      return {
        code: 0,
        msg: '删除成功',
        data: {
          deletedCount: result.deletedCount
        }
      };
    } else {
      throw new Error('删除失败');
    }
  }

  @Post('import-from-url')
  @Permissions('news')
  async importFromUrl(@Body() importDto: ImportFromUrlDto) {
    try {
      const result = await this.newsService.importFromUrl(importDto);
      return {
        code: 0,
        data: result,
        msg: '导入成功',
      };
    } catch (error) {
      return {
        code: 400,
        msg: error.message || '导入失败'
      };
    }
  }
}
