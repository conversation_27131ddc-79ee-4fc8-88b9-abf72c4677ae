import { IsString, IsOptional, IsBoolean } from 'class-validator';

export class CreateNewsDto {
  @IsString()
  title: string;

  @IsString()
  subTitle: string;

  @IsString()
  newsType: string;

  @IsString()
  image: string;

  @IsString()
  content: string;

  @IsOptional()
  @IsString()
  remark?: string;

  @IsOptional()
  @IsString()
  sourceUrl?: string;

  @IsOptional()
  @IsBoolean()
  isImported?: boolean;
}
