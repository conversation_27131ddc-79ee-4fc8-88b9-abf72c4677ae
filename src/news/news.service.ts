import { Injectable, Logger } from '@nestjs/common';
import { CreateNewsDto } from './dto/create-news.dto';
import { UpdateNewsDto } from './dto/update-news.dto';
import { ImportFromUrlDto } from './dto/import-from-url.dto';
import { DatabaseService } from 'src/database/database.service';
import { ObjectId } from 'mongodb';
import { WebScrapingService } from './services/web-scraping.service';
import { ImageDownloadService } from './services/image-download.service';
const COLLECTION_NAME = 't_news';

@Injectable()
export class NewsService {
  private readonly logger = new Logger(NewsService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly webScrapingService: WebScrapingService,
    private readonly imageDownloadService: ImageDownloadService
  ) { }

  async create(createNewsDto: CreateNewsDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = {
      created_at: new Date().getTime(),
      updated_at: new Date().getTime(),
      ...createNewsDto,
    };
    const result = await collection.insertOne(data);
    return result;
  }

  async findAll(query: any): Promise<{ list: any[], total: number, ps: number, pn: number }> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const pn = parseInt(query.pn, 10) || 1;
    const ps = parseInt(query.ps, 10) || 10;
    const skip = (pn - 1) * ps;
    const filter = {};
    if (query.title && typeof query.title === 'string' && query.title.trim() !== '') {
      filter['title'] = { $regex: query.title, $options: 'i' };
    }
    if (query.newsType && typeof query.newsType === 'string' && query.newsType.trim() !== '') {
      filter['newsType'] = { $regex: query.newsType, $options: 'i' };
    }
    const list = await collection.find(filter).skip(skip).limit(ps).toArray();
    const total = await collection.countDocuments(filter);
    return {
      list: list,
      total,
      ps: ps,
      pn: pn,
    };
  }

  async findOne(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = await collection.findOne({ _id: new ObjectId(id) });
    return data;
  }

  async update(updateNewsDto: UpdateNewsDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const { _id, ...data } = updateNewsDto;
    const objectId = new ObjectId(_id);
    const result = await collection.updateOne({ _id: objectId }, { $set: data });
    return result;
  }

  async delete(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id); // 将 id 转换为 ObjectId
    const result = await collection.deleteOne({ _id: objectId });
    return result;
  }

  async importFromUrl(importDto: ImportFromUrlDto) {
    this.logger.log(`开始从URL导入文章: ${importDto.url}`);

    try {
      // 1. 抓取网页内容
      const scrapedContent = await this.webScrapingService.scrapeWebPage(importDto.url);

      // 2. 创建临时文章记录以获取ID
      const collection = this.databaseService.getCollection(COLLECTION_NAME);
      const tempData = {
        title: scrapedContent.title,
        subTitle: scrapedContent.title, // 临时使用标题作为副标题
        newsType: 'productNews', // 默认类型
        image: '', // 稍后更新
        content: scrapedContent.content,
        sourceUrl: scrapedContent.sourceUrl,
        isImported: true,
        created_at: new Date().getTime(),
        updated_at: new Date().getTime(),
      };

      const insertResult = await collection.insertOne(tempData);
      const articleId = insertResult.insertedId.toString();

      this.logger.log(`创建临时文章记录，ID: ${articleId}`);

      // 3. 下载图片并更新内容
      if (scrapedContent.images.length > 0) {
        const { updatedContent, downloadResults } = await this.imageDownloadService.downloadImagesAndUpdateContent(
          scrapedContent.content,
          articleId,
          scrapedContent.images,
          scrapedContent.sourceUrl
        );

        // 4. 更新文章内容和封面图片
        const firstSuccessfulImage = downloadResults.find(r => r.success);
        const updateData: any = {
          content: updatedContent,
          updated_at: new Date().getTime(),
        };

        if (firstSuccessfulImage) {
          updateData.image = firstSuccessfulImage.localUrl;
        }

        await collection.updateOne(
          { _id: insertResult.insertedId },
          { $set: updateData }
        );

        this.logger.log(`文章导入完成，图片下载结果: 成功 ${downloadResults.filter(r => r.success).length}/${downloadResults.length}`);

        return {
          articleId,
          title: scrapedContent.title,
          content: updatedContent,
          imageCount: downloadResults.filter(r => r.success).length,
          totalImages: downloadResults.length,
          downloadResults
        };
      } else {
        this.logger.log('文章导入完成，无图片需要下载');

        return {
          articleId,
          title: scrapedContent.title,
          content: scrapedContent.content,
          imageCount: 0,
          totalImages: 0,
          downloadResults: []
        };
      }

    } catch (error) {
      this.logger.error(`从URL导入文章失败: ${error.message}`, error.stack);
      throw new Error(`导入失败: ${error.message}`);
    }
  }
}