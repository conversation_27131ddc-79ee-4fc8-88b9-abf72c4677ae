import { Injectable, Logger } from '@nestjs/common';
import * as puppeteer from 'puppeteer';
import * as cheerio from 'cheerio';
import { URL } from 'url';

export interface ScrapedContent {
  title: string;
  content: string;
  images: string[];
  sourceUrl: string;
}

@Injectable()
export class WebScrapingService {
  private readonly logger = new Logger(WebScrapingService.name);

  async scrapeWebPage(url: string): Promise<ScrapedContent> {
    this.logger.log(`开始抓取网页: ${url}`);
    
    let browser: puppeteer.Browser | null = null;
    
    try {
      // 启动浏览器
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage();
      
      // 设置用户代理
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      // 设置视口
      await page.setViewport({ width: 1920, height: 1080 });
      
      // 访问页面
      await page.goto(url, { 
        waitUntil: 'networkidle2', 
        timeout: 30000 
      });
      
      // 等待页面完全加载
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 获取页面HTML
      const html = await page.content();
      
      // 使用cheerio解析HTML
      const $ = cheerio.load(html);
      
      // 提取标题
      const title = this.extractTitle($);
      
      // 提取内容
      const content = this.extractContent($);
      
      // 提取图片
      const images = this.extractImages($, url);
      
      this.logger.log(`抓取完成: 标题=${title}, 图片数量=${images.length}`);
      
      return {
        title,
        content,
        images,
        sourceUrl: url
      };
      
    } catch (error) {
      this.logger.error(`抓取网页失败: ${error.message}`, error.stack);
      throw new Error(`抓取网页失败: ${error.message}`);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  private extractTitle($: cheerio.CheerioAPI): string {
    // 尝试多种方式提取标题
    let title = '';
    
    // 1. 尝试article标题
    title = $('article h1').first().text().trim();
    if (title) return title;
    
    // 2. 尝试页面标题
    title = $('h1').first().text().trim();
    if (title) return title;
    
    // 3. 尝试meta标题
    title = $('meta[property="og:title"]').attr('content') || '';
    if (title) return title;
    
    // 4. 使用页面title
    title = $('title').text().trim();
    if (title) return title;
    
    return '未知标题';
  }

  private extractContent($: cheerio.CheerioAPI): string {
    // 移除不需要的元素
    $('script, style, nav, header, footer, aside, .advertisement, .ads').remove();
    
    let content = '';
    
    // 1. 尝试article标签
    const article = $('article');
    if (article.length > 0) {
      content = article.html() || '';
      if (content.trim()) return this.cleanContent(content);
    }
    
    // 2. 尝试常见的内容容器
    const contentSelectors = [
      '.content',
      '.article-content',
      '.post-content',
      '.entry-content',
      '.main-content',
      '#content',
      '.article-body',
      '.post-body'
    ];
    
    for (const selector of contentSelectors) {
      const element = $(selector);
      if (element.length > 0) {
        content = element.html() || '';
        if (content.trim()) return this.cleanContent(content);
      }
    }
    
    // 3. 尝试main标签
    const main = $('main');
    if (main.length > 0) {
      content = main.html() || '';
      if (content.trim()) return this.cleanContent(content);
    }
    
    // 4. 如果都没找到，使用body内容
    content = $('body').html() || '';
    return this.cleanContent(content);
  }

  private cleanContent(content: string): string {
    // 清理内容
    const $ = cheerio.load(content);
    
    // 移除不需要的元素
    $('script, style, nav, header, footer, aside, .advertisement, .ads, .sidebar').remove();
    
    return $.html() || '';
  }

  private extractImages($: cheerio.CheerioAPI, baseUrl: string): string[] {
    const images: string[] = [];
    const baseUrlObj = new URL(baseUrl);
    
    $('img').each((_, element) => {
      const src = $(element).attr('src');
      if (src) {
        try {
          // 处理相对路径
          let imageUrl: string;
          if (src.startsWith('http://') || src.startsWith('https://')) {
            imageUrl = src;
          } else if (src.startsWith('//')) {
            imageUrl = baseUrlObj.protocol + src;
          } else if (src.startsWith('/')) {
            imageUrl = `${baseUrlObj.protocol}//${baseUrlObj.host}${src}`;
          } else {
            // 相对路径
            const currentPath = baseUrlObj.pathname.endsWith('/') 
              ? baseUrlObj.pathname 
              : baseUrlObj.pathname.substring(0, baseUrlObj.pathname.lastIndexOf('/') + 1);
            imageUrl = `${baseUrlObj.protocol}//${baseUrlObj.host}${currentPath}${src}`;
          }
          
          // 验证是否为图片格式
          if (this.isImageUrl(imageUrl)) {
            images.push(imageUrl);
          }
        } catch (error) {
          this.logger.warn(`处理图片URL失败: ${src}`, error);
        }
      }
    });
    
    return [...new Set(images)]; // 去重
  }

  private isImageUrl(url: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
    const urlLower = url.toLowerCase();
    return imageExtensions.some(ext => urlLower.includes(ext));
  }
}
