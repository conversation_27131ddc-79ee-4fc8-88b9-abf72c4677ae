import { Injectable, Logger } from '@nestjs/common';
import * as axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as cheerio from 'cheerio';

export interface ImageDownloadResult {
  originalUrl: string;
  localUrl: string;
  success: boolean;
  error?: string;
}

@Injectable()
export class ImageDownloadService {
  private readonly logger = new Logger(ImageDownloadService.name);

  async downloadImagesAndUpdateContent(
    content: string,
    articleId: string,
    images: string[],
    baseUrl: string
  ): Promise<{ updatedContent: string; downloadResults: ImageDownloadResult[] }> {
    this.logger.log(`开始下载图片，文章ID: ${articleId}, 图片数量: ${images.length}`);

    // 创建存储目录
    const storageDir = this.createStorageDirectory(articleId);

    // 下载所有图片
    const downloadResults: ImageDownloadResult[] = [];
    const imageUrlMap = new Map<string, string>(); // 原始URL -> 本地URL的映射

    for (const imageUrl of images) {
      try {
        const result = await this.downloadSingleImage(imageUrl, storageDir, articleId);
        downloadResults.push(result);

        if (result.success) {
          imageUrlMap.set(result.originalUrl, result.localUrl);
        }
      } catch (error) {
        this.logger.error(`下载图片失败: ${imageUrl}`, error);
        downloadResults.push({
          originalUrl: imageUrl,
          localUrl: '',
          success: false,
          error: error.message
        });
      }
    }

    // 更新内容中的图片URL
    const updatedContent = this.updateImageUrlsInContent(content, imageUrlMap, baseUrl);

    this.logger.log(`图片下载完成，成功: ${downloadResults.filter(r => r.success).length}/${downloadResults.length}`);

    return {
      updatedContent,
      downloadResults
    };
  }

  private createStorageDirectory(articleId: string): string {
    const dateStr = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD
    const dirName = `${articleId}_${dateStr}`;
    const fullPath = path.join(process.env.FILE_PATH || './public', 'raw', 'doc-images', dirName);
    
    // 确保目录存在
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      this.logger.log(`创建目录: ${fullPath}`);
    }
    
    return fullPath;
  }

  private async downloadSingleImage(
    imageUrl: string,
    storageDir: string,
    articleId: string
  ): Promise<ImageDownloadResult> {
    try {
      this.logger.log(`下载图片: ${imageUrl}`);
      
      // 发送HTTP请求下载图片
      const response = await axios.default({
        method: 'GET',
        url: imageUrl,
        responseType: 'stream',
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      // 生成文件名
      const fileName = this.generateFileName(imageUrl);
      const filePath = path.join(storageDir, fileName);
      
      // 保存文件
      const writer = fs.createWriteStream(filePath);
      response.data.pipe(writer);
      
      await new Promise((resolve, reject) => {
        writer.on('finish', resolve);
        writer.on('error', reject);
      });
      
      // 生成本地访问URL
      const dateStr = new Date().toISOString().slice(0, 10).replace(/-/g, '');
      const dirName = `${articleId}_${dateStr}`;
      const localUrl = `/raw/doc-images/${dirName}/${fileName}`;
      
      this.logger.log(`图片下载成功: ${imageUrl} -> ${localUrl}`);
      
      return {
        originalUrl: imageUrl,
        localUrl,
        success: true
      };
      
    } catch (error) {
      this.logger.error(`下载图片失败: ${imageUrl}`, error);
      return {
        originalUrl: imageUrl,
        localUrl: '',
        success: false,
        error: error.message
      };
    }
  }

  private generateFileName(imageUrl: string): string {
    try {
      const url = new URL(imageUrl);
      const pathname = url.pathname;
      const extension = path.extname(pathname) || '.jpg';
      const baseName = path.basename(pathname, extension) || 'image';
      
      // 生成唯一文件名
      const timestamp = Date.now();
      const random = Math.round(Math.random() * 1e9);
      
      return `${baseName}_${timestamp}_${random}${extension}`;
    } catch (error) {
      // 如果URL解析失败，使用默认命名
      const timestamp = Date.now();
      const random = Math.round(Math.random() * 1e9);
      return `image_${timestamp}_${random}.jpg`;
    }
  }

  private updateImageUrlsInContent(content: string, imageUrlMap: Map<string, string>, baseUrl: string): string {
    if (!content || imageUrlMap.size === 0) {
      return content;
    }

    try {
      const $ = cheerio.load(content);
      const baseUrlObj = new URL(baseUrl);

      $('img').each((_, element) => {
        const $img = $(element);
        const src = $img.attr('src');

        if (src) {
          // 将相对路径转换为绝对路径，以便与imageUrlMap中的键匹配
          let absoluteUrl: string;

          if (src.startsWith('http://') || src.startsWith('https://')) {
            absoluteUrl = src;
          } else if (src.startsWith('//')) {
            absoluteUrl = baseUrlObj.protocol + src;
          } else if (src.startsWith('/')) {
            absoluteUrl = `${baseUrlObj.protocol}//${baseUrlObj.host}${src}`;
          } else {
            // 相对路径
            const currentPath = baseUrlObj.pathname.endsWith('/')
              ? baseUrlObj.pathname
              : baseUrlObj.pathname.substring(0, baseUrlObj.pathname.lastIndexOf('/') + 1);
            absoluteUrl = `${baseUrlObj.protocol}//${baseUrlObj.host}${currentPath}${src}`;
          }

          // 检查是否有对应的本地URL
          if (imageUrlMap.has(absoluteUrl)) {
            const localUrl = imageUrlMap.get(absoluteUrl);
            $img.attr('src', localUrl);
            this.logger.log(`更新图片URL: ${src} -> ${localUrl}`);
          }
        }
      });

      return $.html();
    } catch (error) {
      this.logger.error('更新内容中的图片URL失败', error);
      return content;
    }
  }

  // 验证图片URL是否有效
  async validateImageUrl(imageUrl: string): Promise<boolean> {
    try {
      const response = await axios.default({
        method: 'HEAD',
        url: imageUrl,
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const contentType = response.headers['content-type'];
      return contentType && contentType.startsWith('image/');
    } catch (error) {
      return false;
    }
  }

  // 清理失败的下载文件
  cleanupFailedDownloads(storageDir: string): void {
    try {
      if (fs.existsSync(storageDir)) {
        const files = fs.readdirSync(storageDir);
        for (const file of files) {
          const filePath = path.join(storageDir, file);
          const stats = fs.statSync(filePath);
          
          // 删除空文件或小于1KB的文件
          if (stats.size < 1024) {
            fs.unlinkSync(filePath);
            this.logger.log(`清理失败的下载文件: ${filePath}`);
          }
        }
      }
    } catch (error) {
      this.logger.error('清理失败的下载文件时出错', error);
    }
  }
}
