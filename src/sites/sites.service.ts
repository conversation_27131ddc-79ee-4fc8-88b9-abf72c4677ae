import { Injectable } from '@nestjs/common';
import { CreateSiteDto } from './dto/create-site.dto';
import { UpdateSiteDto } from './dto/update-site.dto';
import { DatabaseService } from 'src/database/database.service';
import { ObjectId } from 'mongodb';
import { PageManageService } from '../page-manage/page-manage.service';
const COLLECTION_NAME = 't_sites';
@Injectable()
export class SitesService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly pageManageService: PageManageService,
  ) { }
  async create(createSiteDto: CreateSiteDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = {
      created_at: new Date().getTime(),
      updated_at: new Date().getTime(),
      ...createSiteDto,
    };
    const result = await collection.insertOne(data);
    return result;
  }

  async findAll() {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const result = await collection.find().toArray();
    return result;
  }

  async findOne(id: string): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = await collection.findOne({ _id: new ObjectId(id) });
    return data;
  }

  async getHomepageSite(): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const site = await collection.findOne({}, { sort: { created_at: -1 } });
    return site;
  }

  async getHomepageData(homepageId: string): Promise<any> {
    try {
      const pageData = await this.pageManageService.findOne(homepageId);
      return pageData;
    } catch (error) {
      return null;
    }
  }

  async update(updateSiteDto: UpdateSiteDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const { _id, ...data } = updateSiteDto;
    const objectId = new ObjectId(_id);
    const result = await collection.updateOne({ _id: objectId }, { $set: { ...data, updated_at: new Date().getTime() } });
    return result;
  }

  async delete(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = await collection.deleteOne({ _id: new ObjectId(id) });
    return data;
  }

  async search(keyword: string) {
    // 获取数据库集合
    const pageCollection = this.databaseService.getCollection('t_page_manage');
    const newsCollection = this.databaseService.getCollection('t_news');

    // 如果keyword为空白，则返回最新的5条产品数据
    if (!keyword || keyword.trim() === '') {
      const pageFilter = { pageType: 'product' };
      const pages = await pageCollection
        .find(pageFilter)
        .sort({ created_at: -1 }) // 按创建时间倒序排列
        .limit(5)
        .toArray();

      // 处理页面数据
      const productPages = pages.map((page:any) => ({
        type: 'product',
        name: page.name || '',
        title: page.title || page.label || '',
        subTitle: page.subTitle || '',
        poster: page.poster || '',
        created_at: page.created_at || '',
        path: '/page/'+page._id || ''
      }));

      return productPages;
    }

    // 搜索产品类型的页面（只搜索类型为产品的页面）
    const pageFilter = { 
      name: { $regex: keyword, $options: 'i' },
      pageType: 'product'
    };
    
    // 搜索新闻
    const newsFilter = { 
      title: { $regex: keyword, $options: 'i' }
    };

    // 并行执行查询
    const [pageResult, newsResult] = await Promise.all([
      pageCollection.find(pageFilter).toArray(),
      newsCollection.find(newsFilter).toArray()
    ]);

    // 处理页面数据
    const productPages = pageResult.map((page:any) => ({
      type: 'product',
      name: page.name || '',
      title: page.title || page.label || '',
      subTitle: page.subTitle || '',
      poster: page.poster || '',
      created_at: page.created_at || '',
      path: '/page/'+page._id || ''
    }));

    // 处理新闻数据
    const newsItems = newsResult.map((news:any) => ({
      type: 'news',
      name: news.title || '',
      title: news.title || '',
      subTitle: news.subTitle || '',
      poster: news.image || '',
      created_at: news.created_at || '',
      content: news.content || ''
    }));

    // 合并结果
    return [...productPages, ...newsItems];
  }
}