import { Module } from '@nestjs/common';
import { SitesService } from './sites.service';
import { SitesController } from './sites.controller';
import { DatabaseModule } from 'src/database/database.module';
import { PageManageModule } from 'src/page-manage/page-manage.module';
import { ComponentsModule } from 'src/components/components.module';

@Module({
  imports: [DatabaseModule, PageManageModule, ComponentsModule],
  controllers: [SitesController],
  providers: [SitesService],
  exports: [SitesService],
})
export class SitesModule {}