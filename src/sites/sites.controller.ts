import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { SitesService } from './sites.service';
import { CreateSiteDto } from './dto/create-site.dto';
import { UpdateSiteDto } from './dto/update-site.dto';
import { Permissions } from '../auth/decorators/permissions.decorators';
import { Public } from 'src/auth/decorators/public.decorators';
import { DatabaseService } from '../database/database.service';
import { ComponentsService } from 'src/components/components.service';
import { DataTransformUtil } from '../utils/data-transform.util';

@Permissions('sites')
@Controller('sites')
export class SitesController {
  constructor(
    private readonly sitesService: SitesService,
    private readonly componentsService: ComponentsService,
  ) {}

  @Post('create')
  async create(@Body() createSiteDto: CreateSiteDto) {
    let result = await this.sitesService.create(createSiteDto);
    return {
      code: 0,
      data: result,
      msg: '创建成功',
    }
  }

  @Get('list')
  async findAll() {
    let result = await this.sitesService.findAll();
    return {
      code: 0,
      data: result,
      msg: '查询成功',
    }
  }

  @Public()
  @Get()
  async findFirst() {
    let result = await this.sitesService.findAll();
    return {
      code: 0,
      data: result[0],
      msg: '查询成功',
    }
  }

  @Public()
  @Get('homepage')
  async getHomepage() {
    const site = await this.sitesService.getHomepageSite();
    if (!site || !site.homepageId) {
      return {
        code: 1,
        data: null,
        msg: '未设置首页',
      };
    }

    const homepageData = await this.sitesService.getHomepageData(site.homepageId);
    if (!homepageData) {
      return {
        code: 1,
        data: null,
        msg: '首页数据不存在',
      };
    }

    // 格式化首页数据，参考页面管理接口
    let componentList = [];
    if (homepageData.componentList && Array.isArray(homepageData.componentList)) {
      await Promise.all(homepageData.componentList.map(async item => {
        if (item.isGlobal) {
          let globalComponentData = await this.componentsService.findOne(item._id);
          if(globalComponentData){
            item = globalComponentData;
          }else{
            item.globalNull = true;
          }
        }
        let data = DataTransformUtil.listToObj(item.elements?.children || []);
        componentList.push({
          name: item.name,
          globalNull: item.globalNull,
          props: {
            data: data
          }
        });
      }))
    }

    const data = {
      componentList: componentList,
      name: site.title, // 使用站点标题而不是页面标题
    }

    return {
      code: 0,
      data: data,
      msg: '获取成功',
    };
  }

  @Post('update')
  async update(@Body() updateSiteDto: UpdateSiteDto) {
    let updateResult = await this.sitesService.update(updateSiteDto);
    let result = null;
    //如果更新失败,改用新增
    if (updateResult.modifiedCount !== 1) {
      result = await this.sitesService.create(updateSiteDto);
    }else{
      result = updateResult;
    }
    return {
      code: 0,
      data: result,
      msg: '更新成功',
    }
  }

  @Public()
  @Get('search')
  async search(@Query('keyword') keyword: string) {
    const result = await this.sitesService.search(keyword);

    return {
      code: 0,
      data: result,
      msg: '查询成功'
    };
  }
}