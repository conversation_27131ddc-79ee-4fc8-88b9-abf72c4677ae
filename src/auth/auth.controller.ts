import { Controller, Post, Body, UseGuards, Request, Response, Get } from '@nestjs/common';
import { AuthService } from './auth.service';
import { Public } from './decorators/public.decorators';
import { LocalAuthGuard } from './local-auth.guard';
import { UsersService } from 'src/users/users.service';

@Controller('auth')
export class AuthController {
    constructor(private authService: AuthService, private usersService: UsersService) { }

    @Public()
    @UseGuards(LocalAuthGuard)
    @Post('login')
    async signIn(@Request() req, @Response() res) {
        const token = await this.authService.login(req.user);
        const remember = req.body.remember;
        let cookieOptions = {
            httpOnly: true, // 前端无法通过 JavaScript 访问
            secure: process.env.NODE_ENV === 'production', // 仅在 HTTPS 下生效
            sameSite: 'strict', // 防止 CSRF 攻击
        };
        if(remember){
            cookieOptions['maxAge'] = 365 * 24 * 60 * 60 * 1000;
        }
        res.cookie('jwt', token.access_token, cookieOptions);
        return res.send({
            code: 0,
            data: token,
            msg: '登录成功'
        })
    }

    @Get('profile')
    async getProfile(@Request() req) {
        const user: any = await this.usersService.findOne(req.user.username)
        //去除密码再返回
        if (user) {
            delete user.password;
        }
        return user;
    }

    @Public()
    @Post('logout')
    logout() {
        return 'logout';
    }

    @Get('permissions')
    getPermissions() {
        return this.authService.getPermissionsList();
    }
}
