import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from 'src/users/users.service';
import * as bcrypt from 'bcrypt';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { User } from 'src/users/entities/user.entity';
import { Permissions } from './permissions-list'
@Injectable()
export class AuthService {
    constructor(private usersService: UsersService, private jwtService: JwtService) { }

    async validateUser(username: string, pass: string): Promise<any> {
        const user: User = await this.usersService.findOne(username);

        if (user) {
            const isMatch = await bcrypt.compare(pass, user.password);
            if (!isMatch) {
                return null;
            }
            const { password, ...result } = user;
            return result;
        }
        return null;
    }

    async login(user: any) {
        const payload: JwtPayload = { sub: user._id, username: user.username, permissions: user.permissions };
        return {
            access_token: this.jwtService.sign(payload),
        };
    }

    getPermissionsList(){
        return Permissions;
    }
}
