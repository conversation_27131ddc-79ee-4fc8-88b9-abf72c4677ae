import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from './decorators/permissions.decorators'
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { IS_PUBLIC_KEY } from './decorators/public.decorators';

@Injectable()
export class PermissionsGuard implements CanActivate {
    constructor(private reflector: Reflector, private usersService: UsersService) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        if (isPublic) {
            return true;
        }
        const controllersRequiredPermissions = this.reflector.get<string[]>(
            PERMISSIONS_KEY,
            context.getClass(),
        );
        const methodsRequiredPermissions = this.reflector.get<string[]>(
            PERMISSIONS_KEY,
            context.getHandler(),
        );
        const requiredPermissions = methodsRequiredPermissions || controllersRequiredPermissions;
        if (!requiredPermissions) {
            return true; // 如果没有权限要求，允许访问
        }

        const request = context.switchToHttp().getRequest();
        // const user: User = request.user; // 从请求中获取用户信息
        const user: any = await this.usersService.findOne(request.user.username)
        const result = requiredPermissions.every((permission) =>
            user.permissions.includes(permission),
        );
        if (!result) {
            throw new UnauthorizedException({ message: '权限不足', statusCode: 402 });
        }
        return result;
    }
}