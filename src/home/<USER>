import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { HomeService } from './home.service';
import { CreateHomeDto } from './dto/create-home.dto';
import { Permissions } from '../auth/decorators/permissions.decorators';

@Controller('home')
@Permissions('home')
export class HomeController {
  constructor(private readonly homeService: HomeService) { }

  @Post('create')
  async create(@Body() createHomeDto: CreateHomeDto) {
    return this.homeService.create(createHomeDto);
  }

  @Get('list')
  async findAll(@Query() query: any) {
    const result = await this.homeService.findAll(query);
    return {
      code: 0,
      data: result,
      msg: '查询成功',
    }
  }

  @Post('update')
  async update(@Body() UpdateHomeDto) {
    const { _id, ...data } = UpdateHomeDto;
    // return this.homeService.update(_id, data);
    try {
      const result = await this.homeService.update(_id, data);
      if (result.modifiedCount === 1) {
        return {
          code: 0,
          msg: '更新成功',
          data: {
            modifiedCount: result.modifiedCount
          }
        };
      } else {
        throw new Error('更新失败');
      }
    } catch (error) {
      return {
        code: 400,
        msg: error.message || '更新失败'
      };
    }
  }

  @Post('startHome')
  async startHome(@Body() UpdateHomeDto) {
    const { _id, ...data } = UpdateHomeDto;
    try {
      const result = await this.homeService.startHome(_id);
      if (result.modifiedCount === 1) {
        return {
          code: 0,
          msg: '更新成功',
          data: {
            modifiedCount: result.modifiedCount
          }
        };
      } else {
        throw new Error('更新失败');
      }
    } catch (error) {
      return {
        code: 400,
        msg: error.message || '更新失败'
      };
    }
  }
  

  @Post('delete')
  async delete(@Body('id') id: string) {
    const result = await this.homeService.delete(id);
    if (result.deletedCount === 1) {
      return {
        code: 0,
        msg: '删除成功',
        data: {
          deletedCount: result.deletedCount
        }
      };
    } else {
      throw new Error('删除失败');
    }
  }
}