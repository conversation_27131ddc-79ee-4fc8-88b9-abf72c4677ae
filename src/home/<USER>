import { Injectable } from '@nestjs/common';
import { Home } from './entities/home.entity';
import { CreateHomeDto } from './dto/create-home.dto';
import { UpdateHomeDto } from './dto/update-home.dto';
import { DatabaseService } from 'src/database/database.service';
import { ObjectId } from 'mongodb';
import { STATUS } from 'src/const';
const COLLECTION_NAME = 't_home';
@Injectable()
export class HomeService {
  constructor(private readonly databaseService: DatabaseService) { }

  async create(createHomeDto: CreateHomeDto): Promise<Home> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = {
      created_at: new Date().getTime(),
      updated_at: new Date().getTime(),
      status: STATUS.Inactive,
      ...createHomeDto,
    };
    const result = await collection.insertOne(data);
    return result;
  }

  async findAll(query: any): Promise<{ list: Home[], total: number, ps: number, pn: number }> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const pn = parseInt(query.pn, 10) || 1;
    const ps = parseInt(query.ps, 10) || 10;
    const skip = (pn - 1) * ps;
    const filter = {};
    if (query.homeName) {
      filter['homeName'] = { $regex: query.homeName };
    }
    const list = await collection.find(filter).skip(skip).limit(ps).toArray();
    const total = await collection.countDocuments(filter);
    return {
      list: list,
      total,
      ps: ps,
      pn: pn,
    };
  }

  async findOne(id: string): Promise<Home> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    return await collection.findOne({ _id: objectId });
  }

  async update(id: string, updateHomeDto: UpdateHomeDto){
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    const result = await collection.updateOne({ _id: objectId }, { $set: updateHomeDto });
    return result;
  }

  async startHome(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    await collection.updateMany({ status: STATUS.Active }, { $set: { status: STATUS.Inactive } });
    const result = await collection.updateOne({ _id: objectId }, { $set: {status: STATUS.Active} });
    return result;
  }

  async delete(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id); // 将 id 转换为 ObjectId
    const result = await collection.deleteOne({ _id: objectId });
    return result;
  }
  
}