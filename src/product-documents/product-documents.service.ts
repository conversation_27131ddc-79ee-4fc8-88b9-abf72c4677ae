import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { ProductSeriesService } from './product-series.service';
import { CreateProductDocumentDto } from './dto/create-product-document.dto';
import { UpdateProductDocumentDto } from './dto/update-product-document.dto';
import { ProductDocument, FileInfo } from './entities/product-document.entity';
import { ObjectId } from 'mongodb';

const COLLECTION_NAME = 't_product_documents';

@Injectable()
export class ProductDocumentsService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly productSeriesService: ProductSeriesService,
  ) {}

  async create(
    createProductDocumentDto: CreateProductDocumentDto,
    documentFile: Express.Multer.File,
    modelFile: Express.Multer.File,
  ): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);

    // 验证系列是否存在
    const series = await this.productSeriesService.findOne(createProductDocumentDto.seriesId);
    if (!series) {
      throw new NotFoundException('指定的系列不存在');
    }

    // 检查产品名称在该系列下是否已存在
    const existingProduct = await collection.findOne({
      name: createProductDocumentDto.name,
      seriesId: createProductDocumentDto.seriesId,
    });
    if (existingProduct) {
      throw new BadRequestException('该系列下已存在同名产品');
    }

    const documentFileInfo: FileInfo = {
      name: documentFile.filename,
      originalName: documentFile.originalname,
      url: `/raw/product-documents/${documentFile.filename}`,
      size: documentFile.size,
    };

    const modelFileInfo: FileInfo = {
      name: modelFile.filename,
      originalName: modelFile.originalname,
      url: `/raw/product-documents/${modelFile.filename}`,
      size: modelFile.size,
    };

    const newProduct: ProductDocument = {
      ...createProductDocumentDto,
      seriesName: series.name,
      documentFile: documentFileInfo,
      modelFile: modelFileInfo,
      downloadCount: 0,
      created_at: Date.now(),
      updated_at: Date.now(),
    };

    return await collection.insertOne(newProduct);
  }

  async findAll(
    pn: number = 1,
    ps: number = 10,
    name?: string,
    seriesId?: string,
  ): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);

    const query: any = {};
    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }
    if (seriesId) {
      query.seriesId = seriesId;
    }

    const skip = (pn - 1) * ps;
    const list = await collection.find(query)
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(ps)
      .toArray() as any[];

    const total = await collection.countDocuments(query);

    return {
      list,
      total,
      ps,
      pn,
    };
  }

  async findOne(id: string): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    const product = await collection.findOne({ _id: objectId });

    if (!product) {
      throw new NotFoundException('产品文档不存在');
    }

    return product;
  }

  async update(id: string, updateProductDocumentDto: UpdateProductDocumentDto): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);

    const updateData: any = {
      ...updateProductDocumentDto,
      updated_at: Date.now(),
    };

    // 如果更新了系列，需要更新系列名称
    if (updateProductDocumentDto.seriesId) {
      const series = await this.productSeriesService.findOne(updateProductDocumentDto.seriesId);
      updateData.seriesName = series.name;
    }

    return await collection.updateOne(
      { _id: objectId },
      { $set: updateData }
    );
  }

  async remove(id: string): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    return await collection.deleteOne({ _id: objectId });
  }

  async incrementDownloadCount(id: string, fileType: 'document' | 'model'): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    
    return await collection.updateOne(
      { _id: objectId },
      { $inc: { downloadCount: 1 } }
    );
  }

  async getStatistics(): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);

    const totalProducts = await collection.countDocuments();
    const totalDownloads = await collection.aggregate([
      { $group: { _id: null, total: { $sum: '$downloadCount' } } }
    ]).toArray();

    // 按系列统计
    const seriesStats = await collection.aggregate([
      {
        $group: {
          _id: '$seriesId',
          seriesName: { $first: '$seriesName' },
          count: { $sum: 1 },
          downloads: { $sum: '$downloadCount' }
        }
      },
      { $sort: { count: -1 } }
    ]).toArray();

    return {
      totalProducts,
      totalDownloads: totalDownloads[0]?.total || 0,
      seriesStats,
    };
  }

  // 公开API - 无需认证
  async findAllPublic(seriesId?: string): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);

    const query: any = {};
    if (seriesId) {
      query.seriesId = seriesId;
    }

    const list = await collection.find(query, {
      projection: {
        _id: 1,
        name: 1,
        seriesId: 1,
        seriesName: 1,
        documentFile: 1,
        modelFile: 1,
        downloadCount: 1,
        description: 1,
        created_at: 1,
      }
    }).sort({ created_at: -1 }).toArray() as any[];

    return list;
  }
}
