import { Module } from '@nestjs/common';
import { ProductDocumentsService } from './product-documents.service';
import { ProductSeriesService } from './product-series.service';
import { ProductDocumentsController } from './product-documents.controller';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [DatabaseModule],
  controllers: [ProductDocumentsController],
  providers: [ProductDocumentsService,ProductSeriesService],
  exports: [ProductDocumentsService,ProductSeriesService],
})
export class ProductDocumentsModule {}
