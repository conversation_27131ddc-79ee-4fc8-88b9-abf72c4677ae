import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFiles,
  HttpException,
  HttpStatus,
  Res,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ProductDocumentsService } from './product-documents.service';
import { CreateProductDocumentDto } from './dto/create-product-document.dto';
import { UpdateProductDocumentDto } from './dto/update-product-document.dto';
import { Permissions } from '../auth/decorators/permissions.decorators';
import { Public } from '../auth/decorators/public.decorators';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { Response } from 'express';
// 引入产品系列相关的 DTO
import { CreateProductSeriesDto } from './dto/create-product-series.dto';
import { UpdateProductSeriesDto } from './dto/update-product-series.dto';
// 引入产品系列服务
import { ProductSeriesService } from './product-series.service';

@Controller('product-documents')
export class ProductDocumentsController {
  constructor(
    private readonly productDocumentsService: ProductDocumentsService,
    // 注入产品系列服务
    private readonly productSeriesService: ProductSeriesService,
  ) {}

  // 上传产品文档（需要同时上传文档文件和模型文件）
  @Post('upload')
  @Permissions('product-documents')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'documentFile', maxCount: 1 },
      { name: 'modelFile', maxCount: 1 },
    ], {
      storage: diskStorage({
        destination: process.env.FILE_PATH + '/raw/product-documents',
        filename: (req, file, callback) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
          const ext = extname(file.originalname);
          const filename = `${uniqueSuffix}${ext}`;
          callback(null, filename);
        },
      }),
      fileFilter: (req, file, callback) => {
        // 允许的文件类型
        const allowedTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'text/plain',
          'application/zip',
          'application/x-rar-compressed',
          'application/octet-stream',
          'model/obj',
          'model/stl',
          'model/3mf',
        ];

        if (allowedTypes.includes(file.mimetype) || file.originalname.match(/\.(dwg|step|stp|iges|igs|obj|stl|3mf|zip|rar)$/i)) {
          callback(null, true);
        } else {
          callback(new Error('不支持的文件类型'), false);
        }
      },
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB
      },
    }),
  )
  async upload(
    @UploadedFiles() files: { documentFile?: Express.Multer.File[], modelFile?: Express.Multer.File[] },
    @Body() createProductDocumentDto: CreateProductDocumentDto,
  ) {
    try {
      if (!files.documentFile || !files.modelFile) {
        throw new Error('必须同时上传文档文件和模型文件');
      }

      const documentFile = files.documentFile[0];
      const modelFile = files.modelFile[0];

      const result = await this.productDocumentsService.create(
        createProductDocumentDto,
        documentFile,
        modelFile,
      );

      if (result.acknowledged && result.insertedId) {
        return {
          code: 0,
          msg: '上传成功',
          data: { insertedId: result.insertedId }
        };
      } else {
        return {
          code: 1,
          msg: '上传失败'
        };
      }
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '上传失败'
      }, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('list')
  @Permissions('product-documents')
  async findAll(
    @Query('pn') pn: string = '1',
    @Query('ps') ps: string = '10',
    @Query('name') name?: string,
    @Query('seriesId') seriesId?: string,
  ) {
    try {
      const pageIndex = parseInt(pn) || 1;
      const pageSize = parseInt(ps) || 10;
      const result = await this.productDocumentsService.findAll(pageIndex, pageSize, name, seriesId);
      return {
        code: 0,
        data: result,
        msg: '查询成功'
      };
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '查询失败'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('statistics/overview')
  @Permissions('product-documents')
  async getStatistics() {
    try {
      const result = await this.productDocumentsService.getStatistics();
      return {
        code: 0,
        data: result,
        msg: '查询成功'
      };
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '查询失败'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get(':id')
  @Permissions('product-documents')
  async findOne(@Param('id') id: string) {
    try {
      const result = await this.productDocumentsService.findOne(id);
      return {
        code: 0,
        data: result,
        msg: '查询成功'
      };
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '查询失败'
      }, HttpStatus.NOT_FOUND);
    }
  }

  @Patch(':id')
  @Permissions('product-documents')
  async update(@Param('id') id: string, @Body() updateProductDocumentDto: UpdateProductDocumentDto) {
    try {
      const result = await this.productDocumentsService.update(id, updateProductDocumentDto);
      if (result.modifiedCount === 1) {
        return {
          code: 0,
          msg: '更新成功'
        };
      } else {
        return {
          code: 1,
          msg: '更新失败'
        };
      }
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '更新失败'
      }, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete(':id')
  @Permissions('product-documents')
  async remove(@Param('id') id: string) {
    try {
      const result = await this.productDocumentsService.remove(id);
      if (result.deletedCount === 1) {
        return {
          code: 0,
          msg: '删除成功'
        };
      } else {
        return {
          code: 1,
          msg: '删除失败'
        };
      }
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '删除失败'
      }, HttpStatus.BAD_REQUEST);
    }
  }

  // 下载文件
  @Get('download/:id/:fileType')
  @Permissions('product-documents')
  async downloadFile(
    @Param('id') id: string,
    @Param('fileType') fileType: 'document' | 'model',
    @Res() res: Response,
  ) {
    try {
      const product = await this.productDocumentsService.findOne(id);
      const fileInfo = fileType === 'document' ? product.documentFile : product.modelFile;
      
      const filePath = join(process.env.FILE_PATH, fileInfo.url);
      
      // 增加下载次数
      await this.productDocumentsService.incrementDownloadCount(id, fileType);
      
      // 对文件名进行编码处理，解决中文文件名乱码问题
      const encodedFileName = encodeURIComponent(fileInfo.originalName);
      res.header('Content-Disposition', `attachment; filename*=UTF-8''${encodedFileName}`);
      res.download(filePath, fileInfo.originalName);
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '下载失败'
      }, HttpStatus.NOT_FOUND);
    }
  }

  // 公开API - 获取产品文档列表（无需认证）
  @Get('public/list')
  @Public()
  async findAllPublic(@Query('seriesId') seriesId?: string) {
    try {
      const result = await this.productDocumentsService.findAllPublic(seriesId);
      return {
        code: 0,
        data: result,
        msg: '查询成功'
      };
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '查询失败'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 产品系列管理相关接口
  @Post('series/create')
  @Permissions('product-documents')
  async createSeries(@Body() createProductSeriesDto: CreateProductSeriesDto) {
    try {
      const result = await this.productSeriesService.create(createProductSeriesDto);
      if (result.acknowledged && result.insertedId) {
        return {
          code: 0,
          msg: '创建成功',
          data: { insertedId: result.insertedId }
        };
      } else {
        return {
          code: 1,
          msg: '创建失败'
        };
      }
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '创建失败'
      }, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('series/list')
  @Permissions('product-documents')
  async findAllSeries(
    @Query('pn') pn: string = '1',
    @Query('ps') ps: string = '10',
    @Query('name') name?: string,
  ) {
    try {
      const pageIndex = parseInt(pn) || 1;
      const pageSize = parseInt(ps) || 10;
      const result = await this.productSeriesService.findAll(pageIndex, pageSize, name);
      return {
        code: 0,
        data: result,
        msg: '查询成功'
      };
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '查询失败'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('series/simple')
  @Permissions('product-documents')
  async findAllSimpleSeries() {
    try {
      const result = await this.productSeriesService.findAllSimple();
      return {
        code: 0,
        data: result,
        msg: '查询成功'
      };
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '查询失败'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('series/:id')
  @Permissions('product-documents')
  async findOneSeries(@Param('id') id: string) {
    try {
      const result = await this.productSeriesService.findOne(id);
      return {
        code: 0,
        data: result,
        msg: '查询成功'
      };
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '查询失败'
      }, HttpStatus.NOT_FOUND);
    }
  }

  @Patch('series/:id')
  @Permissions('product-documents')
  async updateSeries(@Param('id') id: string, @Body() updateProductSeriesDto: UpdateProductSeriesDto) {
    try {
      const result = await this.productSeriesService.update(id, updateProductSeriesDto);
      if (result.modifiedCount === 1) {
        return {
          code: 0,
          msg: '更新成功'
        };
      } else {
        return {
          code: 1,
          msg: '更新失败'
        };
      }
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '更新失败'
      }, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete('series/:id')
  @Permissions('product-documents')
  async removeSeries(@Param('id') id: string) {
    try {
      const result = await this.productSeriesService.remove(id);
      if (result.deletedCount === 1) {
        return {
          code: 0,
          msg: '删除成功'
        };
      } else {
        return {
          code: 1,
          msg: '删除失败'
        };
      }
    } catch (error) {
      throw new HttpException({
        code: 1,
        msg: error.message || '删除失败'
      }, HttpStatus.BAD_REQUEST);
    }
  }
}