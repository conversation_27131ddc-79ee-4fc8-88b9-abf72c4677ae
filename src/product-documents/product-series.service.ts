import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CreateProductSeriesDto } from './dto/create-product-series.dto';
import { UpdateProductSeriesDto } from './dto/update-product-series.dto';
import { ProductSeries } from './entities/product-series.entity';
import { ObjectId } from 'mongodb';

const COLLECTION_NAME = 't_product_series';

@Injectable()
export class ProductSeriesService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(createProductSeriesDto: CreateProductSeriesDto): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    
    // 检查系列名称是否已存在
    const existingSeries = await collection.findOne({ name: createProductSeriesDto.name });
    if (existingSeries) {
      throw new ConflictException('系列名称已存在');
    }

    const newSeries: ProductSeries = {
      ...createProductSeriesDto,
      created_at: Date.now(),
      updated_at: Date.now(),
    };

    return await collection.insertOne(newSeries);
  }

  async findAll(pn: number = 1, ps: number = 10, name?: string): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    
    const query: any = {};
    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }

    const skip = (pn - 1) * ps;
    const list = await collection.find(query)
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(ps)
      .toArray() as any[];

    const total = await collection.countDocuments(query);

    return {
      list,
      total,
      ps,
      pn,
    };
  }

  async findOne(id: string): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    const series = await collection.findOne({ _id: objectId });
    
    if (!series) {
      throw new NotFoundException('系列不存在');
    }
    
    return series;
  }

  async update(id: string, updateProductSeriesDto: UpdateProductSeriesDto): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);

    // 如果更新名称，检查是否与其他系列重复
    if (updateProductSeriesDto.name) {
      const existingSeries = await collection.findOne({ 
        name: updateProductSeriesDto.name,
        _id: { $ne: objectId }
      });
      if (existingSeries) {
        throw new ConflictException('系列名称已存在');
      }
    }

    const updateData = {
      ...updateProductSeriesDto,
      updated_at: Date.now(),
    };

    return await collection.updateOne(
      { _id: objectId },
      { $set: updateData }
    );
  }

  async remove(id: string): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    
    // 检查是否有产品文档使用此系列
    const productDocsCollection = this.databaseService.getCollection('t_product_documents');
    const usedInProducts = await productDocsCollection.findOne({ seriesId: id });
    
    if (usedInProducts) {
      throw new ConflictException('该系列下还有产品文档，无法删除');
    }

    return await collection.deleteOne({ _id: objectId });
  }

  // 获取所有系列的简单列表（用于下拉选择）
  async findAllSimple(): Promise<any[]> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    return await collection.find({}, { 
      projection: { _id: 1, name: 1 } 
    }).sort({ name: 1 }).toArray() as any[];
  }
}
