import { Module } from '@nestjs/common';
import { PageManageService } from './page-manage.service';
import { ComponentsService } from '../components/components.service';
import { PageManageController } from './page-manage.controller';

@Module({
  controllers: [PageManageController],
  providers: [PageManageService, ComponentsService],
  exports: [PageManageService]
})
export class PageManageModule {}
