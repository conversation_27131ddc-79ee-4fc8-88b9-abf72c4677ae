import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { PageManageService } from './page-manage.service';
import { CreatePageManageDto } from './dto/create-page-manage.dto';
import { UpdatePageManageDto } from './dto/update-page-manage.dto';
import { Permissions } from '../auth/decorators/permissions.decorators';
import { Public } from 'src/auth/decorators/public.decorators';
import { LocalAuthGuard } from 'src/auth/local-auth.guard';
import { ComponentsService } from 'src/components/components.service';
import { DataTransformUtil } from '../utils/data-transform.util';

@Controller('page-manage')
@Permissions('page-manage')
export class PageManageController {
    constructor(private readonly pageManageService: PageManageService, private readonly componentsService: ComponentsService) { }

    @Post('create')
    create(@Body() createPageManageDto: CreatePageManageDto) {
        return this.pageManageService.create(createPageManageDto);
    }

    @Get('list')
    async findAll(@Query() query: any) {
        const result = await this.pageManageService.findAll(query);
        return {
            code: 0,
            data: result,
            msg: '查询成功',
        }
    }

    @Public()
    @Get('id')
    async findOne(@Query('id') id: string) {
        const result = await this.pageManageService.findOne(id);
        let componentList = [];

        // 如果有草稿数据，优先使用草稿数据
        const pageData = result;

        await Promise.all((pageData.componentList || []).map(async item => {
            if (item.isGlobal) {
                let globalComponentData = await this.componentsService.findOne(item._id);
                if (globalComponentData) {
                    item = globalComponentData;
                } else {
                    item.globalNull = true;
                }
            }
            let data = DataTransformUtil.listToObj(item.elements?.children || []);
            componentList.push({
                name: item.name,
                globalNull: item.globalNull,
                props: {
                    data: data
                }
            });
        }))

        const data = {
            componentList: componentList,
            name: pageData.label,
            theme: pageData.theme,

        }
        return {
            code: 0,
            data: data,
            msg: '查询成功',
        }
    }

    @Post('update')
    async update(@Body() updatePageManageDto: UpdatePageManageDto) {
        try {
            const result = await this.pageManageService.update(updatePageManageDto);
            if (result.modifiedCount === 1) {
                return {
                    code: 0,
                    msg: '更新成功',
                    data: {
                        modifiedCount: result.modifiedCount
                    }
                };
            } else {
                throw new Error('更新失败');
            }
        } catch (error) {
            return {
                code: 400,
                msg: error.message || '更新失败'
            };
        }
    }

    @Post('save-draft')
    async saveDraft(@Body() body: { id: string, draftData: any }) {
        try {
            const result = await this.pageManageService.saveDraft(body.id, body.draftData);
            if (result.modifiedCount === 1) {
                return {
                    code: 0,
                    msg: '草稿保存成功，待发布',
                    data: {
                        modifiedCount: result.modifiedCount
                    }
                };
            } else {
                throw new Error('草稿保存失败');
            }
        } catch (error) {
            return {
                code: 400,
                msg: error.message || '草稿保存失败'
            };
        }
    }

    @Post('publish')
    async publishDraft(@Body('id') id: string) {
        try {
            const result = await this.pageManageService.publishDraft(id);
            if (result.modifiedCount === 1) {
                return {
                    code: 0,
                    msg: '发布成功',
                    data: {
                        modifiedCount: result.modifiedCount
                    }
                };
            } else {
                throw new Error('发布失败');
            }
        } catch (error) {
            return {
                code: 400,
                msg: error.message || '发布失败'
            };
        }
    }

    @Get('preview')
    @Public()
    async getPreviewData(@Query('id') id: string) {
        try {
            const pageData = await this.pageManageService.getPreviewData(id);
            let componentList = [];

            // 处理组件数据
            await Promise.all((pageData.componentList || []).map(async item => {
                if (item.isGlobal) {
                    let globalComponentData = await this.componentsService.findOne(item._id);
                    if (globalComponentData) {
                        item = globalComponentData;
                    } else {
                        item.globalNull = true;
                    }
                }
                let data = DataTransformUtil.listToObj(item.elements?.children || []);
                componentList.push({
                    name: item.name,
                    globalNull: item.globalNull,
                    props: {
                        data: data
                    }
                });
            }))

            const data = {
                componentList: componentList,
                name: pageData.label,
                theme: pageData.theme,
            }

            return {
                code: 0,
                data: data,
                msg: '查询成功',
            };
        } catch (error) {
            return {
                code: 400,
                msg: error.message || '获取预览数据失败'
            };
        }
    }

    @Post('delete')
    async delete(@Body('id') id: string) {
        const result = await this.pageManageService.delete(id);
        if (result.deletedCount === 1) {
            return {
                code: 0,
                msg: '删除成功',
                data: {
                    deletedCount: result.deletedCount
                }
            };
        } else {
            throw new Error('删除失败');
        }
    }
}