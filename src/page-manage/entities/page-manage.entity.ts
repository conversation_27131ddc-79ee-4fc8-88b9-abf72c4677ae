export class PageManage {
    _id?: string;
    name?: string;
    label?: string;
    title?: string;
    subTitle?: string;
    poster?: string;
    path?: string;
    pageType?: string;
    created_at?: number;
    updated_at?: number;
    componentList?: any[];
    
    // 页面主题设置：'light' 为亮色主题，'dark' 为暗色主题
    theme?: 'light' | 'dark';
    
    // 草稿相关字段
    editingData?: {
        componentList?: any[];
        name?: string;
        label?: string;
        title?: string;
        subTitle?: string;
        poster?: string;
        path?: string;
        pageType?: string;
        updated_at?: number;
        theme?: 'light' | 'dark';
    };
    hasDraft?: boolean;
}