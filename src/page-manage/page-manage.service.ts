import { Injectable } from '@nestjs/common';
import { CreatePageManageDto } from './dto/create-page-manage.dto';
import { UpdatePageManageDto } from './dto/update-page-manage.dto';
import { DatabaseService } from 'src/database/database.service';
import { ObjectId } from 'mongodb';
import { PageManage } from './entities/page-manage.entity';
const COLLECTION_NAME = 't_page_manage';
@Injectable()
export class PageManageService {
  constructor(private readonly databaseService: DatabaseService) { }
  // 组件配置相关业务逻辑
  async findAll(query: any): Promise<{ list: any[], total: number, ps: number, pn: number }> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const pn = parseInt(query.pn, 10) || 1;
    const ps = parseInt(query.ps, 10) || 10;
    const skip = (pn - 1) * ps;
    const filter = {};
    if (query.name && typeof query.name === 'string' && query.name.trim() !== '') {
      filter['name'] = { $regex: query.name, $options: 'i' };
    }
    if (query.pageType && typeof query.pageType === 'string' && query.pageType.trim() !== '') {
      filter['pageType'] = { $regex: query.pageType, $options: 'i' };
    }
    const list = await collection.find(filter).skip(skip).limit(ps).toArray();
    const total = await collection.countDocuments(filter);
    return {
      list: list,
      total,
      ps: ps,
      pn: pn,
    };
  }

  async findOne(id: string): Promise<PageManage> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const page = await collection.findOne<PageManage>({ _id: new ObjectId(id) });
    if (page && !page.componentList) {
      page.componentList = [];
    }
    return page;
  }

  async create(createPageManageDto: CreatePageManageDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const data = {
      created_at: new Date().getTime(),
      updated_at: new Date().getTime(),
      ...createPageManageDto,
    };
    const result = await collection.insertOne(data);
    return result;
  }

  async update(updatePageManageDto: UpdatePageManageDto) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const { _id, ...data } = updatePageManageDto;
    const objectId = new ObjectId(_id);
    const result = await collection.updateOne({ _id: objectId }, { $set: { ...data, updated_at: new Date().getTime() } });
    return result;
  }

  async delete(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id); // 将 id 转换为 ObjectId
    const result = await collection.deleteOne({ _id: objectId });
    return result;
  }

  // 保存草稿
  async saveDraft(id: string, draftData: any) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    
    // 准备草稿数据
    const draft = {
      ...draftData,
      updated_at: new Date().getTime()
    };
    
    const result = await collection.updateOne(
      { _id: objectId }, 
      { 
        $set: { 
          editingData: draft,
          hasDraft: true,
          updated_at: new Date().getTime()
        } 
      }
    );
    return result;
  }

  // 发布草稿
  async publishDraft(id: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    
    // 获取页面信息
    const page = await collection.findOne<PageManage>({ _id: objectId });
    
    if (!page || !page.editingData) {
      throw new Error('没有找到草稿数据');
    }
    
    // 将草稿数据发布为主数据
    const result = await collection.updateOne(
      { _id: objectId },
      {
        $set: {
          ...page.editingData,
          editingData: null,
          hasDraft: false,
          updated_at: new Date().getTime()
        }
      }
    );
    
    return result;
  }

  // 获取预览数据
  async getPreviewData(id: string): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const objectId = new ObjectId(id);
    
    // 获取页面信息
    const page = await collection.findOne<PageManage>({ _id: objectId });
    
    if (!page) {
      throw new Error('页面不存在');
    }
    
    // 如果有草稿数据，优先使用草稿数据
    const pageData = page.editingData ? page.editingData : page;
    
    return pageData;
  }
}