import * as dotenv from 'dotenv';
dotenv.config();

import * as cookieParser from 'cookie-parser';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';


const client_front_end = process.env.CLIENT_FRONT_END?.split(',');

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.use(cookieParser());
  app.enableCors({
    origin: client_front_end, // 允许的来源'http://localhost:4200', // 或使用数组指定多个来源 ['http://example.com', 'http://localhost:4200']
    methods: 'GET,POST',
    allowedHeaders: 'Content-Type, Accept',
    credentials: true,
  });
  await app.listen(process.env.PORT ?? 3200);
}
bootstrap();
