/**
 * 数据转换工具函数
 */
export class DataTransformUtil {
    /**
     * 将组件数据列表转换为对象格式
     * @param list 要转换的数组
     * @param key 键名字段，默认为 'key'
     * @param value 值字段，默认为 'value'
     * @returns 转换后的对象
     */
    static listToObj(list: any[], key = 'key', value = 'value'): any {
        const obj: any = {};
        
        if (!Array.isArray(list)) {
            return obj;
        }
        
        list.forEach(item => {
            if (!item || !item[key]) {
                return; // 跳过无效项
            }
            
            if (item.contentType && item.contentType.includes('list')) {
                // 处理列表类型
                if (item[value] && Array.isArray(item[value])) {
                    const arr: any[] = [];
                    item[value].forEach((subItemArray: any) => {
                        if (Array.isArray(subItemArray)) {
                            // 处理嵌套数组结构
                            arr.push(DataTransformUtil.listToObj(subItemArray, key, value));
                        } else {
                            // 处理单个对象
                            arr.push(DataTransformUtil.listToObj([subItemArray], key, value));
                        }
                    });
                    obj[item[key]] = arr;
                } else {
                    obj[item[key]] = [];
                }
            } else if (item.contentType && item.contentType.includes('object')) {
                // 处理对象类型
                if (item.children && Array.isArray(item.children)) {
                    // 如果有children，递归处理children
                    obj[item[key]] = DataTransformUtil.listToObj(item.children, key, value);
                } else if (item[value] !== undefined) {
                    // 如果有值，使用值
                    obj[item[key]] = item[value];
                } else {
                    // 默认空对象
                    obj[item[key]] = {};
                }
            } else {
                // 处理其他类型（text, image, video等）
                if (item.contentType && (item.contentType.includes('image') || item.contentType.includes('video'))) {
                    obj[item[key]] = {
                        url: item[value] || '',
                        type: item.contentType[0] || 'unknown',
                    };
                } else if (Array.isArray(item[value])) {
                    // 如果值是数组，递归处理
                    obj[item[key]] = DataTransformUtil.listToObj(item[value], key, value);
                } else {
                    // 普通值
                    obj[item[key]] = item[value] !== undefined ? item[value] : '';
                }
            }
        });
        
        return obj;
    }
}
