import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './auth/auth.module';
import { ProductsModule } from './products/products.module';
import { UploadFileModule } from './upload-file/upload-file.module';
import { HomeModule } from './home/<USER>';

import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { ComponentsModule } from './components/components.module';
import { PageTemplatesModule } from './page-templates/page-templates.module';
import { PageManageModule } from './page-manage/page-manage.module';
import { CustomerModule } from './customer/customer.module';
import { NewsModule } from './news/news.module';
import { SitesModule } from './sites/sites.module';
import { ProductDocumentsModule } from './product-documents/product-documents.module';

@Module({
  imports: [UsersModule,
    DatabaseModule,
    AuthModule,
    ProductsModule,
    HomeModule,
    ComponentsModule,
    PageTemplatesModule,
    UploadFileModule,
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'), // 静态文件所在的目录
      serveStaticOptions: {
        // setHeaders: (res) => {
        //   res.setHeader('Content-Disposition', 'attachment');
        // },
      },
      // serveRoot: '/agilex-admin/browser/',
    }),
    PageManageModule,
    CustomerModule,
    NewsModule,
    SitesModule,
    ProductDocumentsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
  // exports: [DatabaseService],
})
export class AppModule { }
