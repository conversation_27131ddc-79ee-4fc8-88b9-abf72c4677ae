import { Injectable } from '@nestjs/common';
import { CreatePageTemplateDto } from './dto/create-page-template.dto';
import { UpdatePageTemplateDto } from './dto/update-page-template.dto';
import { DatabaseService } from 'src/database/database.service';
import { ObjectId } from 'mongodb';
const COLLECTION_NAME = 't_page_templates';
@Injectable()
export class PageTemplatesService {
    constructor(private readonly databaseService: DatabaseService) { }
    // 组件配置相关业务逻辑
    async findAll(query: any): Promise<{ list: any[], total: number, ps: number, pn: number }> {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const pn = parseInt(query.pn, 10) || 1;
        const ps = parseInt(query.ps, 10) || 10;
        const skip = (pn - 1) * ps;
        const filter = {};
        if (query.name) {
            filter['name'] = { $regex: query.name };
        }
        const list = await collection.find(filter).skip(skip).limit(ps).toArray();
        const total = await collection.countDocuments(filter);
        return {
            list: list,
            total,
            ps: ps,
            pn: pn,
        };
    }

    async create(createPageTemplateDto: CreatePageTemplateDto ) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const data = {
            created_at: new Date().getTime(),
            updated_at: new Date().getTime(),
            ...createPageTemplateDto,
        };
        const result = await collection.insertOne(data);
        return result;
    }

    async update(updatePageTemplateDto: UpdatePageTemplateDto){
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const { _id, ...data } = updatePageTemplateDto;
        const objectId = new ObjectId(_id);
        const result = await collection.updateOne({ _id: objectId }, { $set: data });
        return result;
      }
    
      async delete(id: string) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const objectId = new ObjectId(id); // 将 id 转换为 ObjectId
        const result = await collection.deleteOne({ _id: objectId });
        return result;
      }
}