import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { PageTemplatesService } from './page-templates.service';
import { CreatePageTemplateDto } from './dto/create-page-template.dto';
import { UpdatePageTemplateDto } from './dto/update-page-template.dto';
import { Permissions } from '../auth/decorators/permissions.decorators';

@Controller('page-templates')
@Permissions('page-templates')
export class PageTemplatesController {
    constructor(private readonly pageTemplatesService: PageTemplatesService) { }

    @Post('create')
    create(@Body() createPageTemplateDto: CreatePageTemplateDto) {
        return this.pageTemplatesService.create(createPageTemplateDto);
    }

    @Get('list')
    async findAll(@Query() query: any) {
        const result = await this.pageTemplatesService.findAll(query);
        return {
            code: 0,
            data: result,
            msg: '查询成功',
        }
    }

    @Post('update')
    async update(@Body() updatePageTemplateDto: UpdatePageTemplateDto) {
        try {
            const result = await this.pageTemplatesService.update(updatePageTemplateDto);
            if (result.modifiedCount === 1) {
                return {
                    code: 0,
                    msg: '更新成功',
                    data: {
                        modifiedCount: result.modifiedCount
                    }
                };
            } else {
                throw new Error('更新失败');
            }
        } catch (error) {
            return {
                code: 400,
                msg: error.message || '更新失败'
            };
        }
    }

    @Post('delete')
    async delete(@Body('id') id: string) {
        const result = await this.pageTemplatesService.delete(id);
        if (result.deletedCount === 1) {
            return {
                code: 0,
                msg: '删除成功',
                data: {
                    deletedCount: result.deletedCount
                }
            };
        } else {
            throw new Error('删除失败');
        }
    }
}