import { ConflictException, Injectable, Scope } from '@nestjs/common';
import { DatabaseService } from "../database/database.service";
import * as bcrypt from 'bcrypt';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UserStatus } from './user-status.enum';
const COLLECTION_NAME = 't_admin';

@Injectable()
export class UsersService {

  saltRounds = 10;

  constructor(private readonly databaseService: DatabaseService) {
  }
  async create(createUserData: User) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const existUser = await collection.findOne({'username': createUserData.username});
    if(existUser){
      throw new ConflictException('用户已存在');
    }

    const hashedPassword = await bcrypt.hash(createUserData.password, this.saltRounds);
    const createData:CreateUserDto = {
      username: createUserData.username,
      name: createUserData.name,
      password: hashedPassword,
      permissions: createUserData.permissions || [],
      created_at: new Date().getTime(),
      updated_at: new Date().getTime(),
      status: UserStatus.Active,
    }
    const result = await collection.insertOne(createData);
    return result;
  }

  async findAll(pageIndex:number = 1, pageSize:number = 10, username) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const query = {
      'status': {$ne: UserStatus.Deleted}
    };
    if(username){
      query['username'] = {$regex: username};
    }
    const userList:any = await collection.find(query).skip((pageIndex-1) * pageSize).limit(pageSize+0).toArray();
    const total = await collection.countDocuments(query);
    return {
      list: userList,
      total,
      ps: pageSize,
      pn:pageIndex,
    }
  }

  async findOne(username: string): Promise<any> {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const user:any = await collection.findOne({'username': username});
    return user;
  }

  async update(username: string, updateUserData: any) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    if(updateUserData.password){
      const hashedPassword = await bcrypt.hash(updateUserData.password, this.saltRounds);
      updateUserData.password = hashedPassword;
    }else{
      delete updateUserData.password;
    }
    updateUserData.updated_at = new Date().getTime();
    let result = await collection.updateOne({username: username}, {$set: updateUserData});
    return result;
  }

  async remove(username: string) {
    const collection = this.databaseService.getCollection(COLLECTION_NAME);
    const result = await collection.deleteOne({username: username});
    return result;
  }
}
