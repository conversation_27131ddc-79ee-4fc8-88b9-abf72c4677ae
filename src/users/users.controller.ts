import { Controller, Get, Post, Body, Request, Patch, Param, Delete, Query, UseGuards, HttpException, HttpStatus } from '@nestjs/common';
import { UsersService } from './users.service';
import { User } from './entities/user.entity';
import { Permissions } from '../auth/decorators/permissions.decorators';

@Controller('users')
@Permissions('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) { }

  @Post('create')
  async create(@Body() createUserData: User) {
    try {
      const result = await this.usersService.create(createUserData);
      if (result.acknowledged && result.insertedId) {
        return {
          code: 0,
          msg: '创建成功',
          data: { insertedId: result.insertedId }
        };
      } else {
        throw new Error('创建失败');
      }
    } catch (error) {
      return {
        code: 400,
        msg: error.message || '创建失败'
      };
    }
  }

  @Get('list')
  async findAll(@Query() search: string) {
    const ps = Number(search['ps']);
    const pn = Number(search['pn']);
    const username = search['username'];
    const result = await this.usersService.findAll(pn, ps, username);
    if (!result) {
      return {
        code: 400,
        msg: '查询失败'
      };
    }
    if (result.list.length === 0) {
      return {
        code: 400,
        msg: '没有更多数据'
      };
    }
    for (let i = 0; i < result.list.length; i++) {
      result.list[i] = {
        ...result.list[i],
        password: '',
      };
    }
    return {
      code: 0,
      data: result,
      msg: '查询成功'
    }
  }

  @Get('find')
  async findOne(@Query('username') username: string) {
    const user = await this.usersService.findOne(username);
    if (!user) {
      return {
        code: 400,
        message: '用户不存在'
      };
    }
    return {
      ...user,
      password: '',
    };
  }

  @Post('update')
  async update(@Body() updateUserData: any) {
    const { username, oid, ...data } = updateUserData;
    try {
      const result = await this.usersService.update(username, data);
      if (result.modifiedCount === 1) {
        return {
          code: 0,
          msg: '更新成功',
          data: {
            modifiedCount: result.modifiedCount
          }
        };
      } else {
        throw new Error('更新失败');
      }
    } catch (error) {
      return {
        code: 400,
        msg: error.message || '更新失败'
      };
    }
  }

  @Post('delete')
  remove(@Body('username') username: string) {
    return this.usersService.remove(username);
  }

  @Get('info')
  getProfile(@Request() req) {
    return req.user;
  }
}