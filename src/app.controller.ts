import { <PERSON>, Get, Req, Res } from '@nestjs/common';
import { AppService } from './app.service';
import { Response } from 'express';
import { join } from 'path';
import { existsSync } from 'fs';
import { Public } from './auth/decorators/public.decorators';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Public()
  @Get('/')
  getHome(@Res() res: Response): any {
    const filePath = join(__dirname, '..', 'public','agilex-admin','browser', 'index.html');
    if (!existsSync(filePath)) {
      return res.status(404).send('index.html not found');
    }
    res.sendFile(filePath);
  }
}
