import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ComponentsService } from './components.service';
import { CreateComponentDto } from './dto/create-component.dto';
import { UpdateComponentDto } from './dto/update-component.dto';
import { Permissions } from '../auth/decorators/permissions.decorators';
import { Public } from 'src/auth/decorators/public.decorators';
import { DataTransformUtil } from '../utils/data-transform.util';
import {staticComponentList} from './staticComponentList';

@Controller('components')
@Permissions('components')
export class ComponentsController {
    constructor(private readonly componentsService: ComponentsService) { }

    @Get('list')
    async findAll(@Query() query: any) {
        const result = await this.componentsService.findAll(query);
        return {
            code: 0,
            data: result,
            msg: '查询成功',
        }

    }

    @Get('global-list')
    async findGlobalComponents(@Query() query: any) {
        const result = await this.componentsService.findGlobalComponents(query);
        return {
            code: 0,
            data: {
                ...result,
                staticComponentList: staticComponentList,
            },
            msg: '查询成功',
        }
    }

    @Get('list2')
    async list(@Query() query: any) {
        const result = await this.componentsService.list();
        return {
            code: 0,
            data: result,
            msg: '查询成功',
        }
    }


    @Public()
    @Get('component-prop-data')
    async findOne(@Query() query: any) {
        const result: any = await this.componentsService.findOne(query.id);
        let data = DataTransformUtil.listToObj(result.elements?.children || []);
        return {
            code: 0,
            data: data,
            msg: '查询成功',
        }
    }

    @Public()
    @Get('component-prop-data-by-name')
    async findComponentPropData(@Query() query: any) {
        const result = await this.componentsService.findGlobalComponents(query);
        if(result.list.length > 0){
            return {
                code: 0,
                data: DataTransformUtil.listToObj(result.list[0].elements?.children || []),
                msg: '查询成功',
            }
        }else{
            return {
                code: 0,
                data: {},
                msg: '查询成功',
            }
        }
    }

    @Post('create')
    create(@Body() CreateComponentDto: CreateComponentDto) {
        return this.componentsService.create(CreateComponentDto);
    }

    @Post('update')
    async update(@Body() UpdateComponentDto: UpdateComponentDto) {
        try {
            const result = await this.componentsService.update(UpdateComponentDto);
            if (result.modifiedCount === 1) {
                return {
                    code: 0,
                    msg: '更新成功',
                    data: {
                        modifiedCount: result.modifiedCount
                    }
                };
            } else {
                throw new Error('更新失败');
            }
        } catch (error) {
            return {
                code: 400,
                msg: error.message || '更新失败'
            };
        }
    }

    // 保存草稿
    @Post('save-draft')
    async saveDraft(@Body() body: { id: string, draftData: any }) {
        try {
            const result = await this.componentsService.saveDraft(body.id, body.draftData);
            if (result.modifiedCount === 1) {
                return {
                    code: 0,
                    msg: '草稿保存成功，待发布',
                    data: {
                        modifiedCount: result.modifiedCount
                    }
                };
            } else {
                throw new Error('草稿保存失败');
            }
        } catch (error) {
            return {
                code: 400,
                msg: error.message || '草稿保存失败'
            };
        }
    }

    // 发布草稿
    @Post('publish')
    async publishDraft(@Body('id') id: string) {
        try {
            const result = await this.componentsService.publishDraft(id);
            if (result.modifiedCount === 1) {
                return {
                    code: 0,
                    msg: '发布成功',
                    data: {
                        modifiedCount: result.modifiedCount
                    }
                };
            } else {
                throw new Error('发布失败');
            }
        } catch (error) {
            return {
                code: 400,
                msg: error.message || '发布失败'
            };
        }
    }

    // 获取预览数据
    @Get('preview')
    @Public()
    async getPreviewData(@Query('id') id: string) {
        try {
            const componentData: any = await this.componentsService.getPreviewData(id);
            let componentDataObj = DataTransformUtil.listToObj(componentData.elements?.children || []);
            let data = {
                componentList: [
                    {
                        name: componentData.name,
                        props: {
                            data: componentDataObj
                        }
                    }
                ]
            }
            return {
                code: 0,
                data: data,
                msg: '查询成功',
            };
        } catch (error) {
            return {
                code: 400,
                msg: error.message || '获取预览数据失败'
            };
        }
    }

    @Post('delete')
    async delete(@Body('id') id: string) {
        const result = await this.componentsService.delete(id);
        if (result.deletedCount === 1) {
            return {
                code: 0,
                msg: '删除成功',
                data: {
                    deletedCount: result.deletedCount
                }
            };
        } else {
            throw new Error('删除失败');
        }
    }


}