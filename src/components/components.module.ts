import { Module } from '@nestjs/common';
import { DatabaseModule } from '../database/database.module';
import { UploadFileModule } from '../upload-file/upload-file.module';
import { ComponentsService } from './components.service';
import { ComponentsController } from './components.controller';

@Module({
  imports: [DatabaseModule, UploadFileModule],
  controllers: [ComponentsController],
  providers: [ComponentsService],
  exports: [ComponentsService]
})
export class ComponentsModule {}