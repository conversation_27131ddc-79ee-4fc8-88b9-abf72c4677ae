import { Injectable } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { CreateComponentDto } from './dto/create-component.dto';
import { UpdateComponentDto } from './dto/update-component.dto';
import { List } from "./componentList"
import { ObjectId } from 'mongodb';
import { Component } from './entities/component.entity';
const COLLECTION_NAME = 't_components';

@Injectable()
export class ComponentsService {
    constructor(private readonly databaseService: DatabaseService) { }
    // 组件配置相关业务逻辑
    async findAll(query: any): Promise<{ list: any[], total: number, ps: number, pn: number }> {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const pn = parseInt(query.pn, 10) || 1;
        const ps = parseInt(query.ps, 10) || 10;
        const skip = (pn - 1) * ps;
        const filter = {};
        if (query.name) {
            filter['name'] = { $regex: query.name, $options: 'i' };
        }
        //排除全局组件,可能数据中不存在isGlobal字段
        filter['isGlobal'] = { $ne: true };
        const list = await collection.find(filter).skip(skip).limit(ps).toArray();
        const total = await collection.countDocuments(filter);
        return {
            list: list,
            total,
            ps: ps,
            pn: pn,
        };
    }

    async findGlobalComponents(query: any): Promise<{ list: any[], total: number, ps: number, pn: number }> {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const pn = parseInt(query.pn, 10) || 1;
        const ps = parseInt(query.ps, 10) || 10;
        const skip = (pn - 1) * ps;
        const filter = {};
        if (query.name) {
            filter['name'] = { $regex: query.name, $options: 'i' };
        }
        filter['isGlobal'] = true;
        const list = await collection.find(filter).skip(skip).limit(ps).toArray();
        const total = await collection.countDocuments(filter);
        return {
            list: list,
            total,
            ps: ps,
            pn: pn,
        };
    }

    async findOne(id: string): Promise<any> {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const data = await collection.findOne({ _id: new ObjectId(id) });
        return data;
    }

    async findOneByName(name: string, isGlobal: boolean = false): Promise<any> {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const data = await collection.findOne({ name: name, isGlobal: true });
        console.log(data);
        return data;
    }

    async list(): Promise<{ list: any[] }> {
        return {
            list: List
        }
    }

    async create(createComponentDto: CreateComponentDto) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const data = {
            created_at: new Date().getTime(),
            updated_at: new Date().getTime(),
            ...createComponentDto,
        };
        const result = await collection.insertOne(data);
        return result;
    }

    async update(updateComponentDto: UpdateComponentDto) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const { _id, ...data } = updateComponentDto;
        const objectId = new ObjectId(_id);
        const result = await collection.updateOne({ _id: objectId }, { $set: data });
        return result;
    }

    // 保存草稿
    async saveDraft(id: string, draftData: any) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const objectId = new ObjectId(id);
        
        // 准备草稿数据
        const draft = {
            ...draftData,
            updated_at: new Date().getTime()
        };
        
        const result = await collection.updateOne(
            { _id: objectId }, 
            { 
                $set: { 
                    editingData: draft,
                    hasDraft: true,
                    updated_at: new Date().getTime()
                } 
            }
        );
        return result;
    }

    // 发布草稿
    async publishDraft(id: string) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const objectId = new ObjectId(id);
        
        // 获取组件信息
        const component = await collection.findOne<Component>({ _id: objectId });
        
        if (!component || !component.editingData) {
            throw new Error('没有找到草稿数据');
        }
        
        // 将草稿数据发布为主数据
        const result = await collection.updateOne(
            { _id: objectId },
            {
                $set: {
                    ...component.editingData,
                    editingData: null,
                    hasDraft: false,
                    updated_at: new Date().getTime()
                }
            }
        );
        
        return result;
    }

    // 获取预览数据（优先返回草稿数据）
    async getPreviewData(id: string): Promise<any> {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const objectId = new ObjectId(id);
        
        // 获取组件信息
        const component = await collection.findOne<Component>({ _id: objectId });
        
        if (!component) {
            throw new Error('组件不存在');
        }
        
        // 如果有草稿数据，优先使用草稿数据
        const componentData = component.editingData ? component.editingData : component;
        
        return componentData;
    }

    async delete(id: string) {
        const collection = this.databaseService.getCollection(COLLECTION_NAME);
        const objectId = new ObjectId(id); // 将 id 转换为 ObjectId
        const result = await collection.deleteOne({ _id: objectId });
        return result;
    }
}