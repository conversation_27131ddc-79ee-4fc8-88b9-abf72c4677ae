// 组件属性配置文件（优化版）
// 根据各组件的Vue文件和实际用途重新设计的数据结构
// 组件属性配置文件（优化版）
// 根据各组件的Vue文件和实际用途重新设计的数据结构

interface ComponentProperty {
  name: string;
  key: string;
  contentType: string[];
  required?: boolean;
  defaultValueType?: string;
  defaultValue?: any;
  options?: string[];
  children?: ComponentProperty[];
}

interface ComponentConfig {
  _id: string;
  created_at: number;
  updated_at: number;
  name: string;
  label: string;
  image: string;
  remark: string;
  elements: ComponentProperty;
}

// 将每个组件定义为独立的对象
const Banner: ComponentConfig = {
  "_id": "Banner",
  "created_at": 1751274019263,
  "updated_at": 1751274019263,
  "name": "Banner",
  "label": "轮播横幅组件",
  "image": "/images/banner.png",
  "remark": "首页轮播横幅组件，支持图片和视频",
  "elements": {
    "name": "轮播横幅数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "轮播类型",
        "key": "type",
        "contentType": ["select"],
        "required": true,
        "defaultValue": "swiper",
        "options": ["swiper", "no-btn"]
      },
      {
        "name": "轮播图列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "副标题",
            "key": "subTitle",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "轮播素材",
            "key": "resource",
            "contentType": ["image", "video"],
            "required": true
          },
          {
            "name": "跳转链接",
            "key": "jumpLink",
            "contentType": ["text"],
            "required": false
          }
        ]
      }
    ]
  }
};

const CoreTechHighlights: ComponentConfig = {
  "_id": "CoreTechHighlights",
  "created_at": 1751340047841,
  "updated_at": 1751340047841,
  "name": "CoreTechHighlights",
  "label": "核心技术亮点组件",
  "image": "/images/core-tech-highlights.png",
  "remark": "展示核心技术亮点，支持Tab切换",
  "elements": {
    "name": "核心技术亮点数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "标签页列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "标签名",
            "key": "name",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "内容标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "内容详情",
            "key": "desc",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "内容图片",
            "key": "img",
            "contentType": ["image"],
            "required": true
          },
          {
            "name": "特征列表",
            "key": "feature",
            "contentType": ["list"],
            "required": false,
            "children": [
              {
                "name": "特征名",
                "key": "name",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "特征详情",
                "key": "desc",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "特征图标",
                "key": "icon",
                "contentType": ["image"],
                "required": false
              }
            ]
          }
        ]
      }
    ]
  }
};

const ImgCard: ComponentConfig = {
  "_id": "ImgCard",
  "created_at": 1751965588268,
  "updated_at": 1751965588268,
  "name": "ImgCard",
  "label": "图片卡片组件",
  "image": "/images/img-card.png",
  "remark": "四图片卡片组组件",
  "elements": {
    "name": "图片卡片数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "图片列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "副标题",
            "key": "subTitle",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "图片",
            "key": "img",
            "contentType": ["image"],
            "required": true
          },
          {
            "name": "跳转链接",
            "key": "jumpLink",
            "contentType": ["text"],
            "required": false
          }
        ]
      }
    ]
  }
};

const Navbar: ComponentConfig = {
  "_id": "Navbar",
  "created_at": 1751967921428,
  "updated_at": 1751967921428,
  "name": "Navbar",
  "label": "导航栏组件",
  "image": "/images/navbar.png",
  "remark": "网站导航栏，支持多级菜单",
  "elements": {
    "name": "导航栏数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "英文网址",
        "key": "enUrl",
        "contentType": ["text"],
        "required": true,
        "defaultValueType": "text",
        "defaultValue": "https://global.agilex.ai/"
      },
      {
        "name": "商城网址",
        "key": "shopUrl",
        "contentType": ["text"],
        "required": true,
        "defaultValueType": "text",
        "defaultValue": "https://shop425844649.taobao.com/?spm=pc_detail.29232929/evo365560b447259.shop_block.dentershop.445a7dd6jRPXWm"
      },
      {
        "name": "一级菜单列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "菜单名称",
            "key": "name",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "菜单路径",
            "key": "path",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "二级菜单列表",
            "key": "subMenu",
            "contentType": ["list"],
            "required": false,
            "children": [
              {
                "name": "菜单名称",
                "key": "name",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "菜单路径",
                "key": "path",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "三级菜单列表",
                "key": "children",
                "contentType": ["list"],
                "required": false,
                "children": [
                  {
                    "name": "菜单名称",
                    "key": "name",
                    "contentType": ["text"],
                    "required": true
                  },
                  {
                    "name": "菜单路径",
                    "key": "path",
                    "contentType": ["text"],
                    "required": true
                  },
                  {
                    "name": "预览图列表",
                    "key": "imgs",
                    "contentType": ["list"],
                    "required": false,
                    "children": [
                      {
                        "name": "菜单预览图",
                        "key": "img",
                        "contentType": ["image"],
                        "required": true
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

const BigBanner: ComponentConfig = {
  "_id": "BigBanner",
  "created_at": 1752220045218,
  "updated_at": 1752220045218,
  "name": "BigBanner",
  "label": "大轮播组件",
  "image": "/images/big-banner.png",
  "remark": "大图轮播组件",
  "elements": {
    "name": "大轮播数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "详细描述",
        "key": "desc",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "轮播图列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "名称",
            "key": "name",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "描述",
            "key": "subDesc",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "图或视频",
            "key": "resource",
            "contentType": ["image", "video"],
            "required": true
          }
        ]
      }
    ]
  }
};

const Banner2: ComponentConfig = {
  "_id": "Banner2",
  "created_at": 1752220706219,
  "updated_at": 1752220706219,
  "name": "Banner2",
  "label": "解决方案横幅组件",
  "image": "/images/banner2.png",
  "remark": "解决方案横幅组件",
  "elements": {
    "name": "解决方案横幅数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "资源",
        "key": "resource",
        "contentType": ["image", "video"],
        "required": true
      }
    ]
  }
};

const ImgCard2: ComponentConfig = {
  "_id": "ImgCard2",
  "created_at": 1752221326007,
  "updated_at": 1752221326007,
  "name": "ImgCard2",
  "label": "优秀案例组件",
  "image": "/images/img-card2.png",
  "remark": "优秀案例展示组件",
  "elements": {
    "name": "优秀案例数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "图文列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "副标题",
            "key": "subTitle",
            "contentType": ["text"],
            "required": false
          },
          {
            "name": "资源",
            "key": "resource",
            "contentType": ["image"],
            "required": true
          },
          {
            "name": "跳转链接",
            "key": "jumpLink",
            "contentType": ["text"],
            "required": false
          }
        ]
      }
    ]
  }
};

const CompanyPartners: ComponentConfig = {
  "_id": "CompanyPartners",
  "created_at": 1752221664280,
  "updated_at": 1752221664280,
  "name": "CompanyPartners",
  "label": "合作伙伴组件",
  "image": "/images/company-partners.png",
  "remark": "合作伙伴logo展示组件",
  "elements": {
    "name": "合作伙伴数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "合作伙伴logo列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "logo图片",
            "key": "resource",
            "contentType": ["image"],
            "required": true
          }
        ]
      }
    ]
  }
};

const Footer: ComponentConfig = {
  "_id": "Footer",
  "created_at": 1752564555749,
  "updated_at": 1752564555749,
  "name": "Footer",
  "label": "页脚组件",
  "image": "/images/footer.png",
  "remark": "网站页脚组件",
  "elements": {
    "name": "页脚数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "联系电话",
        "key": "phone",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "邮箱",
        "key": "mail",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "bilibili链接",
        "key": "bilibili",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "微博链接",
        "key": "weibo",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "抖音链接",
        "key": "tiktok",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "小红书链接",
        "key": "xhs",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "微信二维码",
        "key": "wechat",
        "contentType": ["image"],
        "required": true
      },
      {
        "name": "ICP备案信息",
        "key": "copyright",
        "contentType": ["text"],
        "required": true,
        "defaultValue": "©2022 松灵机器人(深圳)有限公司 版权所有 粤ICP备17155738号-2"
      },
      {
        "name": "页脚导航",
        "key": "footerSections",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "导航分组列表",
            "key": "subcategories",
            "contentType": ["list"],
            "required": true,
            "children": [
              {
                "name": "分组名称",
                "key": "title",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "导航项列表",
                "key": "items",
                "contentType": ["list"],
                "required": true,
                "children": [
                  {
                    "name": "链接名称",
                    "key": "title",
                    "contentType": ["text"],
                    "required": true
                  },
                  {
                    "name": "导航链接",
                    "key": "link",
                    "contentType": ["text"],
                    "required": true
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

const ContactUs: ComponentConfig = {
  "_id": "ContactUs",
  "created_at": 1752829315500,
  "updated_at": 1752829315500,
  "name": "ContactUs",
  "label": "联系我们组件",
  "image": "/images/contact-us.png",
  "remark": "联系我们表单组件",
  "elements": {
    "name": "联系我们数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "组件标题",
        "key": "title",
        "contentType": ["text"],
        "required": true,
        "defaultValue": "联系我们"
      },
      {
        "name": "电话咨询",
        "key": "phone",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "邮箱地址",
        "key": "email",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "公司地址",
        "key": "addr",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "隐私条款文件",
        "key": "privacyFile",
        "contentType": ["file"],
        "required": false
      },
      {
        "name": "使用条款文件",
        "key": "usePolicy",
        "contentType": ["file"],
        "required": false
      },
      {
        "name": "产品列表",
        "key": "productList",
        "contentType": ["list"],
        "required": false,
        "children": [
          {
            "name": "产品类型名称",
            "key": "name",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "产品系列列表",
            "key": "subTypes",
            "contentType": ["list"],
            "required": true,
            "children": [
              {
                "name": "系列名称",
                "key": "name",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "产品选项列表",
                "key": "options",
                "contentType": ["list"],
                "required": true,
                "children": [
                  {
                    "name": "产品名称",
                    "key": "name",
                    "contentType": ["text"],
                    "required": true
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

const Api: ComponentConfig = {
  "_id": "Api",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "Api",
  "label": "API文档组件",
  "image": "/images/api-component.png",
  "remark": "API文档展示组件",
  "elements": {
    "name": "API组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "描述列表",
        "key": "desc",
        "contentType": ["list"],
        "required": false,
        "children": [
          {
            "name": "描述文本",
            "key": "text",
            "contentType": ["text"],
            "required": true
          }
        ]
      },
      {
        "name": "API列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "API名称",
            "key": "name",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "API内容",
            "key": "content",
            "contentType": ["object"],
            "required": true,
            "children": [
              {
                "name": "标题",
                "key": "title",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "副标题",
                "key": "subTitle",
                "contentType": ["text"],
                "required": false
              },
              {
                "name": "描述",
                "key": "desc",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "代码示例",
                "key": "code",
                "contentType": ["text"],
                "required": true
              }
            ]
          }
        ]
      }
    ]
  }
};

const ApplicationScenarios: ComponentConfig = {
  "_id": "ApplicationScenarios",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ApplicationScenarios",
  "label": "应用场景组件",
  "image": "/images/application-scenarios.png",
  "remark": "应用场景展示组件，包含场景详情弹窗",
  "elements": {
    "name": "应用场景组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "场景列表",
        "key": "scenarios",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "场景标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "场景描述",
            "key": "description",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "场景图片",
            "key": "imageUrl",
            "contentType": ["image"],
            "required": true
          },
          {
            "name": "场景详情数据",
            "key": "scenarioDetail",
            "contentType": ["object"],
            "required": false,
            "children": [
              {
                "name": "横幅数据",
                "key": "bannerData",
                "contentType": ["object"],
                "required": true,
                "children": [
                  {
                    "name": "主标题",
                    "key": "title",
                    "contentType": ["text"],
                    "required": true
                  },
                  {
                    "name": "副标题",
                    "key": "subTitle",
                    "contentType": ["text"],
                    "required": false
                  },
                  {
                    "name": "资源",
                    "key": "resource",
                    "contentType": ["object"],
                    "required": true,
                    "children": [
                      {
                        "name": "资源类型",
                        "key": "type",
                        "contentType": ["select"],
                        "required": true,
                        "options": ["image", "video"]
                      },
                      {
                        "name": "资源数据",
                        "key": "data",
                        "contentType": ["object"],
                        "required": true,
                        "children": [
                          {
                            "name": "资源URL",
                            "key": "url",
                            "contentType": ["image", "video"],
                            "required": true
                          }
                        ]
                      }
                    ]
                  }
                ]
              },
              {
                "name": "用户痛点数据",
                "key": "painPointsData",
                "contentType": ["object"],
                "required": false,
                "children": [
                  {
                    "name": "主标题",
                    "key": "title",
                    "contentType": ["text"],
                    "required": true,
                    "defaultValue": "用户痛点"
                  },
                  {
                    "name": "痛点列表",
                    "key": "items",
                    "contentType": ["list"],
                    "required": true,
                    "children": [
                      {
                        "name": "痛点标题",
                        "key": "title",
                        "contentType": ["text"],
                        "required": true
                      },
                      {
                        "name": "痛点描述",
                        "key": "subTitle",
                        "contentType": ["text"],
                        "required": true
                      },
                      {
                        "name": "痛点图片",
                        "key": "img",
                        "contentType": ["image"],
                        "required": true
                      }
                    ]
                  }
                ]
              },
              {
                "name": "产品优势数据",
                "key": "advantagesData",
                "contentType": ["object"],
                "required": false,
                "children": [
                  {
                    "name": "主标题",
                    "key": "title",
                    "contentType": ["text"],
                    "required": true,
                    "defaultValue": "产品优势"
                  },
                  {
                    "name": "优势列表",
                    "key": "items",
                    "contentType": ["list"],
                    "required": true,
                    "children": [
                      {
                        "name": "优势标题",
                        "key": "title",
                        "contentType": ["text"],
                        "required": true
                      },
                      {
                        "name": "优势描述",
                        "key": "subTitle",
                        "contentType": ["text"],
                        "required": false
                      },
                      {
                        "name": "优势图片",
                        "key": "img",
                        "contentType": ["image"],
                        "required": true
                      }
                    ]
                  }
                ]
              },
              {
                "name": "解决方案横幅",
                "key": "solutionBanner",
                "contentType": ["object"],
                "required": false,
                "children": [
                  {
                    "name": "主标题",
                    "key": "title",
                    "contentType": ["text"],
                    "required": true,
                    "defaultValue": "解决方案"
                  },
                  {
                    "name": "副标题",
                    "key": "subTitle",
                    "contentType": ["text"],
                    "required": false
                  },
                  {
                    "name": "资源",
                    "key": "resource",
                    "contentType": ["object"],
                    "required": true,
                    "children": [
                      {
                        "name": "资源类型",
                        "key": "type",
                        "contentType": ["select"],
                        "required": true,
                        "options": ["image", "video"]
                      },
                      {
                        "name": "资源数据",
                        "key": "data",
                        "contentType": ["object"],
                        "required": true,
                        "children": [
                          {
                            "name": "资源URL",
                            "key": "url",
                            "contentType": ["image", "video"],
                            "required": true
                          }
                        ]
                      }
                    ]
                  }
                ]
              },
              {
                "name": "产品详情数据",
                "key": "productDetailData",
                "contentType": ["object"],
                "required": false,
                "children": [
                  {
                    "name": "主要产品",
                    "key": "mainProduct",
                    "contentType": ["object"],
                    "required": true,
                    "children": [
                      {
                        "name": "产品标题",
                        "key": "title",
                        "contentType": ["text"],
                        "required": true
                      },
                      {
                        "name": "产品描述",
                        "key": "desc",
                        "contentType": ["text"],
                        "required": true
                      },
                      {
                        "name": "产品图片",
                        "key": "img",
                        "contentType": ["image"],
                        "required": true
                      }
                    ]
                  },
                  {
                    "name": "相关产品列表",
                    "key": "relatedProducts",
                    "contentType": ["list"],
                    "required": false,
                    "children": [
                      {
                        "name": "产品描述",
                        "key": "desc",
                        "contentType": ["text"],
                        "required": true
                      },
                      {
                        "name": "产品图片",
                        "key": "img",
                        "contentType": ["image"],
                        "required": true
                      }
                    ]
                  }
                ]
              },
              {
                "name": "优秀案例数据",
                "key": "excellentCasesData",
                "contentType": ["object"],
                "required": false,
                "children": [
                  {
                    "name": "主标题",
                    "key": "title",
                    "contentType": ["text"],
                    "required": true,
                    "defaultValue": "优秀案例"
                  },
                  {
                    "name": "副标题",
                    "key": "subTitle",
                    "contentType": ["text"],
                    "required": false
                  },
                  {
                    "name": "案例列表",
                    "key": "list",
                    "contentType": ["list"],
                    "required": true,
                    "children": [
                      {
                        "name": "案例标题",
                        "key": "title",
                        "contentType": ["text"],
                        "required": true
                      },
                      {
                        "name": "案例描述",
                        "key": "subTitle",
                        "contentType": ["text"],
                        "required": false
                      },
                      {
                        "name": "案例图片",
                        "key": "img",
                        "contentType": ["image"],
                        "required": true
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

const BigBannerNoLoopWithPagination: ComponentConfig = {
  "_id": "BigBannerNoLoopWithPagination",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "BigBannerNoLoopWithPagination",
  "label": "大轮播组件(无循环带分页)",
  "image": "/images/big-banner-no-loop.png",
  "remark": "大轮播组件，无循环，带分页控制",
  "elements": {
    "name": "大轮播组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "描述",
        "key": "desc",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "轮播列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "资源",
            "key": "resource",
            "contentType": ["image", "video"],
            "required": true
          }
        ]
      }
    ]
  }
};

const BigBannerStickyScale: ComponentConfig = {
  "_id": "BigBannerStickyScale",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "BigBannerStickyScale",
  "label": "粘性缩放大轮播组件",
  "image": "/images/big-banner-sticky.png",
  "remark": "带粘性滚动和缩放效果的大轮播组件",
  "elements": {
    "name": "粘性轮播组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "描述",
        "key": "desc",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "轮播列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "资源",
            "key": "resource",
            "contentType": ["image", "video"],
            "required": true
          }
        ]
      }
    ]
  }
};

const BigBannerWithPagination: ComponentConfig = {
  "_id": "BigBannerWithPagination",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "BigBannerWithPagination",
  "label": "带分页大轮播组件",
  "image": "/images/big-banner-pagination.png",
  "remark": "带分页控制的大轮播组件",
  "elements": {
    "name": "分页轮播组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "描述",
        "key": "desc",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "轮播列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "名称",
            "key": "name",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "描述",
            "key": "subDesc",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "资源",
            "key": "resource",
            "contentType": ["image", "video"],
            "required": true
          }
        ]
      }
    ]
  }
};

const CoreAdvantages: ComponentConfig = {
  "_id": "CoreAdvantages",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "CoreAdvantages",
  "label": "核心优势组件",
  "image": "/images/core-advantages.png",
  "remark": "展示核心优势的卡片组件",
  "elements": {
    "name": "核心优势组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "描述",
        "key": "desc",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "顶部卡片列表",
        "key": "topCards",
        "contentType": ["list"],
        "required": false,
        "children": [
          {
            "name": "标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "描述",
            "key": "desc",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "图标",
            "key": "img",
            "contentType": ["image"],
            "required": true
          }
        ]
      },
      {
        "name": "底部卡片列表",
        "key": "bottomCards",
        "contentType": ["list"],
        "required": false,
        "children": [
          {
            "name": "标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "描述",
            "key": "desc",
            "contentType": ["text"],
            "required": true
          }
        ]
      }
    ]
  }
};

const DeveloperResources: ComponentConfig = {
  "_id": "DeveloperResources",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "DeveloperResources",
  "label": "开发者资源组件",
  "image": "/images/developer-resources.png",
  "remark": "开发者资源展示组件",
  "elements": {
    "name": "开发者资源组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "资源列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "描述",
            "key": "desc",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "图标",
            "key": "icon",
            "contentType": ["image"],
            "required": true
          },
          {
            "name": "跳转链接",
            "key": "jumpLink",
            "contentType": ["text"],
            "required": true
          }
        ]
      }
    ]
  }
};

const FileDownload: ComponentConfig = {
  "_id": "FileDownload",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "FileDownload",
  "label": "文件下载组件",
  "image": "/images/file-download.png",
  "remark": "文件下载组件，支持分类和表单提交",
  "elements": {
    "name": "文件下载组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
    ]
  }
};

const HonorImgCard: ComponentConfig = {
  "_id": "HonorImgCard",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "HonorImgCard",
  "label": "荣誉图片卡组件",
  "image": "/images/honor-img-card.png",
  "remark": "展示荣誉图片的卡片组件",
  "elements": {
    "name": "荣誉图片卡组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "图片列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "图片",
            "key": "img",
            "contentType": ["image"],
            "required": true
          }
        ]
      }
    ]
  }
};

const IndustryImgCard: ComponentConfig = {
  "_id": "IndustryImgCard",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "IndustryImgCard",
  "label": "行业图片卡组件",
  "image": "/images/industry-img-card.png",
  "remark": "行业应用图片卡片组件",
  "elements": {
    "name": "行业图片卡组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "行业主页链接",
        "key": "industryHome",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "行业列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "行业标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "缩略图",
            "key": "thumbnail",
            "contentType": ["image"],
            "required": true
          },
          {
            "name": "跳转链接",
            "key": "jumpLink",
            "contentType": ["text"],
            "required": false
          }
        ]
      }
    ]
  }
};

const IndustryTextCard: ComponentConfig = {
  "_id": "IndustryTextCard",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "IndustryTextCard",
  "label": "行业文本卡组件",
  "image": "/images/industry-text-card.png",
  "remark": "行业文本卡片组件",
  "elements": {
    "name": "行业文本卡组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "描述",
        "key": "desc",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "卡片列表",
        "key": "cards",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "描述",
            "key": "desc",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "图标",
            "key": "img",
            "contentType": ["image"],
            "required": true
          }
        ]
      }
    ]
  }
};

const Intro: ComponentConfig = {
  "_id": "Intro",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "Intro",
  "label": "介绍组件",
  "image": "/images/intro.png",
  "remark": "公司介绍组件，包含数字统计",
  "elements": {
    "name": "介绍组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "描述标题",
        "key": "descTitle",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "描述内容",
        "key": "descContent",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "数字统计项列表",
        "key": "numberItems",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "数值",
            "key": "value",
            "contentType": ["number"],
            "required": true
          },
          {
            "name": "标签",
            "key": "key",
            "contentType": ["text"],
            "required": true
          }
        ]
      }
    ]
  }
};

const ItemComparison: ComponentConfig = {
  "_id": "ItemComparison",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ItemComparison",
  "label": "物品对比组件",
  "image": "/images/item-comparison.png",
  "remark": "物品对比展示组件",
  "elements": {
    "name": "物品对比组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "对比图片列表",
        "key": "imgs",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "名称",
            "key": "name",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "图片",
            "key": "url",
            "contentType": ["image"],
            "required": true
          }
        ]
      }
    ]
  }
};

const MetricDisplayGroup: ComponentConfig = {
  "_id": "MetricDisplayGroup",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "MetricDisplayGroup",
  "label": "指标展示组件",
  "image": "/images/metric-display.png",
  "remark": "数据指标展示组件",
  "elements": {
    "name": "指标展示组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "指标列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "数值",
            "key": "value",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "标签",
            "key": "key",
            "contentType": ["text"],
            "required": true
          }
        ]
      }
    ]
  }
};

const NewsList: ComponentConfig = {
  "_id": "NewsList",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "NewsList",
  "label": "新闻列表组件",
  "image": "/images/news-list.png",
  "remark": "新闻列表展示组件，支持分类筛选",
  "elements": {
    "name": "新闻列表组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "新闻分类数据",
        "key": "newsTypes",
        "contentType": ["object"],
        "required": true,
        "children": [
          {
            "name": "分类ID列表",
            "key": "typeId",
            "contentType": ["list"],
            "required": true,
            "children": [
              {
                "name": "新闻标题",
                "key": "title",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "新闻副标题",
                "key": "subTitle",
                "contentType": ["text"],
                "required": false
              },
              {
                "name": "新闻图片",
                "key": "image",
                "contentType": ["image"],
                "required": true
              },
              {
                "name": "发布时间",
                "key": "time",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "新闻内容",
                "key": "content",
                "contentType": ["text"],
                "required": true
              }
            ]
          }
        ]
      }
    ]
  }
};

const PageTopBanner: ComponentConfig = {
  "_id": "PageTopBanner",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "PageTopBanner",
  "label": "页面顶部横幅组件",
  "image": "/images/page-top-banner.png",
  "remark": "页面顶部横幅组件，支持图片和视频",
  "elements": {
    "name": "页面顶部横幅组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "标题左对齐",
        "key": "titleLeft",
        "contentType": ["boolean"],
        "required": false
      },
      {
        "name": "资源",
        "key": "resource",
        "contentType": ["object"],
        "required": true,
        "children": [
          {
            "name": "资源类型",
            "key": "type",
            "contentType": ["select"],
            "required": true,
            "options": ["image", "video"]
          },
          {
            "name": "资源数据",
            "key": "data",
            "contentType": ["object"],
            "required": true,
            "children": [
              {
                "name": "资源URL",
                "key": "url",
                "contentType": ["image", "video"],
                "required": true
              }
            ]
          }
        ]
      }
    ]
  }
};

const ProductImgCard: ComponentConfig = {
  "_id": "ProductImgCard",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ProductImgCard",
  "label": "产品图片卡组件",
  "image": "/images/product-img-card.png",
  "remark": "产品图片卡片组件，支持图片和视频",
  "elements": {
    "name": "产品图片卡组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "产品列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "产品标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "产品副标题",
            "key": "subTitle",
            "contentType": ["text"],
            "required": false
          },
          {
            "name": "产品资源",
            "key": "resource",
            "contentType": ["object"],
            "required": true,
            "children": [
              {
                "name": "资源类型",
                "key": "type",
                "contentType": ["select"],
                "required": true,
                "options": ["image", "video"]
              },
              {
                "name": "资源数据",
                "key": "data",
                "contentType": ["object"],
                "required": true,
                "children": [
                  {
                    "name": "资源URL",
                    "key": "url",
                    "contentType": ["image", "video"],
                    "required": true
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

const ProductSpecs: ComponentConfig = {
  "_id": "ProductSpecs",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ProductSpecs",
  "label": "产品规格组件",
  "image": "/images/product-specs.png",
  "remark": "产品规格参数展示组件，支持多产品切换",
  "elements": {
    "name": "产品规格组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": '产品列表',
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "产品名称",
            "key": "productName",
            "contentType": ["text"],
            "required": true
          }, 
          {
            name: '产品文档',
            key: "productDoc",
            contentType: ["object"],
            required: true,
            children: [
              {
                name: "文档名称",
                key: 'name',
                contentType: ["text"],
                required: true,
              },
              {
                name: "文档资源",
                key: "url",
                contentType: ["file"],
                required: true,
              }
            ]
          },
          {
            "name": "规格列表",
            "key": "specs",
            "contentType": ["list"],
            "required": true,
            "children": [
              {
                "name": "规格类型",
                "key": "type",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "规格项目列表",
                "key": "items",
                "contentType": ["list"],
                "required": true,
                "children": [
                  {
                    "name": "项目类型",
                    "key": "type",
                    "contentType": ["select"],
                    "required": true,
                    "options": ["text", "image"]
                  },
                  {
                    "name": "参数名",
                    "key": "key",
                    "contentType": ["text"],
                    "required": false
                  },
                  {
                    "name": "参数值",
                    "key": "value",
                    "contentType": ["text"],
                    "required": false
                  },
                  {
                    "name": "图片URL",
                    "key": "image",
                    "contentType": ["image"],
                    "required": false
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

const ServiceSupport: ComponentConfig = {
  "_id": "ServiceSupport",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ServiceSupport",
  "label": "服务支持组件",
  "image": "/images/service-support.png",
  "remark": "服务支持展示组件",
  "elements": {
    "name": "服务支持组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "服务列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "服务标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "服务描述",
            "key": "desc",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "服务图标",
            "key": "icon",
            "contentType": ["image"],
            "required": true
          }
        ]
      }
    ]
  }
};

const Timeline: ComponentConfig = {
  "_id": "Timeline",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "Timeline",
  "label": "时间线组件",
  "image": "/images/timeline.png",
  "remark": "时间线展示组件，支持交互切换",
  "elements": {
    "name": "时间线组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "时间线列表",
        "key": "timelineList",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "时间点标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "描述内容",
            "key": "desc",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "配图",
            "key": "img",
            "contentType": ["image"],
            "required": false
          }
        ]
      }
    ]
  }
};

const ThreeDViewer: ComponentConfig = {
  "_id": "ThreeDViewer",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ThreeDViewer",
  "label": "3D查看器组件",
  "image": "/images/3d-viewer.png",
  "remark": "3D模型查看器组件",
  "elements": {
    "name": "3D查看器组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "3D模型URL",
        "key": "modelUrl",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "查看器标题",
        "key": "title",
        "contentType": ["text"],
        "required": false,
        "defaultValue": "全方位，好好看"
      },
      {
        "name": "查看器描述",
        "key": "desc",
        "contentType": ["text"],
        "required": false,
        "defaultValue": "拖动图片全方位查看"
      },
      {
        "name": "底部说明",
        "key": "bottomNote",
        "contentType": ["text"],
        "required": false,
        "defaultValue": "*3D 效果仅作示意，具体效果请以实物为准。"
      }
    ]
  }
};

const ExternalVideo: ComponentConfig = {
  "_id": "ExternalVideo",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ExternalVideo",
  "label": "外部视频组件",
  "image": "/images/external-video.png",
  "remark": "外部视频展示组件，支持视频预览和播放",
  "elements": {
    "name": "外部视频组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "视频主页链接",
        "key": "videoHome",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "视频列表",
        "key": "videos",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "视频标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "视频缩略图",
            "key": "thumbnail",
            "contentType": ["image"],
            "required": true
          },
          {
            "name": "视频URL",
            "key": "url",
            "contentType": ["video"],
            "required": true
          }
        ]
      }
    ]
  }
};

const SupportDoc: ComponentConfig = {
  "_id": "SupportDoc",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "SupportDoc",
  "label": "支持文档组件",
  "image": "/images/support-doc.png",
  "remark": "支持文档展示组件，支持标签页切换",
  "elements": {
    "name": "支持文档组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "文档列表",
        "key": "list",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "标签名称",
            "key": "name",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "文档内容",
            "key": "content",
            "contentType": ["text"],
            "required": true
          }
        ]
      }
    ]
  }
};

const CenterBanner: ComponentConfig = {
  "_id": "CenterBanner",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "CenterBanner",
  "label": "居中横幅组件",
  "image": "/images/center-banner.png",
  "remark": "居中显示的横幅组件",
  "elements": {
    "name": "居中横幅组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "背景资源",
        "key": "resource",
        "contentType": ["image", "video"],
        "required": true
      }
    ]
  }
};

const CardStack: ComponentConfig = {
  "_id": "CardStack",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "CardStack",
  "label": "卡片堆叠组件",
  "image": "/images/card-stack.png",
  "remark": "卡片堆叠展示组件",
  "elements": {
    "name": "卡片堆叠组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "卡片列表",
        "key": "cards",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "卡片标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "卡片内容",
            "key": "content",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "卡片图片",
            "key": "image",
            "contentType": ["image"],
            "required": false
          }
        ]
      }
    ]
  }
};

const AutoLoopScroll: ComponentConfig = {
  "_id": "AutoLoopScroll",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "AutoLoopScroll",
  "label": "自动循环滚动组件",
  "image": "/images/auto-loop-scroll.png",
  "remark": "支持内容自动循环滚动的组件",
  "elements": {
    "name": "自动循环滚动组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "滚动项列表",
        "key": "items",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "内容",
            "key": "content",
            "contentType": ["text"],
            "required": true
          }
        ]
      }
    ]
  }
};

const AxModal: ComponentConfig = {
  "_id": "AxModal",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "AxModal",
  "label": "模态框组件",
  "image": "/images/ax-modal.png",
  "remark": "通用模态框组件",
  "elements": {
    "name": "模态框组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "标题",
        "key": "title",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "内容",
        "key": "content",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "是否显示",
        "key": "visible",
        "contentType": ["boolean"],
        "required": false,
        "defaultValue": false
      }
    ]
  }
};

const AxSelect: ComponentConfig = {
  "_id": "AxSelect",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "AxSelect",
  "label": "选择器组件",
  "image": "/images/ax-select.png",
  "remark": "通用选择器组件",
  "elements": {
    "name": "选择器组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "选项列表",
        "key": "options",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "选项值",
            "key": "value",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "选项标签",
            "key": "label",
            "contentType": ["text"],
            "required": true
          }
        ]
      },
      {
        "name": "选中值",
        "key": "modelValue",
        "contentType": ["text"],
        "required": false
      }
    ]
  }
};

const Banner2InScen: ComponentConfig = {
  "_id": "Banner2InScen",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "Banner2InScen",
  "label": "场景内横幅组件",
  "image": "/images/banner2-in-scen.png",
  "remark": "在特定场景中使用的横幅组件",
  "elements": {
    "name": "场景内横幅组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "资源",
        "key": "resource",
        "contentType": ["image", "video"],
        "required": true
      }
    ]
  }
};

const BigImgSizeContain: ComponentConfig = {
  "_id": "BigImgSizeContain",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "BigImgSizeContain",
  "label": "大图尺寸包含组件",
  "image": "/images/big-img-size-contain.png",
  "remark": "用于展示大图并保持尺寸的组件",
  "elements": {
    "name": "大图尺寸包含组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "图片资源",
        "key": "resource",
        "contentType": ["image"],
        "required": true
      },
      {
        "name": "标题",
        "key": "title",
        "contentType": ["text"],
        "required": false
      }
    ]
  }
};

const BlockHeight: ComponentConfig = {
  "_id": "BlockHeight",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "BlockHeight",
  "label": "区块高度组件",
  "image": "/images/block-height.png",
  "remark": "用于设置区块高度的占位组件",
  "elements": {
    "name": "区块高度组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "高度值",
        "key": "height",
        "contentType": ["number"],
        "required": false,
        "defaultValue": 50
      }
    ]
  }
};

const ExternalVideoMoreText: ComponentConfig = {
  "_id": "ExternalVideoMoreText",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ExternalVideoMoreText",
  "label": "外部视频更多文本组件",
  "image": "/images/external-video-more-text.png",
  "remark": "带有更多文本描述的外部视频展示组件",
  "elements": {
    "name": "外部视频更多文本组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "描述",
        "key": "desc",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "视频列表",
        "key": "videos",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "视频标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "视频缩略图",
            "key": "thumbnail",
            "contentType": ["image"],
            "required": true
          },
          {
            "name": "视频URL",
            "key": "url",
            "contentType": ["video"],
            "required": true
          }
        ]
      }
    ]
  }
};

const ProductSelect: ComponentConfig = {
  "_id": "ProductSelect",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ProductSelect",
  "label": "产品选择组件",
  "image": "/images/product-select.png",
  "remark": "用于产品选择的组件",
  "elements": {
    "name": "产品选择组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "主标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "副标题",
        "key": "subTitle",
        "contentType": ["text"],
        "required": false
      },
      {
        "name": "产品列表",
        "key": "productList",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "产品图片",
            "key": "img",
            "contentType": ["image"],
            "required": true
          },
          {
            "name": "产品标题",
            "key": "title",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "产品副标题",
            "key": "subTitle",
            "contentType": ["text"],
            "required": false
          },
          {
            "name": "产品链接",
            "key": "jumpLink",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "产品规格列表",
            "key": "specs",
            "contentType": ["list"],
            "required": true,
            "children": [
              {
                "name": "规格名",
                "key": "key",
                "contentType": ["text"],
                "required": true
              },
              {
                "name": "规格值",
                "key": "value",
                "contentType": ["text"],
                "required": true
              }
            ]
          }
        ]
      }
    ]
  }
};

const ScenarioDetail: ComponentConfig = {
  "_id": "ScenarioDetail",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "ScenarioDetail",
  "label": "场景详情组件",
  "image": "/images/scenario-detail.png",
  "remark": "用于展示应用场景详情的组件",
  "elements": {
    "name": "场景详情组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "场景标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "场景描述",
        "key": "description",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "场景图片",
        "key": "imageUrl",
        "contentType": ["image"],
        "required": true
      },
      {
        "name": "详细内容",
        "key": "content",
        "contentType": ["text"],
        "required": false
      }
    ]
  }
};

const SubmitInfoForm: ComponentConfig = {
  "_id": "SubmitInfoForm",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "SubmitInfoForm",
  "label": "信息提交表单组件",
  "image": "/images/submit-info-form.png",
  "remark": "用于提交用户信息的表单组件",
  "elements": {
    "name": "信息提交表单组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "表单标题",
        "key": "title",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "提交URL",
        "key": "submitUrl",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "字段列表",
        "key": "fields",
        "contentType": ["list"],
        "required": true,
        "children": [
          {
            "name": "字段标签",
            "key": "label",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "字段名",
            "key": "name",
            "contentType": ["text"],
            "required": true
          },
          {
            "name": "字段类型",
            "key": "type",
            "contentType": ["select"],
            "required": true,
            "options": ["text", "email", "tel", "textarea"]
          },
          {
            "name": "是否必填",
            "key": "required",
            "contentType": ["boolean"],
            "required": false,
            "defaultValue": false
          }
        ]
      }
    ]
  }
};

const SuccessMsgModal: ComponentConfig = {
  "_id": "SuccessMsgModal",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "SuccessMsgModal",
  "label": "成功消息模态框组件",
  "image": "/images/success-msg-modal.png",
  "remark": "用于显示成功消息的模态框组件",
  "elements": {
    "name": "成功消息模态框组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "标题",
        "key": "title",
        "contentType": ["text"],
        "required": true,
        "defaultValue": "提交成功"
      },
      {
        "name": "消息内容",
        "key": "message",
        "contentType": ["text"],
        "required": true
      },
      {
        "name": "是否显示",
        "key": "visible",
        "contentType": ["boolean"],
        "required": false,
        "defaultValue": false
      }
    ]
  }
};

const VideoModal: ComponentConfig = {
  "_id": "VideoModal",
  "created_at": 1753000000000,
  "updated_at": 1753000000000,
  "name": "VideoModal",
  "label": "视频模态框组件",
  "image": "/images/video-modal.png",
  "remark": "用于播放视频的模态框组件",
  "elements": {
    "name": "视频模态框组件数据",
    "key": "data",
    "contentType": ["object"],
    "required": true,
    "children": [
      {
        "name": "视频URL",
        "key": "videoUrl",
        "contentType": ["video"],
        "required": true
      },
      {
        "name": "是否显示",
        "key": "visible",
        "contentType": ["boolean"],
        "required": false,
        "defaultValue": false
      }
    ]
  }
};

// 将所有组件配置导出为一个对象
const componentPropConfig = {
  Banner,
  CoreTechHighlights,
  ImgCard,
  // Navbar,
  BigBanner,
  Banner2,
  ImgCard2,
  CompanyPartners,
  // Footer,
  ContactUs,
  Api,
  ApplicationScenarios,
  BigBannerNoLoopWithPagination,
  BigBannerStickyScale,
  BigBannerWithPagination,
  CoreAdvantages,
  DeveloperResources,
  FileDownload,
  HonorImgCard,
  IndustryImgCard,
  IndustryTextCard,
  Intro,
  ItemComparison,
  MetricDisplayGroup,
  NewsList,
  PageTopBanner,
  ProductImgCard,
  ProductSpecs,
  ServiceSupport,
  Timeline,
  ThreeDViewer,
  ExternalVideo,
  SupportDoc,
  CenterBanner,
  CardStack,
  // AutoLoopScroll,
  // AxModal,
  // AxSelect,
  // Banner2InScen,
  BigImgSizeContain,
  // BlockHeight,
  ExternalVideoMoreText,
  ProductSelect,
  // ScenarioDetail,
  SubmitInfoForm,
  SuccessMsgModal,
  // VideoModal
};

// 导出组件配置componentPropConfig成列表形式
const List = Object.values(componentPropConfig);

export { List };