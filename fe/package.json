{"name": "agilex-admin", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4201", "start-proxy-server": "node ./proxy.server.js", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@ctrl/tinycolor": "^4.1.0", "@wangeditor/editor": "^5.1.23", "axios": "^1.7.9", "ng-zorro-antd": "^19.0.1", "ngx-clipboard": "^16.0.0", "ngx-editor": "^19.0.0-beta.1", "ngx-quill": "^28.0.1", "quill": "^2.0.3", "quill-image-resize-module-ts": "^3.0.3", "request": "^2.88.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.6", "@angular/cli": "^19.0.6", "@angular/compiler-cli": "^19.0.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.6.2"}}