<nz-layout class="app-layout" [class.collapsed]="isCollapsed">
  @if(isLoggedIn){
    <nz-sider class="menu-sidebar" nzCollapsible nzWidth="256px" nzBreakpoint="md" [(nzCollapsed)]="isCollapsed"
    [nzTrigger]="null">
      <app-sidebar-menu [isCollapsed]="isCollapsed" [permissions]="userInfo.permissions"></app-sidebar-menu>
    </nz-sider>
  }
  <nz-layout>
    @if(isLoggedIn){
    <nz-header class="layout-header">
      <div class="app-header">
        <span class="header-trigger" (click)="isCollapsed = !isCollapsed">
          <nz-icon class="trigger" [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'" />
        </span>
        <nz-breadcrumb style="display: inline-block;" [nzAutoGenerate]="true"></nz-breadcrumb>
        <div class="menu">
          <button nz-button nz-dropdown [nzDropdownMenu]="menu" nzPlacement="bottomLeft"
            class="username">{{userInfo['username']}}</button>
          <nz-dropdown-menu #menu="nzDropdownMenu">
            <ul nz-menu>
              <li nz-menu-item (click)="logout()">
                <a>登出</a>
              </li>
            </ul>
          </nz-dropdown-menu>
        </div>
      </div>
    </nz-header>
    }
    <nz-content class="main-content">
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
  </nz-layout>
</nz-layout>