import { Component } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzMenuModule,  } from 'ng-zorro-antd/menu';
import { CommonModule } from '@angular/common';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { AuthService } from './auth.service';
import { filter, map } from 'rxjs';
import { SidebarMenuComponent } from './components/sidebar-menu/sidebar-menu.component';
@Component({
  selector: 'app-root',
  imports: [RouterOutlet, NzIconModule, NzLayoutModule, NzMenuModule, CommonModule, NzBreadCrumbModule, NzDropDownModule,SidebarMenuComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {

  isCollapsed = false;
  isLoggedIn: Boolean = false;
  userInfo:any = {};
  constructor(private authService: AuthService, private router: Router, private activatedRoute: ActivatedRoute) {
  }

  logout() {
    this.authService.logout();
    this.router.navigate(['login']);
  }

  ngOnInit() {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
    ).subscribe((event) => {
      this.isLoggedIn = this.authService.isLoggedIn();
      const currentUrl = event.urlAfterRedirects;
      if (!this.isLoggedIn) {
        this.router.navigate(['login']);
      } else {
        if(!this.userInfo.username){
          this.authService.getProfile().subscribe(res => {
            this.userInfo = res;
            console.log(res);
          });
        }

        if (currentUrl === '/login') {
          this.router.navigate(['']);
        }
      }
    })
  }
}
