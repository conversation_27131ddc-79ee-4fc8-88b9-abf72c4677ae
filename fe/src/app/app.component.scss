:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  min-height: 100vh;

  // 确保主布局不影响固定定位
  &.ant-layout {
    background: transparent;
  }

  // 内层布局容器
  .ant-layout {
    background: #f0f2f5;
  }
}

.menu-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10;
  height: 100vh;
  overflow-y: auto;
  box-shadow: 2px 0 6px rgba(0,21,41,.35);
  transition: all 0.2s ease-in-out;
}

.header-trigger {
  height: 64px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all .3s,padding 0s;
}

.trigger:hover {
  color: #1890ff;
}

.sidebar-logo {
  position: relative;
  height: 64px;
  padding-left: 24px;
  overflow: hidden;
  line-height: 64px;
  background: #001529;
  transition: all .3s;
}

.sidebar-logo img {
  display: inline-block;
  height: 32px;
  width: 32px;
  vertical-align: middle;
}

.sidebar-logo h1 {
  display: inline-block;
  margin: 0 0 0 20px;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
  font-family: Avenir,Helvetica Neue,Arial,Helvetica,sans-serif;
  vertical-align: middle;
}

nz-header {
  position: fixed;
  top: 0;
  right: 0;
  height: 48px;
  line-height: 48px;
  padding: 0;
  width: calc(100% - 256px);
  z-index: 9;
  transition: width 0.2s;
}

// 当侧边栏收起时调整header宽度
.app-layout.collapsed nz-header {
  width: calc(100% - 80px);
}

.app-header {
  position: relative;
  height: 48px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

nz-content.main-content {
  margin-left: 256px;
  margin-top: 48px;
  margin-right: 0;
  margin-bottom: 0;
  padding: 24px;
  min-height: calc(100vh - 48px);
  transition: margin-left 0.2s;
}

// 当侧边栏收起时调整content的左边距
.app-layout.collapsed nz-content.main-content {
  margin-left: 80px;
}

.inner-content {
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 96px);
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.menu_title {
  padding: 0;
  outline: none;
}
.menu {
  position: absolute;
  top: 0;
  right: 16px;
}
.username {
  border: 0;
  background: none;
  cursor: pointer;
}

// 响应式设计 - 移动端适配
@media (max-width: 768px) {
  .menu-sidebar {
    width: 80px !important;
  }

  nz-header {
    width: calc(100% - 80px) !important;
  }

  nz-content.main-content {
    margin-left: 80px !important;
  }

  .app-layout.collapsed nz-header {
    width: 100% !important;
  }

  .app-layout.collapsed nz-content.main-content {
    margin-left: 0 !important;
  }

  .app-layout.collapsed .menu-sidebar {
    transform: translateX(-100%);
  }
}
