import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { API_URL } from './const';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(private http: HttpClient) { }
  private isLoggedInKey = 'isLoggedIn';
  redirectUrl = '';
  login(data: any):Observable<any> {
    return this.http.post(`${API_URL}/auth/login`, data);
  }

  saveLoginStatus(token: string): void {
    localStorage.setItem(this.isLoggedInKey, 'true');
  }

  getLoginStatus(): string | null {
    return localStorage.getItem(this.isLoggedInKey);
  }

  isLoggedIn(): boolean {
    return !!this.getLoginStatus();
  }

  // 登出
  logout(): void {
    localStorage.removeItem(this.isLoggedInKey);
  }

  getProfile(): Observable<any> {
    return this.http.get(`${API_URL}/auth/profile`);
  }
}
