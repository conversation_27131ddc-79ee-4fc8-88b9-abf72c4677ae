import { Component, forwardRef, Input } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzUploadFile, NzUploadModule } from 'ng-zorro-antd/upload';
import { CommonModule } from '@angular/common';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-video-upload-form',
  imports: [NzIconModule, NzModalModule, NzUploadModule, CommonModule],
  templateUrl: './video-upload-form.component.html',
  styleUrl: './video-upload-form.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => VideoUploadFormComponent),
      multi: true,
    },
  ],
  standalone: true
})
export class VideoUploadFormComponent implements ControlValueAccessor {
  @Input() uploadUrl: string = '';
  fileList: NzUploadFile[] = [];
  videoUrl: string | null = null;
  loading = false;

  previewVisible = false;
  previewVideoUrl: string | null = null;

  // ControlValueAccessor 实现
  onChange: any = () => {};
  onTouched: any = {};

  constructor(private messageService: NzMessageService) {}

  writeValue(value: any): void {
    this.videoUrl = value;
    this.fileList = value ? [{
      uid: '-1',
      name: value,
      status: 'done',
      url: value
    }] : [];
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  handleChange(info: any): void {
    switch (info.file.status) {
      case 'uploading':
        this.loading = true;
        break;
      
      case 'done':
        this.loading = false;
        this.videoUrl = info.file.response?.url || null;
        this.fileList = this.videoUrl ? [{
            uid: '-1',
            name: this.videoUrl,
            status: 'done',
            url: this.videoUrl
          }] : [];
        this.onChange(this.videoUrl);
        this.messageService.success('视频上传成功');
        break;
      
      case 'error':
        this.loading = false;
        this.fileList = [];
        this.messageService.error('视频上传失败');
        break;

      // 新增删除链接分支
      case 'removed':
        this.videoUrl = null;
        this.fileList = [];
        this.onChange(null);
        this.messageService.success('视频链接已删除');
        break;
    }
  }

  downloadVideo = (): void => {
    if (!this.videoUrl) {
      this.messageService.warning('没有可下载的视频链接');
      return;
    }

    try {
      const link = document.createElement('a');
      link.href = this.videoUrl;
      link.download = this.videoUrl.split('/').pop() || 'video';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.messageService.success('开始下载视频');
    } catch (err) {
      this.messageService.error('下载视频失败');
    }
  }

  handlePreview = (file: NzUploadFile): void => {
    this.previewVideoUrl = file.url || file.response?.url;
    this.previewVisible = true;
  };

  closePreview = (): void => {
    this.previewVisible = false;
  };
}
