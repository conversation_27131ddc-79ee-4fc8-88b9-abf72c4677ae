<nz-upload 
  [nzAction]="uploadUrl"
  nzListType="picture-card"
  [(nzFileList)]="fileList"
  [nzDownload]="download"
  (nzChange)="handleChange($event)"
  [nzPreview]="handlePreview"
  [nzShowButton]="fileList.length < 1"
  [nzAccept]="'.jpg, .jpeg, .png, .gif, .ico'"> <!-- 新增属性 -->
  <div class="upload-btn">
    @if (!imageUrl) {
    <span class="upload-icon" nz-icon [nzType]="loading ? 'loading' : 'plus'"></span>
    <div class="ant-upload-text">Upload</div>
    } @else {
    <img [src]="imageUrl" style="width: 100%" />
    }
  </div>
</nz-upload>
<nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
  (nzOnCancel)="previewVisible = false">
  <ng-template #modalContent>
    <img [src]="previewImage" style="width: 100%" />
  </ng-template>
</nz-modal>