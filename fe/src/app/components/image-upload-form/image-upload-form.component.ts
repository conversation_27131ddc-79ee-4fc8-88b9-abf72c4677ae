import { Component, forwardRef, Input } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzUploadFile, NzUploadModule } from 'ng-zorro-antd/upload';
import { CommonModule } from '@angular/common';
import { NzMessageService } from 'ng-zorro-antd/message';

const getBase64 = (file: File): Promise<string | ArrayBuffer | null> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });

@Component({
  selector: 'app-image-upload-form',
  imports: [NzIconModule, NzModalModule, NzUploadModule, CommonModule],
  templateUrl: './image-upload-form.component.html',
  styleUrl: './image-upload-form.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ImageUploadFormComponent),
      multi: true,
    },
  ],
})
export class ImageUploadFormComponent {
  @Input() uploadUrl: string = '';
  fileList: NzUploadFile[] = []; // 上传文件列表
  previewVisible = false;
  imageUrl: string | null = null; // 图片 URL
  previewImage: string | null = null;
  loading: boolean = false;

  constructor(private messageService: NzMessageService) { }


  // ControlValueAccessor 接口实现
  onChange = (value: any) => { };
  onTouched = () => { };

  writeValue(value: any): void {
    this.imageUrl = value;
    this.fileList = value ? [{
      uid: '1',
      status: 'done',
      name: 'file',
      url: value
    }] : [];
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    // 可选：处理禁用状态
  }

  handleChange(info: { file: NzUploadFile }): void {
    console.log(info);
    switch (info.file.status) {
      case 'removed':
        this.imageUrl = null;
        this.fileList = [];
        this.onChange(this.imageUrl);
        break;
      case 'uploading':
        this.loading = true;
        break;
      case 'done':
        // Get this url from response in real world.
        this.imageUrl = info.file.response?.url || null;
        this.onChange(this.imageUrl); // 通知表单值更新
        this.loading = false;
        break;
      case 'error':
        this.fileList = [];
        this.messageService.error('Network error');
        this.loading = false;
        break;
    }
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    console.log(file);
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  download = (): void => {
    if (!this.imageUrl) {
      this.messageService.warning('没有可下载的文件链接');
      return;
    }

    try {
      const link = document.createElement('a');
      link.href = this.imageUrl;
      link.download = this.imageUrl.split('/').pop() || 'file';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.messageService.success('开始下载文件');
    } catch (err) {
      this.messageService.error('下载文件失败');
    }
  }
}
