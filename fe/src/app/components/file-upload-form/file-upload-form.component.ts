import { Component, forwardRef, Input } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzUploadFile, NzUploadModule } from 'ng-zorro-antd/upload';
import { CommonModule } from '@angular/common';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-file-upload-form',
  imports: [NzIconModule, NzModalModule, NzUploadModule, CommonModule],
  templateUrl: './file-upload-form.component.html',
  styleUrl: './file-upload-form.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FileUploadFormComponent),
      multi: true,
    },
  ],
  standalone: true
})
export class FileUploadFormComponent implements ControlValueAccessor {
  @Input() uploadUrl: string = '';
  fileList: NzUploadFile[] = [];
  fileUrl: string | null = null;
  loading = false;

  // ControlValueAccessor 实现
  onChange: any = () => {};
  onTouched: any = {};

  constructor(private messageService: NzMessageService) {}

  writeValue(value: any): void {
    this.fileUrl = value;
    this.fileList = value ? [{
      uid: '-1',
      name: value,
      status: 'done',
      url: value
    }] : [];
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  handleChange(info: any): void {
    switch (info.file.status) {
      case 'uploading':
        this.loading = true;
        break;
      
      case 'done':
        this.loading = false;
        this.fileUrl = info.file.response?.url || null;
        this.fileList = this.fileUrl ? [{
            uid: '-1',
            name: this.fileUrl,
            status: 'done',
            url: this.fileUrl
          }] : [];
        this.onChange(this.fileUrl);
        this.messageService.success('文件上传成功');
        break;
      
      case 'error':
        this.loading = false;
        this.fileList = [];
        this.messageService.error('文件上传失败');
        break;

      // 新增删除链接分支
      case 'removed':
        this.fileUrl = null;
        this.fileList = [];
        this.onChange(null);
        this.messageService.success('文件链接已删除');
        break;
    }
  }

  downloadFile = (): void => {
    if (!this.fileUrl) {
      this.messageService.warning('没有可下载的文件链接');
      return;
    }

    try {
      const link = document.createElement('a');
      link.href = this.fileUrl;
      link.download = this.fileUrl.split('/').pop() || 'file';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.messageService.success('开始下载文件');
    } catch (err) {
      this.messageService.error('下载文件失败');
    }
  }
}