import { Component, Input } from '@angular/core';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { RouterLink } from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-sidebar-menu',
  imports: [NzMenuModule, NzLayoutModule, RouterLink, NzIconModule, NzToolTipModule],
  templateUrl: './sidebar-menu.component.html',
  styleUrl: './sidebar-menu.component.scss'
})
export class SidebarMenuComponent {
  @Input() permissions: string[] = []; // 接收权限数组
  @Input() isCollapsed = false;

  // 定义所有可能的菜单项
  menuItems = [
    {
      title: '用户管理',
      icon: 'user',
      permission: 'users',
      link: '/users',
    },
    // {
    //   title: '首页',
    //   icon: 'home',
    //   permission: 'home',
    //   link: '/home',
    // },
    {
      title: '组件管理',
      icon: 'appstore',
      permission: 'components',
      link: '/components-library',
    },
    {
      title: '全局组件管理',
      icon: 'appstore',
      permission: 'global-components-manage',
      link: '/global-components-manage',
    },
    {
      title: '页面模板管理',
      icon: 'appstore',
      permission: 'page-templates',
      link: '/pages-library',
    },
    {
      title: '页面管理',
      icon: 'appstore',
      permission: 'page-manage',
      link: '/pages-manage',
    },
    {
      title: '新闻管理',
      icon: 'appstore',
      permission: 'news',
      link: '/news',
    },
    {
      title: '站点信息',
      icon: 'appstore',
      permission: 'sites',
      link: '/sites',
    },
    {
      title: '客户管理',
      icon: 'appstore',
      permission: 'customer',
      link: '/customer',
    },
    {
      title: '产品文档管理',
      icon: 'appstore',
      permission: 'product-documents',
      children: [
        {
          title: '文档管理',
          link: '/product-documents/list',
          permission: 'product-documents'
        },
        {
          title: '系列管理',
          link: '/product-documents/series',
          permission: 'product-documents'  // 使用相同的权限
        }
      ]
    }
  ];

  // 根据权限过滤菜单项
  get filteredMenuItems() {
    return this.menuItems.filter((item) => {
      if (!this.permissions) {
        return [];
      }
      
      // 如果有子菜单，则检查子菜单权限
      if (item.children) {
        return item.children.some(child => this.permissions.includes(child.permission));
      }
      
      // 没有子菜单，直接检查权限
      return this.permissions.includes(item.permission);
    });
  }
}