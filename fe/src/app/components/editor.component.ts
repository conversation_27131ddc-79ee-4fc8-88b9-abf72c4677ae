import { Component, ElementRef, ViewChild, forwardRef, AfterViewInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { IDomEditor, createEditor, createToolbar } from '@wangeditor/editor';
import '@wangeditor/editor/dist/css/style.css';

@Component({
  selector: 'app-editor',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div style="border: 1px solid #ccc;">
      <div #toolbar style="border-bottom: 1px solid #ccc;"></div>
      <div #editor style="height: 400px;"></div>
    </div>
  `,
  providers: [{
    provide: NG_VALUE_ACCESSOR,
    useExisting: forwardRef(() => EditorComponent),
    multi: true
  }]
})
export class EditorComponent implements AfterViewInit, OnDestroy, ControlValueAccessor {
  @ViewChild('editor', { static: true }) editorElem!: ElementRef;
  @ViewChild('toolbar', { static: true }) toolbarElem!: ElementRef;
  private editor!: IDomEditor;

  private onChange = (value: string) => {};
  private onTouched = () => {};
  private value = '';

  ngAfterViewInit() {

    this.editor = createEditor({
      selector: this.editorElem.nativeElement,
      config: {
        placeholder: '请输入内容...',
        onChange: (editor: IDomEditor) => {
          const html = editor.getHtml();
          this.value = html;
          this.onChange(html);
        },
        MENU_CONF: {
          uploadImage: {
            async customUpload(file: File, insertFn: (url: string) => void) {
              const formData = new FormData();
              formData.append('file', file);
              const res = await fetch('/upload-file/image', {
                method: 'POST',
                body: formData
              });
              const data = await res.json();
              insertFn(data.url);
            }
          }
        }
      }
    });

    createToolbar({
      editor: this.editor,
      selector: this.toolbarElem.nativeElement,
      config: {}
    });

    if (this.value) this.editor.setHtml(this.value);
  }

  ngOnDestroy() {
    if (this.editor) this.editor.destroy();
  }

  // ControlValueAccessor 实现
  writeValue(value: string): void {
    this.value = value || '';
    if (this.editor) {
      this.editor.setHtml(this.value);
    }
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    if (this.editor) {
      this.editor.disable();
      if (!isDisabled) this.editor.enable();
    }
  }
}