export const listToObj = (list: Array<any>, key = 'key', value = 'value') => {
    const obj: any = {};

    if (!Array.isArray(list)) {
        return obj;
    }

    list.forEach(item => {
        if (!item || !item[key]) {
            return; // 跳过无效项
        }

        if (item.contentType && item.contentType.includes('list')) {
            // 处理列表类型
            if (item[value] && Array.isArray(item[value])) {
                const arr: any[] = [];
                item[value].forEach((subItemArray: any) => {
                    if (Array.isArray(subItemArray)) {
                        // 处理嵌套数组结构
                        arr.push(listToObj(subItemArray, key, value));
                    } else {
                        // 处理单个对象
                        arr.push(listToObj([subItemArray], key, value));
                    }
                });
                obj[item[key]] = arr;
            } else {
                obj[item[key]] = [];
            }
        } else if (item.contentType && item.contentType.includes('object')) {
            // 处理对象类型
            if (item.children && Array.isArray(item.children)) {
                // 如果有children，递归处理children
                obj[item[key]] = listToObj(item.children, key, value);
            } else if (item[value] !== undefined) {
                // 如果有值，使用值
                obj[item[key]] = item[value];
            } else {
                // 默认空对象
                obj[item[key]] = {};
            }
        } else {
            // 处理其他类型（text, image, video等）
            if (item.contentType && (item.contentType.includes('image') || item.contentType.includes('video'))) {
                obj[item[key]] = {
                    url: item[value] || '',
                    type: item.contentType[0] || 'unknown',
                };
            } else if (Array.isArray(item[value])) {
                // 如果值是数组，递归处理
                obj[item[key]] = listToObj(item[value], key, value);
            } else {
                // 普通值
                obj[item[key]] = item[value] !== undefined ? item[value] : '';
            }
        }
    });

    return obj;
}