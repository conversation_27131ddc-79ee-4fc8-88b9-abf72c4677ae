import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: '/welcome'
  },
  {
    title: 'welcome',
    path: 'welcome',
    loadChildren: () => import('./pages/welcome/welcome.routes').then(m => m.WELCOME_ROUTES),
    data: {
      breadcrumb: 'welcome'
    },
  },
  {
    title: '登录',
    path: 'login',
    pathMatch: 'full',
    loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent),
    data: {
      breadcrumb: '用户登录'
    },
  },
  {
    title: '用户管理',
    path: 'users',
    pathMatch: 'full',
    loadComponent: () => import('./pages/users/users.component').then(m => m.UsersComponent),
    data: {
      breadcrumb: '用户管理'
    },
  },
  {
    title: '首页',
    path: 'home',
    pathMatch: 'full',
    loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent),
    data: {
      breadcrumb: '首页'
    },
  },
  //组件库管理
  {
    title: '组件管理',
    path: 'components-library',
    pathMatch: 'full',
    loadComponent: () => import('./pages/components-library/components-library.component').then(m => m.ComponentsLibraryComponent),
    data: {
      breadcrumb: '组件管理'
    },
  }, 
  //页面模板管理
  {
    title: '页面模板管理',
    path: 'pages-library',
    pathMatch: 'full',
    loadComponent: () => import('./pages/pages-library/pages-library.component').then(m => m.PagesLibraryComponent),
    data: {
      breadcrumb: '页面模板管理'
    },
  }, 
  //页面管理
  {
    title: '页面管理',
    path: 'pages-manage',
    pathMatch: 'full',
    loadComponent: () => import('./pages/pages-manage/pages-manage.component').then(m => m.PagesManageComponent),
    data: {
      breadcrumb: '页面管理'
    },
  },
  //通用组件管理
  {
    title: '通用组件管理',
    path: 'global-components-manage',
    pathMatch: 'full',
    loadComponent: () => import('./pages/global-components-manage/global-components-manage.component').then(m => m.GlobalComponentsManageComponent),
    data: {
      breadcrumb: '页面管理'
    },
  },
  //新闻管理
  {
    title: '新闻管理',
    path: 'news',
    pathMatch: 'full',
    loadComponent: () => import('./pages/news/news.component').then(m => m.NewsComponent),
    data: {
      breadcrumb: '新闻管理'
    },
  },
  //站点管理
  {
    title: '站点信息',
    path: 'sites',
    pathMatch: 'full',
    loadComponent: () => import('./pages/sites/sites.component').then(m => m.SitesComponent),
    data: {
      breadcrumb: '站点信息'
    },
  },
  //客户管理
  {
    title: '客户管理',
    path: 'customer',
    pathMatch: 'full',
    loadComponent: () => import('./pages/customer/customer.component').then(m => m.CustomerComponent),
    data: {
      breadcrumb: '客户管理'
    },
  },
  //产品文档管理
  {
    title: '产品文档管理',
    path: 'product-documents',
    data: {
      breadcrumb: '产品文档管理'
    },
    children: [
      {
        title: '文档管理',
        path: 'list',
        pathMatch: 'full',
        loadComponent: () => import('./pages/product-documents/product-documents.component').then(m => m.ProductDocumentsComponent),
        data: {
          breadcrumb: '文档管理'
        },
      },
      {
        title: '系列管理',
        path: 'series',
        pathMatch: 'full',
        loadComponent: () => import('./pages/product-series/product-series.component').then(m => m.ProductSeriesComponent),
        data: {
          breadcrumb: '系列管理'
        },
      },
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'list'
      }
    ]
  },

];