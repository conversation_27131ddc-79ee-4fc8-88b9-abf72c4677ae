import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TablePageSizeService {
  private readonly STORAGE_KEY = 'table_page_size';
  private readonly DEFAULT_PAGE_SIZE = 20;
  
  private pageSizeSubject = new BehaviorSubject<number>(this.getStoredPageSize());
  
  constructor() {}
  
  /**
   * 获取当前页面大小
   */
  getPageSize(): number {
    return this.pageSizeSubject.value;
  }
  
  /**
   * 获取页面大小的Observable
   */
  getPageSize$(): Observable<number> {
    return this.pageSizeSubject.asObservable();
  }
  
  /**
   * 设置页面大小
   * @param pageSize 新的页面大小
   */
  setPageSize(pageSize: number): void {
    if (pageSize > 0) {
      this.pageSizeSubject.next(pageSize);
      this.savePageSize(pageSize);
    }
  }
  
  /**
   * 从localStorage获取存储的页面大小
   */
  private getStoredPageSize(): number {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const pageSize = parseInt(stored, 10);
        return pageSize > 0 ? pageSize : this.DEFAULT_PAGE_SIZE;
      }
    } catch (error) {
      console.warn('Failed to get page size from localStorage:', error);
    }
    return this.DEFAULT_PAGE_SIZE;
  }
  
  /**
   * 保存页面大小到localStorage
   */
  private savePageSize(pageSize: number): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, pageSize.toString());
    } catch (error) {
      console.warn('Failed to save page size to localStorage:', error);
    }
  }
}
