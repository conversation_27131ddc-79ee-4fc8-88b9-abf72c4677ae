<div class="welcome-container">
  <h1>欢迎使用后台管理系统</h1>

  <nz-card nzTitle="系统概览" class="overview-card">
    <p>这是一个功能完整的后台管理系统，具有以下特性：</p>
    <ul>
      <li>固定的左侧导航菜单</li>
      <li>固定的顶部状态栏</li>
      <li>响应式布局设计</li>
      <li>用户权限管理</li>
      <li>数据管理功能</li>
    </ul>
  </nz-card>

  <nz-card nzTitle="布局测试" class="test-card">
    <p>请滚动页面测试布局是否正确：</p>
    <div class="test-content">
      <div class="test-item" *ngFor="let item of testItems; let i = index">
        <h3>测试项目 {{i + 1}}</h3>
        <p>这是第 {{i + 1}} 个测试项目。当您滚动页面时，左侧菜单和顶部状态栏应该保持固定，而这个内容区域应该正常滚动。</p>
        <p>内容区域不应该被菜单栏遮挡，应该有适当的边距。</p>
        <nz-divider></nz-divider>
      </div>
    </div>
  </nz-card>

  <nz-card nzTitle="功能验证" class="function-card">
    <h3>请验证以下功能：</h3>
    <nz-list [nzDataSource]="checkList" nzBordered>
      <ng-template #renderItem let-item>
        <nz-list-item>
          <nz-list-item-meta>
            <nz-list-item-meta-title>
              <nz-icon [nzType]="item.checked ? 'check-circle' : 'clock-circle'"
                       [style.color]="item.checked ? '#52c41a' : '#faad14'"></nz-icon>
              {{item.title}}
            </nz-list-item-meta-title>
            <nz-list-item-meta-description>
              {{item.description}}
            </nz-list-item-meta-description>
          </nz-list-item-meta>
        </nz-list-item>
      </ng-template>
    </nz-list>
  </nz-card>
</div>
