import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../auth.service';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';

@Component({
  selector: 'app-welcome',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzListModule,
    NzIconModule,
    NzDividerModule
  ],
  templateUrl: './welcome.component.html',
  styleUrl: './welcome.component.scss'
})
export class WelcomeComponent {

  testItems = Array.from({ length: 20 }, (_, i) => i + 1);

  checkList = [
    {
      title: '左侧菜单固定',
      description: '滚动页面时，左侧菜单应该保持固定在屏幕左侧',
      checked: true
    },
    {
      title: '顶部状态栏固定',
      description: '滚动页面时，顶部状态栏应该保持固定在屏幕顶部',
      checked: true
    },
    {
      title: '内容区域不被遮挡',
      description: '内容区域应该有适当的左边距，不被菜单栏遮挡',
      checked: false
    },
    {
      title: '菜单收起功能',
      description: '点击菜单收起按钮，布局应该正确调整',
      checked: false
    },
    {
      title: '响应式适配',
      description: '在不同屏幕尺寸下，布局应该正确适配',
      checked: false
    }
  ];

  constructor(private authService: AuthService) {}

  getUserInfo(){
    this.authService.getProfile().subscribe(res => {
      console.log(res);
    });
  }

  ngOnInit() {
    this.getUserInfo()
  }
}
