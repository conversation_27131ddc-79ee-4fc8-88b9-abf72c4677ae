.ant-advanced-search-form {
    padding: 24px;
    background: #fbfbfb;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
}

button {
    margin-left: 8px;
}

.search-result-list {
    margin-top: 16px;
    border: 1px dashed #e9e9e9;
    border-radius: 6px;
    background-color: #fafafa;
    min-height: 200px;
    text-align: center;
    padding: 0 10px;
}

.component-img{
    object-fit: contain;
}

.modal-content {

    .form-group-container {
        border: 1px dashed #9E9E9E;
        margin-bottom: 10px;
        // padding-bottom: 10px;
        position: relative;
    }

    .form-item-title {
        font-size: 16px;
        text-align: center;
        margin-bottom: 10px;
    }

    .delete-element-list {
        position: absolute;
        top: 5px;
        right: 5px;
    }

    .form-item {
        margin-bottom: 10px;
        position: relative;
        margin: 0 24px;

        input {
            margin-bottom: 8px;
        }

        nz-select {
            margin-bottom: 8px;
        }

        .delete-form-item-btn {
            position: absolute;
            right: 10px;
        }

        .param-input {
            width: 50px;
        }

        &.count-type{
            margin-top: 10px;
        }

    }

    .add-form-item-btn {
        display: block;
        margin: 0 auto;
    }
}