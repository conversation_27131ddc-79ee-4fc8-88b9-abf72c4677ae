import { Component } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NewsService } from './news.service';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { ClipboardModule } from 'ngx-clipboard';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { ImageUploadFormComponent } from '../../components/image-upload-form/image-upload-form.component';
import { EditorComponent } from '../../components/editor.component';
import { TablePageSizeService } from '../../services/table-page-size.service';

@Component({
  selector: 'app-news',
  imports: [
    ReactiveFormsModule,
    CommonModule,
    FormsModule,
    NzButtonModule,
    NzFormModule,
    ClipboardModule,
    NzToolTipModule,
    NzInputModule,
    NzIconModule,
    NzModalModule,
    NzTableModule,
    NzDividerModule,
    NzDrawerModule,
    NzSelectModule,
    NzAlertModule,
    ImageUploadFormComponent,
    EditorComponent,
  ],
  templateUrl: './news.component.html',
  styleUrl: './news.component.scss'
})
export class NewsComponent {






  constructor(
    private fb: FormBuilder,
    private NewsService: NewsService,
    private messageService: NzMessageService,
    private tablePageSizeService: TablePageSizeService
  ) {
    this.searchValidateForm = this.fb.group({});
    this.newsForm = this.fb.group({});
    this.importForm = this.fb.group({
      url: ['', [Validators.required]]
    });
  }

  NEWS_TYPE_OPTIONS = [{ label: '新闻', value: 'news' }]

  NEWS_TYPE_STR: { [key: string]: string } = {}
  // 生命周期钩子
  ngOnInit(): void {
    // 初始化页面大小
    this.pageSize = this.tablePageSizeService.getPageSize();

    this.searchControlArray.map((control) => {
      this.searchValidateForm.addControl(control.name, this.fb.control(''));
    });
    this.getList(this.pageIndex, this.pageSize, {});
    this.initNewsForm();
    this.initNewsTypeOptions();
  }

  initNewsTypeOptions(): void {
    this.NewsService.getNewsType().subscribe(res => {
      this.NEWS_TYPE_OPTIONS = res.data;
      this.NEWS_TYPE_STR = this.NEWS_TYPE_OPTIONS.reduce((acc: any, cur: any) => {
        acc[cur.value] = cur.label;
        return acc;
      }, {});
    });
  }

  // #region 搜索
  listData: any = [];
  componentList: any = [];
  searchValidateForm: FormGroup;
  searchControlArray = [
    {
      name: 'title',
      labelName: '新闻标题'
    }
  ];
  pageIndex: number = 1;
  pageSize: number = 20;
  total: number = 0;
  listLoading: boolean = false;
  selectNewsType: string = '';

  resetForm(): void {
    this.searchValidateForm.reset();
    this.selectNewsType = '';
    this.search(true);
  }

  search(reset = false): void {
    let searchFilter: any = {};
    Object.keys(this.searchValidateForm.controls).forEach(k => {
      searchFilter[k] = this.searchValidateForm.controls[k]['value'];
    });
    if (reset) {
      searchFilter = {};
      this.pageIndex = 1;
    }
    if (this.selectNewsType) {
      searchFilter['newsType'] = this.selectNewsType;
    }
    this.getList(this.pageIndex, this.pageSize, searchFilter);
  }



  onPageIndexChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.search();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.tablePageSizeService.setPageSize(pageSize);
    this.search();
  }


  getList(pageIndex: number = 1, pageSize: number = 20, search: { [k: string]: string }): void {
    this.listLoading = true;
    this.NewsService.getList(pageIndex, pageSize, search).subscribe(res => {
      this.listData = res.data.list;
      this.total = res.data.total;
      this.pageIndex = res.data.pn;
      this.pageSize = res.data.ps;
      this.listLoading = false;
    });
  }

  copySuccess(): void {
    this.messageService.success('复制成功');
  }

  drawerVisible: boolean = false;
  newsForm: FormGroup;
  curEditNews: any = null;

  // 导入功能相关
  importModalVisible: boolean = false;
  importForm: FormGroup;
  importLoading: boolean = false;

  editNews(data: any) {
    this.curEditNews = data;
    this.newsForm.patchValue(data);
    this.showNewsDrawer();
  }

  // 判断是否为导入的新闻
  isImportedNews(): boolean {
    return this.curEditNews && this.curEditNews.isImported;
  }

  showNewsDrawer(): void {
    this.drawerVisible = true;
  }

  closeNewsDrawer() {
    this.drawerVisible = false;
    this.initNewsForm();
  }

  initNewsForm() {
    this.newsForm = this.fb.group({
      title: ['', Validators.required],
      subTitle: ['', Validators.required],
      newsType: ['', Validators.required],
      image: ['', Validators.required],
      content: ['', Validators.required],
      remark: [''],
    });
  }

  submitNewsInfo() {
    if (this.curEditNews) {
      this.newsForm.addControl('_id', this.fb.control(this.curEditNews._id));
      this.NewsService.update(this.newsForm.value).subscribe(res => {
        this.messageService.success('更新成功');
        this.closeNewsDrawer();
        this.search();
      });
    } else {
      this.NewsService.create(this.newsForm.value).subscribe(res => {
        this.messageService.success('创建成功');
        this.closeNewsDrawer();
        this.search();
      });
    }
  }

  isOkLoading: unknown;
  curDeleteNews: any;
  deleteNewsModalVisible: any;
  deleteNews(): void {
    this.isOkLoading = true;
    this.NewsService.delete(this.curDeleteNews._id).subscribe(res => {
      this.messageService.success('删除成功');
      this.hideDeleteNewsModal();
      this.isOkLoading = false;
      this.search();
    });
  }
  showDeleteNewsModal(news: any) {
    this.curDeleteNews = news;
    this.deleteNewsModalVisible = true;
  }


  hideDeleteNewsModal() {
    this.curDeleteNews = null;
    this.deleteNewsModalVisible = false;
  }

  // 导入功能方法
  showImportModal(): void {
    this.importModalVisible = true;
    this.importForm.reset();
  }

  hideImportModal(): void {
    this.importModalVisible = false;
    this.importLoading = false;
  }

  importFromUrl(): void {
    if (this.importForm.valid) {
      this.importLoading = true;
      const url = this.importForm.get('url')?.value;

      this.NewsService.importFromUrl(url).subscribe({
        next: (res) => {
          if (res.code === 0) {
            this.messageService.success(`导入成功！已下载 ${res.data.imageCount}/${res.data.totalImages} 张图片`);
            this.hideImportModal();
            this.search(); // 刷新列表

            // 直接使用导入返回的数据打开编辑界面
            this.openImportedNewsEditor(res.data);
          } else {
            this.messageService.error(res.msg || '导入失败');
          }
        },
        error: (error) => {
          this.messageService.error('导入失败：' + (error.error?.msg || error.message || '网络错误'));
        },
        complete: () => {
          this.importLoading = false;
        }
      });
    } else {
      this.messageService.warning('请输入有效的URL');
    }
  }

  private openImportedNewsEditor(importData: any): void {
    // 使用导入返回的数据直接填充表单
    this.curEditNews = {
      _id: importData.articleId,
      title: importData.title,
      subTitle: importData.title, // 使用标题作为副标题
      newsType: 'productNews', // 默认类型
      image: '', // 如果有封面图片会在后端设置
      content: importData.content,
      remark: `从 ${importData.sourceUrl || ''} 导入`,
      sourceUrl: importData.sourceUrl,
      isImported: true
    };

    // 填充表单数据
    this.newsForm.patchValue(this.curEditNews);
    this.showNewsDrawer();
  }

  previewNews(id: string): void {
    const previewUrl = `/news/preview/${id}`;
    window.open(previewUrl, '_blank');
  }

}
