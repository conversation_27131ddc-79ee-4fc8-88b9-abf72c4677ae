import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { API_URL } from '../../const';

@Injectable({
  providedIn: 'root'
})
export class NewsService {

  constructor(private http: HttpClient) { }

  getList(pageIndex: number, pageSize: number, filters: { [k: string]: string }): Observable<any> {
    let params = new HttpParams()
      .append('pn', <string><unknown>pageIndex)
      .append('ps', <string><unknown>pageSize);

    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params = params.append(key, filters[key]);
      }
    });
    return this.http.get(`${API_URL}/news/list`, { params });
  }

  getNewsType(): Observable<any> {
    return this.http.get(`${API_URL}/news/news-type`);
  }

  create(newsData: any): Observable<any> {
    return this.http.post(`${API_URL}/news/create`, newsData);
  }

  update(newsData: any): Observable<any> {
    return this.http.post(`${API_URL}/news/update`, newsData);
  }

  delete(id: number): Observable<any> {
    return this.http.post(`${API_URL}/news/delete`, { id: id });
  }

  importFromUrl(url: string): Observable<any> {
    return this.http.post(`${API_URL}/news/import-from-url`, { url });
  }
}
