<form nz-form [formGroup]="searchValidateForm" class="ant-advanced-search-form">
    <div nz-row [nzGutter]="24">
        @for (control of searchControlArray; track control['name']) {
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>{{ control['labelName'] }}</nz-form-label>
                <nz-form-control>
                    <input nz-input [placeholder]="control['labelName']" [formControlName]="control['name']"
                        [attr.id]="control['name']" />
                </nz-form-control>
            </nz-form-item>
        </div>
        }
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>页面类型</nz-form-label>
                <nz-form-control>
                    <nz-select [(ngModel)]="selectNewsType"  (ngModelChange)="search()" [ngModelOptions]="{standalone: true}" name="pageType" nzPlaceHolder="选择页面类型" style="width: 100%">
                        <nz-option nzValue="" nzLabel="全部"></nz-option>
                        @for (option of NEWS_TYPE_OPTIONS; track $index) {
                        <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        }
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
        </div>
    </div>
     <div nz-row>
        <div nz-col [nzSpan]="24" class="search-area">
            <button nz-button [nzType]="'primary'" (click)="search()">搜索</button>
            <button nz-button (click)="resetForm()">重置</button>
            <button nz-button [nzType]="'primary'" (click)="showNewsDrawer()">新增新闻</button>
            <button nz-button [nzType]="'default'" (click)="showImportModal()">
                <span nz-icon nzType="import"></span>
                从链接导入
            </button>
        </div>
    </div>
</form>

<div class="search-result-list">
    <!-- <nz-table #columnTable [nzData]="listData" [nzScroll]="{ x: '1000px' }"> -->
    <nz-table #columnTable nzShowSizeChanger nzShowQuickJumper [nzData]="listData" [nzFrontPagination]="false"
        nzPaginationType="small" [nzPageSizeOptions]="[10,20,30,40,50]" [(nzPageSize)]="pageSize"
        [(nzPageIndex)]="pageIndex" [nzTotal]="total" [nzLoading]="listLoading"
        (nzPageIndexChange)="onPageIndexChange(pageIndex)" (nzPageSizeChange)="onPageSizeChange(pageSize)"
        [nzScroll]="{ x: '1000px' }">
        <thead>
            <tr>
                <th width="50px">编号</th>
                <th width="100px">标题</th>
                <th width="100px">副标题</th>
                <th width="100px">分类</th>
                <th width="180px">修改时间</th>
                <th width="180px">创建时间</th>
                <th width="100px">ID</th>
                <th nzRight width="160px">操作</th>
            </tr>
        </thead>
        <tbody>
            @for (data of columnTable.data; track data['_id']) {
            <tr>
                <td width="50px">{{(pageIndex - 1) * pageSize + $index + 1}}</td>
                <td width="100px">{{data['title']}}</td>
                <td width="100px">{{data['subTitle']}}</td>
                <td width="100px">{{NEWS_TYPE_STR[data['newsType']]}}</td>
                <td width="180px">{{data['updated_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="180px">{{data['created_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="100px">
                    <span ngxClipboard [cbContent]="data['_id']" (cbOnSuccess)="copySuccess()"
                        style="cursor: pointer; color: blue; text-decoration: underline;" nz-tooltip
                        nzTooltipTitle="点击复制ID">{{data['_id']}}</span>
                </td>
                <td nzRight width="160px">
                    <a (click)="previewNews(data._id)" target="_blank">预览</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="editNews(data)">编辑</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="showDeleteNewsModal(data)">删除</a>
                </td>
            </tr>
            }
        </tbody>
    </nz-table>
</div>

<nz-drawer [nzClosable]="true" [nzVisible]="drawerVisible" nzPlacement="right" nzTitle="页面信息" [nzWidth]="'100vw'"
    [nzFooter]="footerTpl" [nzZIndex]="1" (nzOnClose)="closeNewsDrawer()">
    <div *nzDrawerContent class="page-model-edit-drawer">
        <div class="page-model">
            <div class="modal-content">
                <form nz-form [formGroup]="newsForm">
                    <nz-form-item class="form-item">
                        <nz-form-label nzRequired>新闻标题</nz-form-label>
                        <nz-form-control>
                            <input nz-input formControlName="title" placeholder="请输入新闻标题">
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label nzRequired>新闻副标题</nz-form-label>
                        <nz-form-control>
                            <input nz-input formControlName="subTitle" placeholder="请输入新闻副标题">
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label nzRequired>新闻分类</nz-form-label>
                        <nz-form-control>
                            <nz-select nzPlaceHolder="选择新闻类型" formControlName="newsType" style="width: 100%">
                                @for (option of NEWS_TYPE_OPTIONS; track $index) {
                                <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                                }
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label nzRequired>封面图片</nz-form-label>
                        <nz-form-control>
                            <app-image-upload-form uploadUrl="/upload-file/image"
                                formControlName="image"></app-image-upload-form>
                        </nz-form-control>
                    </nz-form-item>
                    <nz-divider nzText="新闻内容"></nz-divider>
                    <nz-form-item class="form-item" *ngIf="!isImportedNews()">
                        <nz-form-label nzRequired>新闻内容</nz-form-label>
                        <nz-form-control>
                            <app-editor formControlName="content"></app-editor>
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item" *ngIf="isImportedNews()">
                        <nz-form-label>新闻内容</nz-form-label>
                        <nz-form-control>
                            <nz-alert nzType="info" nzMessage="提示" nzDescription="此文章为从链接导入，无法编辑内容。如需修改，请删除后重新创建。">
                            </nz-alert>
                        </nz-form-control>
                    </nz-form-item>
                    <nz-divider nzText="备注"></nz-divider>
                    <nz-form-item class="form-item">
                        <nz-form-label>备注</nz-form-label>
                        <nz-form-control>
                            <textarea style="height: 100px;" nz-input formControlName="remark" placeholder="请输入备注">
                                </textarea>
                        </nz-form-control>
                    </nz-form-item>
                </form>
                <nz-divider nzText="组件数据"></nz-divider>

            </div>
        </div>
    </div>

    <ng-template #footerTpl>
        <div style="float: right">
            <button nz-button style="margin-right: 8px;" (click)="closeNewsDrawer()">Cancel</button>
            <button nz-button nzType="primary" (click)="submitNewsInfo()">提交</button>
        </div>
    </ng-template>
</nz-drawer>

<nz-modal [(nzVisible)]="deleteNewsModalVisible" nzTitle="删除新闻" (nzOnCancel)="hideDeleteNewsModal()"
    (nzOnOk)="deleteNews()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        @if (curDeleteNews) {
        确定要删除<span class="news-name">{{ curDeleteNews.title }}</span> 吗？
        }
    </div>
</nz-modal>

<!-- 导入模态框 -->
<nz-modal [(nzVisible)]="importModalVisible" nzTitle="从链接导入文章" (nzOnCancel)="hideImportModal()"
    (nzOnOk)="importFromUrl()" [nzOkLoading]="importLoading" nzOkText="导入" nzCancelText="取消">
    <div *nzModalContent>
        <form nz-form [formGroup]="importForm">
            <nz-form-item>
                <nz-form-label nzRequired>文章链接</nz-form-label>
                <nz-form-control nzErrorTip="请输入有效的URL">
                    <input nz-input formControlName="url" placeholder="请输入文章链接，如：https://example.com/article" />
                </nz-form-control>
            </nz-form-item>
            <nz-alert nzType="info" nzMessage="提示"
                nzDescription="系统将自动抓取文章内容和图片，并保存到本地。支持JS动态生成的内容。"
                nzShowIcon>
            </nz-alert>
        </form>
    </div>
</nz-modal>