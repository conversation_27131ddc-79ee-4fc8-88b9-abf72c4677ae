.ant-advanced-search-form {
    padding: 24px;
    background: #fbfbfb;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
}

.search-result-list {
    margin-top: 16px;
    border: 1px dashed #e9e9e9;
    border-radius: 6px;
    background-color: #fafafa;
    min-height: 200px;
    text-align: center;
    padding: 0 10px;
}

[nz-form-label] {
    overflow: visible;
}

button {
    margin-left: 8px;
}

.collapse {
    margin-left: 8px;
    font-size: 12px;
}

.search-area {
    text-align: left;
}

.user-form {
    font-size: 14px;
    line-height: 1.6;

    .form-item {
        margin-bottom: 16px;
    }

    [nz-form-label] {
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: 8px;
    }

    input[nz-input] {
        height: 40px;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 14px;
    }

    .error {
        color: #ff4d4f;
        font-size: 12px;
        margin-top: 4px;
    }

    .random-password-btn {
        margin-top: 8px;
        color: #1890ff;
        padding: 0;
        font-size: 12px;
    }

    .random-password-btn:hover {
        text-decoration: underline;
        color: #40a9ff;
    }
}