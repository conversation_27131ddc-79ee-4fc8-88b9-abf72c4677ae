import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_URL } from '../../const';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UsersService {

  constructor(private http: HttpClient) { }

  getUsers(pageIndex: number, pageSize: number, search: { [k: string]: string }): Observable<any> {
    let params = new HttpParams()
      .append('pn', <string><unknown>pageIndex)
      .append('ps', <string><unknown>pageSize);

    Object.keys(search).forEach(v => {
      if (search[v]) {
        params = params.append(v, search[v]);
      }
    });
    return this.http.get(`${API_URL}/users/list`, {params});
  }

  getUser(username: string): Observable<any> {
    return this.http.get(`${API_URL}/users/find/`, { params: { username: username } });
  }

  createUser(data: any): Observable<any> {
    return this.http.post(`${API_URL}/users/create`, data);
  }

  updateUser(data: any): Observable<any> {
    return this.http.post(`${API_URL}/users/update`, data);
  }

  getPermissionsList(): Observable<any> {
    return this.http.get(`${API_URL}/auth/permissions`);
  }

}
