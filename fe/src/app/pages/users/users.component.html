<form nz-form [formGroup]="searchValidateForm" class="ant-advanced-search-form">
    <div nz-row [nzGutter]="24">
        @for (control of searchControlArray; track control['name']) {
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>{{ control['labelName'] }}</nz-form-label>
                <nz-form-control>
                    <input nz-input [placeholder]="control['labelName']" [formControlName]="control['name']"
                        [attr.id]="control['name']" />
                </nz-form-control>
            </nz-form-item>
        </div>
        }
    </div>
    <div nz-row>
        <div nz-col [nzSpan]="24" class="search-area">
            <button nz-button [nzType]="'primary'" (click)="search()">搜索</button>
            <button nz-button (click)="resetForm()">重置</button>
            <button nz-button [nzType]="'primary'" (click)="showModal()">新建用户</button>
        </div>
    </div>
</form>
<div class="search-result-list">
    <nz-table #columnTable nzShowSizeChanger nzShowQuickJumper [nzData]="usersListData" [nzFrontPagination]="false" nzPaginationType="small" [nzPageSizeOptions]="[10,20,30,40,50]"
        [(nzPageSize)]="pageSize" [(nzPageIndex)]="pageIndex" [nzTotal]="total" [nzLoading]="listLoading"
        (nzPageIndexChange)="onPageIndexChange(pageIndex)" (nzPageSizeChange)="onPageSizeChange(pageSize)"
        [nzScroll]="{ x: '1000px' }">
        <thead>
            <tr>
                <th width="100px">编号</th>
                <th width="100px">用户名</th>
                <th width="100px">姓名</th>
                <th width="180px">创建时间</th>
                <th width="100px">状态</th>
                <th nzRight width="200px">操作</th>
            </tr>
        </thead>
        <tbody>
            @for (data of columnTable.data; track data['username']) {
            <tr>
                <td width="100px">{{(pageIndex - 1) * pageSize + $index + 1}}</td>
                <td width="100px">{{data['username']}}</td>
                <td width="100px">{{data['name']}}</td>
                <td width="180px">{{data['created_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="100px">{{STATUS_TYPE_STR[data['status']]}}</td>
                <td nzRight width="200px">
                    <a (click)="editUser(data)">编辑</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="showEditPermissonsModal(data)">授权</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="showSetUserStatusModal(data)">{{BTN_STATUS[data['status']]}}</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="showDeleteUserModal(data)">删除</a>
                </td>
            </tr>
            }
        </tbody>
    </nz-table>
</div>

<nz-modal [(nzVisible)]="isVisible" nzTitle="新建用户" (nzOnCancel)="handleCancel()" (nzOnOk)="handleOk()"
    [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        <form [formGroup]="userForm" class="user-form">
            <div nz-form-item nz-row class="form-item">
                <div nz-form-label nz-col [nzSpan]="4">
                    <label for="username">用户名:</label>
                </div>
                <div nz-form-control nz-col [nzSpan]="18">
                    <input nz-input formControlName="username" id="username" placeholder="请输入用户名" />
                    @if (userForm.controls['username'].invalid && userForm.controls['username'].touched) {
                    <div class="error">用户名不能为空</div>
                    }
                </div>
            </div>

            <div nz-form-item nz-row class="form-item">
                <div nz-form-label nz-col [nzSpan]="4">
                    <label for="name">姓名:</label>
                </div>
                <div nz-form-control nz-col [nzSpan]="18">
                    <input nz-input formControlName="name" id="name" placeholder="请输入姓名" />
                    @if (userForm.controls['name'].invalid && userForm.controls['name'].touched) {
                    <div class="error">姓名不能为空</div>
                    }
                </div>
            </div>

            <div nz-form-item nz-row class="form-item">
                <div nz-form-label nz-col [nzSpan]="4">
                    <label for="password">密码:</label>
                </div>
                <div nz-form-control nz-col [nzSpan]="18">
                    <input nz-input type="text" formControlName="password" id="password" placeholder="请输入密码" />
                    @if (userForm.controls['password'].invalid && userForm.controls['password'].touched) {
                    <div class="error">密码不能为空</div>
                    }
                    <button nz-button nzType="link" class="random-password-btn"
                        (click)="generateRandomPassword()">使用随机密码</button>
                </div>
            </div>
        </form>
    </div>
</nz-modal>

<nz-modal [(nzVisible)]="SetUserStatusModalVisible" nzTitle="更改用户状态" (nzOnCancel)="hideSetUserStatusModal()"
    (nzOnOk)="SetUserStatus()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        确定要{{BTN_STATUS[curEditUser['status']]}}<span class="user-name">{{ curEditUser.name }}</span> 吗？
    </div>
</nz-modal>

<nz-modal [(nzVisible)]="deleteUserModalVisible" nzTitle="删除用户" (nzOnCancel)="hideDeleteUserModal()"
    (nzOnOk)="deleteUser()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        确定要删除<span class="user-name">{{ curEditUser.name }}</span> 吗？
    </div>
</nz-modal>

<nz-modal [nzVisible]="editPermissionsModalVisible" nzTitle="分配权限" (nzOnCancel)="hideEditPermissonsModal()"
    (nzOnOk)="updateUserPermissions()" [nzOkLoading]="isOkLoading">
    <nz-tree *nzModalContent #nzTreeComponent [nzData]="permissonsNodes" [nzCheckedKeys]="defalutPermissonCheckedKeys"
        nzCheckable nzMultiple></nz-tree>
</nz-modal>