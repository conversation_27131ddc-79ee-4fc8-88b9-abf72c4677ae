import { Component, EventEmitter, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, FormRecord, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { UsersService } from './users.service';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzFormatEmitEvent, NzTreeComponent, NzTreeModule, NzTreeNodeOptions, } from 'ng-zorro-antd/tree';
import { TablePageSizeService } from '../../services/table-page-size.service';


@Component({
  selector: 'app-users',
  imports: [ReactiveFormsModule, CommonModule, NzButtonModule, NzFormModule, NzInputModule, NzIconModule, NzModalModule, NzTableModule, NzDividerModule, NzTreeModule],
  templateUrl: './users.component.html',
  styleUrl: './users.component.scss'
})
export class UsersComponent {
  @ViewChild('nzTreeComponent', { static: false }) nzTreeComponent!: NzTreeComponent;

  STATUS_TYPE_STR: { [key: string]: string } = {
    0: '正常',
    1: '停用'
  }
  BTN_STATUS: { [key: string]: string } = {
    0: '停用',
    1: '启用'
  }
  USER_STATUS: { [key: string]: number } = {
    Active: 0, // 激活状态
    Inactive: 1, // 禁用状态
    Deleted: 3 // 已删除状态
  }

  searchValidateForm: FormGroup;
  searchControlArray = [{
    name: 'username',
    labelName: '用户名'
  }]

  isVisible = false;
  isOkLoading = false;
  userForm: FormGroup;
  curEditUser: any;
  curDelUser: any;


  usersListData = [];

  SetUserStatusModalVisible: boolean = false;
  deleteUserModalVisible: boolean = false;

  permissonsNodes: NzTreeNodeOptions[] = [];
  defalutPermissonCheckedKeys = [];
  editPermissionsModalVisible: boolean = false;

  pageSize: number = 20;
  pageIndex: number = 1;
  total: number = 0;
  listLoading: boolean = false;


  resetForm(): void {
    this.searchValidateForm.reset();
  }

  search(reset = false): void {
    let searchFilter: any = {};
    Object.keys(this.searchValidateForm.controls).forEach(k => {
      searchFilter[k] = this.searchValidateForm.controls[k]['value']
    });
    if (reset) {
      searchFilter = {};
      this.pageIndex = 1;
    }
    this.getUsersList(this.pageIndex, this.pageSize, searchFilter)
  }

  getUsersList(pageIndex: number = 1, pageSize: number = 20, search: { [k: string]: string }): void {
    this.listLoading = true;
    this.usersSerivce.getUsers(pageIndex, pageSize, search).subscribe(res => {
      console.log(res);
      this.usersListData = res.data.list;
      this.total = res.data.total;
      this.pageIndex = res.data.pn;
      this.pageSize = res.data.ps;
      this.listLoading = false;
    });
  }

  showModal(): void {
    this.isVisible = true;
    this.userForm.controls['password'].setValidators([Validators.required]);
  }

  handleOk(): void {
    if (this.userForm.valid) {
      this.isOkLoading = true;
      const formData = this.userForm.value;
      console.log('Form Data:', formData);
      let response: any;
      if (this.curEditUser) {
        response = this.usersSerivce.updateUser(formData);
      } else {
        response = this.usersSerivce.createUser(formData);
      }
      response.subscribe((res: any) => {
        console.log(res);
        if (res.code === 0) {
          this.isVisible = false;
          this.isOkLoading = false;
          this.userForm.reset();
          this.message.success('操作成功');
          this.getUsersList(this.pageIndex, this.pageSize, {});
        } else {
          this.isOkLoading = false;
          this.message.error('操作失败,' + res.msg);
        }
      });
    } else {
      Object.values(this.userForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsTouched();
        }
      });
    }
  }

  handleCancel(): void {
    this.isVisible = false;
    this.userForm.reset();
    this.curEditUser = null;
  }

  generateRandomPassword(): void {
    const randomPassword = Math.random().toString(36).slice(-8);
    this.userForm.patchValue({ password: randomPassword });
  }

  editUser(user: any) {
    this.isVisible = true;
    this.curEditUser = user;
    this.userForm.patchValue({
      username: user.username,
      name: user.name,
      password: '',
    });
    this.userForm.controls['password'].clearValidators();
  }

  showSetUserStatusModal(user: any) {
    this.curEditUser = user;
    this.SetUserStatusModalVisible = true;
  }

  hideSetUserStatusModal() {
    this.SetUserStatusModalVisible = false;
    this.curEditUser = null;
  }
  SetUserStatus() {
    this.isOkLoading = true;
    let status = this.curEditUser.status === this.USER_STATUS['Active'] ? this.USER_STATUS['Inactive'] : this.USER_STATUS['Active'];
    this.usersSerivce.updateUser({ username: this.curEditUser.username, status }).subscribe(res => {
      console.log(res);
      if (res.code === 0) {
        this.message.success('操作成功');
        this.hideSetUserStatusModal();
        this.getUsersList(this.pageIndex, this.pageSize, {});
        this.isOkLoading = false;
      } else {
        this.message.error('操作失败,' + res.msg);
      }
    });
  }

  //删除用户
  showDeleteUserModal(user: any) {
    this.curEditUser = user;
    this.deleteUserModalVisible = true;
  }

  hideDeleteUserModal() {
    this.deleteUserModalVisible = false;
    this.curEditUser = null;
  }

  deleteUser() {
    this.usersSerivce.updateUser({ username: this.curEditUser.username, status: this.USER_STATUS['Deleted'] }).subscribe(res => {
      console.log(res);
      if (res.code === 0) {
        this.message.success('删除成功');
        this.hideDeleteUserModal();
        this.getUsersList(this.pageIndex, this.pageSize, {});
        this.isOkLoading = false;
      } else {
        this.message.error('删除失败,' + res.msg);
      }
    });
  }

  getPermissionsList() {
    this.usersSerivce.getPermissionsList().subscribe(res => {
      this.permissonsNodes = this.parsePermissionsNodes(res)
    });
  }

  parsePermissionsNodes(list: []): NzTreeNodeOptions[] {
    let nodes: NzTreeNodeOptions[] = [];
    for (let i = 0; i < list.length; i++) {
      var data: any = list[i];
      let node: NzTreeNodeOptions = <NzTreeNodeOptions>{};
      node.title = data['name'];
      node.key = data['code'];
      node.isLeaf = true;
      nodes.push(node);
    }
    return nodes;
  }

  showEditPermissonsModal(user: any) {
    this.curEditUser = user;
    this.defalutPermissonCheckedKeys = user.permissions;
    console.log(this.defalutPermissonCheckedKeys)
    this.editPermissionsModalVisible = true;
  }

  hideEditPermissonsModal() {
    this.editPermissionsModalVisible = false;
    this.curEditUser = null;
    this.defalutPermissonCheckedKeys = [];
  }

  updateUserPermissions() {
    let permissions = this.nzTreeComponent.getCheckedNodeList().map((node: any) => node.key);
    this.usersSerivce.updateUser({ username: this.curEditUser.username, permissions: permissions }).subscribe(res => {
      if (res.code === 0) {
        this.message.success('操作成功');
        this.hideEditPermissonsModal();
        this.getUsersList(this.pageIndex, this.pageSize, {});
        this.isOkLoading = false;
      } else {
        this.message.error('操作失败,' + res.msg);
      }
    })
  }

  onPageIndexChange(pageIndex: number) {
    this.pageIndex = pageIndex;
    this.search();
  }

  onPageSizeChange(pageSize: number){
    this.pageSize = pageSize;
    this.tablePageSizeService.setPageSize(pageSize);
    this.search();
  }


  constructor(
    private fb: NonNullableFormBuilder,
    private usersSerivce: UsersService,
    private message: NzMessageService,
    private tablePageSizeService: TablePageSizeService
  ) {
    this.searchValidateForm = this.fb.group({});
    this.userForm = this.fb.group({
      username: ['', [Validators.required]],
      name: ['', [Validators.required]],
      password: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {
    // 初始化页面大小
    this.pageSize = this.tablePageSizeService.getPageSize();

    this.searchControlArray.map((control) => {
      this.searchValidateForm.addControl(control.name, this.fb.control(''));
    });
    this.getUsersList(this.pageIndex, this.pageSize, {});
    this.getPermissionsList();
  }
}
