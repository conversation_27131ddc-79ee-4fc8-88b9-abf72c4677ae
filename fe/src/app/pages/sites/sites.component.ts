import { Component } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { SitesService } from './sites.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzFormModule } from 'ng-zorro-antd/form';
import { CommonModule } from '@angular/common';
import { ImageUploadFormComponent } from '../../components/image-upload-form/image-upload-form.component';
import { NzButtonModule } from 'ng-zorro-antd/button';

@Component({
  selector: 'app-sites',
  imports: [
    ReactiveFormsModule,
    CommonModule,
    NzFormModule,
    ImageUploadFormComponent,
    NzButtonModule,
  ],
  templateUrl: './sites.component.html',
  styleUrl: './sites.component.scss'
})
export class SitesComponent {

  sitesData: any;
  sitesForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private SitesService: SitesService,
    private messageService: NzMessageService
  ) {
    this.sitesForm = this.fb.group({});
  }

  ngOnInit(): void {
    this.initSitesForm();
    this.getSitesData();
  }

  initSitesForm(): void {
    this.sitesForm = this.fb.group({
      title: ['', Validators.required],
      keywords: ['', Validators.required],
      description: ['', Validators.required],
      favicon: ['', Validators.required],
      cnUrl: ['', Validators.required],
      enUrl: ['', Validators.required],
      community: ['', Validators.required],
      copyright: ['', Validators.required],
      recordInfo: ['', Validators.required],
      recordUrl: ['', Validators.required],
      homepageId: [''],
    });
  }

  resetForm() {
    this.sitesForm.reset();
  }
  submitSitesInfo() {
    if(this.sitesData._id) {
      this.sitesForm.addControl('_id', this.fb.control(this.sitesData._id));
    }
    this.SitesService.update(this.sitesForm.value).subscribe(res => {
      console.log(res);
      this.messageService.success('更新成功');
    })
  }


  getSitesData() {
    this.SitesService.getSites().subscribe((res:any) => {
      this.sitesData = res.data || {};
      this.sitesForm.patchValue(this.sitesData);
      console.log(res);
    })
  }


}