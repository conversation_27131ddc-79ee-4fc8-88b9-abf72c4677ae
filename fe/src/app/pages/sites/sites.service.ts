import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_URL } from '../../const';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SitesService {

  constructor(private http: HttpClient) { }

  getSites() {
    return this.http.get(`${API_URL}/sites`);
  }

  update(data: any): Observable<any> {
    return this.http.post(`${API_URL}/sites/update`, data);
  }
}
