<nz-card nzTitle="产品系列管理">
  <!-- 搜索和操作区域 -->
  <div nz-row [nzGutter]="16" style="margin-bottom: 16px;">
    <div nz-col [nzSpan]="8">
      <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
        <input type="text" nz-input placeholder="搜索系列名称" [(ngModel)]="searchName" (keyup.enter)="onSearch()" />
      </nz-input-group>
      <ng-template #suffixIconButton>
        <button nz-button nzType="primary" nzSearch (click)="onSearch()">
          <span nz-icon nzType="search"></span>
        </button>
      </ng-template>
    </div>
    <div nz-col [nzSpan]="16" style="text-align: right;">
      <button nz-button nzType="primary" (click)="showCreateModal()">
        <span nz-icon nzType="plus"></span>
        新增系列
      </button>
    </div>
  </div>

  <!-- 系列列表表格 -->
  <nz-table 
    #basicTable 
    [nzData]="seriesList" 
    [nzLoading]="loading"
    [nzFrontPagination]="false"
    [nzShowPagination]="true"
    [nzPageIndex]="pageIndex"
    [nzPageSize]="pageSize"
    [nzTotal]="total"
    (nzPageIndexChange)="onPageIndexChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)"
    nzSize="middle">
    <thead>
      <tr>
        <th nzWidth="60px">序号</th>
        <th>系列名称</th>
        <th>描述</th>
        <th nzWidth="180px">创建时间</th>
        <th nzWidth="180px">更新时间</th>
        <th nzWidth="150px" nzRight>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let series of seriesList; let i = index">
        <td>{{ (pageIndex - 1) * pageSize + i + 1 }}</td>
        <td>{{ series.name }}</td>
        <td>{{ series.description || '-' }}</td>
        <td>{{ formatDate(series.created_at) }}</td>
        <td>{{ formatDate(series.updated_at) }}</td>
        <td nzRight>
          <nz-space>
            <button *nzSpaceItem nz-button nzType="link" nzSize="small" (click)="showEditModal(series)">
              <span nz-icon nzType="edit"></span>
              编辑
            </button>
            <button 
              *nzSpaceItem 
              nz-button 
              nzType="link" 
              nzDanger 
              nzSize="small"
              nz-popconfirm
              nzPopconfirmTitle="确定要删除这个系列吗？"
              nzPopconfirmPlacement="topRight"
              (nzOnConfirm)="deleteSeries(series._id)">
              <span nz-icon nzType="delete"></span>
              删除
            </button>
          </nz-space>
        </td>
      </tr>
    </tbody>
    <!-- 移除了页面底部的分页控件，使用表格内置的分页控件 -->
  </nz-table>
</nz-card>

<!-- 新增/编辑系列模态框 -->
<nz-modal
  [(nzVisible)]="isModalVisible"
  [nzTitle]="modalTitle"
  [nzOkText]="editingSeriesId ? '更新' : '创建'"
  nzCancelText="取消"
  (nzOnOk)="handleModalOk()"
  (nzOnCancel)="handleModalCancel()">
  
  <ng-container *nzModalContent>
    <form nz-form [formGroup]="seriesForm" nzLayout="vertical">
      <nz-form-item>
        <nz-form-label nzRequired>系列名称</nz-form-label>
        <nz-form-control nzErrorTip="请输入系列名称（最多100个字符）">
          <input nz-input formControlName="name" placeholder="请输入系列名称" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label>描述</nz-form-label>
        <nz-form-control nzErrorTip="描述最多500个字符">
          <textarea 
            nz-input 
            formControlName="description" 
            placeholder="请输入系列描述（可选）"
            [nzAutosize]="{ minRows: 3, maxRows: 6 }">
          </textarea>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-container>
</nz-modal>