import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface ProductSeries {
  _id: string;
  name: string;
  description?: string;
  created_at: number;
  updated_at: number;
}

export interface ProductSeriesListResponse {
  code: number;
  data: {
    list: ProductSeries[];
    total: number;
    ps: number;
    pn: number;
  };
  msg: string;
}

export interface ProductSeriesResponse {
  code: number;
  data: ProductSeries;
  msg: string;
}

export interface CreateProductSeriesDto {
  name: string;
  description?: string;
}

export interface UpdateProductSeriesDto {
  name?: string;
  description?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ProductSeriesService {
  private apiUrl = '/product-documents/series';  // 更新为整合后的接口地址

  constructor(private http: HttpClient) {}

  // 获取系列列表
  getSeriesList(pn: number = 1, ps: number = 10, name?: string): Observable<ProductSeriesListResponse> {
    let params = new HttpParams()
      .set('pn', pn.toString())
      .set('ps', ps.toString());
    
    if (name) {
      params = params.set('name', name);
    }

    return this.http.get<ProductSeriesListResponse>(`${this.apiUrl}/list`, { params });
  }

  // 获取简单系列列表（用于下拉选择）
  getSimpleSeriesList(): Observable<{ code: number; data: { _id: string; name: string }[]; msg: string }> {
    return this.http.get<{ code: number; data: { _id: string; name: string }[]; msg: string }>(`${this.apiUrl}/simple`);
  }

  // 获取单个系列
  getSeries(id: string): Observable<ProductSeriesResponse> {
    return this.http.get<ProductSeriesResponse>(`${this.apiUrl}/${id}`);
  }

  // 创建系列
  createSeries(data: CreateProductSeriesDto): Observable<{ code: number; msg: string; data?: any }> {
    return this.http.post<{ code: number; msg: string; data?: any }>(`${this.apiUrl}/create`, data);
  }

  // 更新系列
  updateSeries(id: string, data: UpdateProductSeriesDto): Observable<{ code: number; msg: string }> {
    return this.http.patch<{ code: number; msg: string }>(`${this.apiUrl}/${id}`, data);
  }

  // 删除系列
  deleteSeries(id: string): Observable<{ code: number; msg: string }> {
    return this.http.delete<{ code: number; msg: string }>(`${this.apiUrl}/${id}`);
  }
}