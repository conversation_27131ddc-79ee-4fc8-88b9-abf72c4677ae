import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { NzDividerModule } from 'ng-zorro-antd/divider';

import { ProductSeriesService, ProductSeries } from './product-series.service';

@Component({
  selector: 'app-product-series',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzInputModule,
    NzFormModule,
    NzModalModule,
    NzPaginationModule,
    NzPopconfirmModule,
    NzIconModule,
    NzCardModule,
    NzSpaceModule,
    NzDividerModule,
  ],
  templateUrl: './product-series.component.html',
  styleUrls: ['./product-series.component.scss']
})
export class ProductSeriesComponent implements OnInit {
  seriesList: ProductSeries[] = [];
  loading = false;
  total = 0;
  pageIndex = 1;
  pageSize = 10;
  searchName = '';

  // 模态框
  isModalVisible = false;
  modalTitle = '';
  seriesForm: FormGroup;
  editingSeriesId: string | null = null;

  constructor(
    private productSeriesService: ProductSeriesService,
    private message: NzMessageService,
    private fb: FormBuilder
  ) {
    this.seriesForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(500)]]
    });
  }

  ngOnInit(): void {
    this.loadSeriesList();
  }

  loadSeriesList(): void {
    this.loading = true;
    this.productSeriesService.getSeriesList(this.pageIndex, this.pageSize, this.searchName)
      .subscribe({
        next: (response) => {
          if (response.code === 0) {
            this.seriesList = response.data.list;
            this.total = response.data.total;
          } else {
            this.message.error(response.msg || '获取系列列表失败');
          }
          this.loading = false;
        },
        error: (error) => {
          this.message.error('获取系列列表失败');
          this.loading = false;
        }
      });
  }

  onSearch(): void {
    this.pageIndex = 1;
    this.loadSeriesList();
  }

  onPageIndexChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadSeriesList();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageIndex = 1;
    this.loadSeriesList();
  }

  showCreateModal(): void {
    this.modalTitle = '新增系列';
    this.editingSeriesId = null;
    this.seriesForm.reset();
    this.isModalVisible = true;
  }

  showEditModal(series: ProductSeries): void {
    this.modalTitle = '编辑系列';
    this.editingSeriesId = series._id;
    this.seriesForm.patchValue({
      name: series.name,
      description: series.description || ''
    });
    this.isModalVisible = true;
  }

  handleModalOk(): void {
    if (this.seriesForm.valid) {
      const formData = this.seriesForm.value;
      
      if (this.editingSeriesId) {
        // 编辑
        this.productSeriesService.updateSeries(this.editingSeriesId, formData)
          .subscribe({
            next: (response) => {
              if (response.code === 0) {
                this.message.success('更新成功');
                this.isModalVisible = false;
                this.loadSeriesList();
              } else {
                this.message.error(response.msg || '更新失败');
              }
            },
            error: (error) => {
              this.message.error('更新失败');
            }
          });
      } else {
        // 新增
        this.productSeriesService.createSeries(formData)
          .subscribe({
            next: (response) => {
              if (response.code === 0) {
                this.message.success('创建成功');
                this.isModalVisible = false;
                this.loadSeriesList();
              } else {
                this.message.error(response.msg || '创建失败');
              }
            },
            error: (error) => {
              this.message.error('创建失败');
            }
          });
      }
    } else {
      Object.values(this.seriesForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleModalCancel(): void {
    this.isModalVisible = false;
  }

  deleteSeries(id: string): void {
    this.productSeriesService.deleteSeries(id)
      .subscribe({
        next: (response) => {
          if (response.code === 0) {
            this.message.success('删除成功');
            this.loadSeriesList();
          } else {
            this.message.error(response.msg || '删除失败');
          }
        },
        error: (error) => {
          this.message.error('删除失败');
        }
      });
  }

  formatDate(timestamp: number): string {
    return new Date(timestamp).toLocaleString('zh-CN');
  }
}
