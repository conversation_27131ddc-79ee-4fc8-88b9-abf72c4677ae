import { CommonModule } from '@angular/common';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { PagesLibraryService } from './pages-library.service';
import { ComponentsLibraryService } from '../components-library/components-library.service';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ClipboardModule } from 'ngx-clipboard';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { TablePageSizeService } from '../../services/table-page-size.service';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzImageModule } from 'ng-zorro-antd/image';
import { CdkDragDrop, CdkDropList, CdkDrag, moveItemInArray, CdkDragMove } from '@angular/cdk/drag-drop';
import { NzTabsModule } from 'ng-zorro-antd/tabs';

@Component({
  selector: 'app-pages-library',
  imports: [
    ReactiveFormsModule,
    CommonModule,
    NzButtonModule,
    NzFormModule,
    ClipboardModule,
    NzToolTipModule,
    NzInputModule,
    NzIconModule,
    NzModalModule,
    NzTableModule,
    NzDividerModule,
    NzTreeModule,
    NzCardModule,
    NzPopconfirmModule,
    NzSelectModule,
    NzDrawerModule,
    NzImageModule,
    CdkDropList,
    CdkDrag,
    NzTabsModule
  ],
  templateUrl: './pages-library.component.html',
  styleUrl: './pages-library.component.scss'
})
export class PagesLibraryComponent {

  // 常量
  STATUS_TYPE_STR: { [key: string]: string } = {
    0: '展示中',
    1: '未使用'
  };
  BTN_STATUS: { [key: string]: string } = {
    0: '停用',
    1: '启用'
  };
  PAGE_STATUS: { [key: string]: number } = {
    Active: 0, // 激活状态
    Inactive: 1, // 禁用状态
    Deleted: 3 // 已删除状态
  }
  RESOURCE_TYPE_OPTIONS = [
    {
      label: '文字',
      value: 'text'
    },
    {
      label: '图片',
      value: 'image'
    },
    {
      label: '视频',
      value: 'video'
    },
    {
      label: '文件',
      value: 'file'
    },
    {
      label: '调色盘',
      value: 'palette'
    }
  ];

  ELEMENT_TYPE_OPTIONS = [
    {
      label: '单一',
      value: 'single'
    },
    {
      label: '多个',
      value: 'array'
    },
  ]

  // 变量
  searchValidateForm: FormGroup;
  searchControlArray = [
    {
      name: 'name',
      labelName: '页面名称'
    }
  ];
  pageIndex: number = 1;
  pageSize: number = 20;
  listLoading: boolean = false;
  listData: any;
  total: number = 0;
  curPage: any = null;
  pageForm: FormGroup;
  pageInfoModalVisiable: boolean = false;
  isOkLoading: boolean = false;
  deletePageModalVisible: boolean = false;
  curEditPage: any = null;
  SetPageStatusModalVisible: boolean = false;

  constructor(
    private fb: FormBuilder,
    private PagesLibraryService: PagesLibraryService,
    private ComponentsLibraryService: ComponentsLibraryService,
    private messageService: NzMessageService,
    private tablePageSizeService: TablePageSizeService
  ) {
    this.searchValidateForm = this.fb.group({});
    this.pageForm = this.fb.group({});
  }

  // 生命周期钩子
  ngOnInit(): void {
    // 初始化页面大小
    this.pageSize = this.tablePageSizeService.getPageSize();

    this.searchControlArray.map((control) => {
      this.searchValidateForm.addControl(control.name, this.fb.control(''));
    });
    this.getList(this.pageIndex, this.pageSize, {});
    this.getComponentList();
    this.getGlobalList();
    this.initPageForm();
  }

  // #region 搜索
  resetForm(): void {
    this.searchValidateForm.reset();
  }

  search(reset = false): void {
    let searchFilter: any = {};
    Object.keys(this.searchValidateForm.controls).forEach(k => {
      searchFilter[k] = this.searchValidateForm.controls[k]['value'];
    });
    if (reset) {
      searchFilter = {};
      this.pageIndex = 1;
    }
    this.getList(this.pageIndex, this.pageSize, searchFilter);
  }

  onPageIndexChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.search();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.tablePageSizeService.setPageSize(pageSize);
    this.search();
  }

  getList(pageIndex: number = 1, pageSize: number = 20, search: { [k: string]: string }): void {
    this.listLoading = true;
    this.PagesLibraryService.getList(pageIndex, pageSize, search).subscribe(res => {
      console.log(res);
      this.listData = res.data.list;
      this.total = res.data.total;
      this.pageIndex = res.data.pn;
      this.pageSize = res.data.ps;
      this.listLoading = false;
    });
  }



  // #endregion

  // #region: 页面模板组合
  componentList: any;
  globalList: any;
  pageModelDrawerVisible: boolean = false;

  showPageModelDrawer(): void {
    this.pageModelDrawerVisible = true;
  }
  closePageModelDrawer(): void {
    this.pageModelDrawerVisible = false;
    if (this.curPage) {
      this.initPageForm();
      this.pageComponentList = [];
      this.curPage = null;
    }
  }
  getComponentList(): void {
    this.ComponentsLibraryService.getList(1, 1000, {}).subscribe(res => {
      this.componentList = res.data.list;
    });
  }

  getGlobalList(): void {
    this.ComponentsLibraryService.getGlobalComponents(1, 1000, {}).subscribe(res => {
      this.globalList = res.data.list;
    });
  }

  @ViewChild('scrollContainer') scrollContainer!: ElementRef;
  pageComponentList: any[] = [];
  drop(event: CdkDragDrop<any[]>): void {
    moveItemInArray(this.pageComponentList, event.previousIndex, event.currentIndex);
  }

  addToPageComponentList(item: any): void {
    this.pageComponentList.push(item);
    // 数据更新后滚动到底部
    setTimeout(() => {
      const container = this.scrollContainer.nativeElement as HTMLElement;
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      })
    }, 100);
  }

  deleteComponetFromPageComponetList(index: number): void {
    this.pageComponentList.splice(index, 1);
  }

  copySuccess(): void {
    this.messageService.success('复制成功');
  }


  // #endregion


  submitPageInfo(): void {
    if (this.pageForm.invalid) {
      // 递归检查所有子表单组
      this.validateAllFormFields(this.pageForm);
      this.messageService.error('请检查表单填写是否正确！');
      return;
    }
    if (this.curPage) {
      this.pageForm.addControl('_id', this.fb.control(this.curPage._id));
      this.pageForm.controls['componentList'].setValue(this.pageComponentList);
      this.PagesLibraryService.updatetConfig(this.pageForm.value).subscribe(res => {
        this.closePageModelDrawer();
        this.getList(this.pageIndex, this.pageSize, {});
      });
    } else {
      this.pageForm.controls['componentList'].setValue(this.pageComponentList);
      this.PagesLibraryService.createConfig(this.pageForm.value).subscribe(res => {
        this.closePageModelDrawer();
        this.getList(this.pageIndex, this.pageSize, {});
      });
    }
    this.pageForm.reset();
    this.pageComponentList = [];
  }

  editPage(page: any): void {
    this.initPageForm();
    this.curPage = page;
    this.pageModelDrawerVisible = true;
    console.log(this.curPage)
    this.pageComponentList = [...this.curPage.componentList];
    this.pageForm.patchValue(page);
  }

  initPageForm(): void {
    this.pageForm = this.fb.group({
      name: ['', Validators.required],
      label: ['', Validators.required],
      componentList: [[]],
      remark: ['']
    });
  }

  showDeletePageModal(page: any): void {
    this.curEditPage = page;
    this.deletePageModalVisible = true;
  }

  hideDeletePageModal(): void {
    this.deletePageModalVisible = false;
    this.curEditPage = null;
  }

  deletePage(): void {
    this.isOkLoading = true;
    this.PagesLibraryService.deleteConfig(this.curEditPage._id).subscribe(res => {
      console.log(res);
      if (res.code === 0) {
        this.messageService.success('删除成功');
        this.hideDeletePageModal();
        this.getList(this.pageIndex, this.pageSize, {});
        this.isOkLoading = false;
      } else {
        this.messageService.error('删除失败,' + res.msg);
      }
    });
  }

  // 递归验证所有表单字段
  private validateAllFormFields(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(field => {
      const control = formGroup.get(field);
      if (control instanceof FormArray) {
        control.controls.forEach((c: AbstractControl) => {
          if (c instanceof FormGroup) {
            this.validateAllFormFields(c);
          } else {
            c.markAsDirty();
            c.updateValueAndValidity({ onlySelf: true });
          }
        });
      } else if (control instanceof FormGroup) {
        this.validateAllFormFields(control);
      } else {
        control?.markAsDirty();
        control?.updateValueAndValidity({ onlySelf: true });
      }
    });
  }
}


