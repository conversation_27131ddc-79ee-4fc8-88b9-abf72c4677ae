<form nz-form [formGroup]="searchValidateForm" class="ant-advanced-search-form">
    <div nz-row [nzGutter]="24">
        @for (control of searchControlArray; track control['name']) {
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>{{ control['labelName'] }}</nz-form-label>
                <nz-form-control>
                    <input nz-input [placeholder]="control['labelName']" [formControlName]="control['name']"
                        [attr.id]="control['name']" />
                </nz-form-control>
            </nz-form-item>
        </div>
        }
    </div>
    <div nz-row>
        <div nz-col [nzSpan]="24" class="search-area">
            <button nz-button [nzType]="'primary'" (click)="search()">搜索</button>
            <button nz-button (click)="resetForm()">重置</button>
            <button nz-button [nzType]="'primary'" (click)="showPageModelDrawer()">新增模版</button>
        </div>
    </div>
</form>

<div class="search-result-list">
    <nz-table #columnTable nzShowSizeChanger nzShowQuickJumper [nzData]="listData" [nzFrontPagination]="false"
        nzPaginationType="small" [nzPageSizeOptions]="[10,20,30,40,50]" [(nzPageSize)]="pageSize"
        [(nzPageIndex)]="pageIndex" [nzTotal]="total" [nzLoading]="listLoading"
        (nzPageIndexChange)="onPageIndexChange(pageIndex)" (nzPageSizeChange)="onPageSizeChange(pageSize)"
        [nzScroll]="{ x: '1000px' }">
        <thead>
            <tr>
                <th width="50px">编号</th>
                <th width="100px">名称</th>
                <th width="180px">模版label</th>
                <th width="180px">创建时间</th>
                <th width="100px">ID</th>
                <th nzRight width="100px">操作</th>
            </tr>
        </thead>
        <tbody>
            @for (data of columnTable.data; track data['_id']) {
            <tr>
                <td width="50px">{{(pageIndex - 1) * pageSize + $index + 1}}</td>
                <td width="100px">{{data['name']}}</td>
                <td width="100px">{{data['label']}}</td>
                <td width="180px">{{data['created_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="100px">
                    <span ngxClipboard [cbContent]="data['_id']" (cbOnSuccess)="copySuccess()"
                        style="cursor: pointer; color: blue; text-decoration: underline;" nz-tooltip
                        nzTooltipTitle="点击复制ID">{{data['_id']}}</span>
                </td>
                <td nzRight width="100px">
                    <a (click)="editPage(data)">编辑</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="showDeletePageModal(data)">删除</a>
                </td>
            </tr>
            }
        </tbody>
    </nz-table>
</div>

<nz-drawer [nzClosable]="true" [nzVisible]="pageModelDrawerVisible" nzPlacement="right" nzTitle="模版信息"
    [nzWidth]="'100vw'" [nzFooter]="footerTpl" (nzOnClose)="closePageModelDrawer()">
    <div *nzDrawerContent class="page-model-edit-drawer">
        <div class="component-list">
            <nz-tabset [nzAnimated]="false">
                <nz-tab nzTitle="模版组件">
                    <div class="component-list-container">
                        <div class="component-item" *ngFor="let item of componentList; let i = index"
                            (click)="addToPageComponentList(item)">
                            <div class="item-name">{{item.name}}</div>
                            <img class="item-img" [src]="item.image" alt="" />
                            <div class="item-remark">{{item.remark}}</div>
                        </div>
                    </div>

                </nz-tab>
                <nz-tab nzTitle="全局组件">
                    <div class="component-list-container">
                        <div class="component-item" *ngFor="let item of globalList; let i = index"
                            (click)="addToPageComponentList(item)">
                            <div class="item-name">{{item.name}}</div>
                            <img class="item-img" [src]="item.image" alt="" />
                            <div class="item-remark">{{item.remark}}</div>
                        </div>
                    </div>
                </nz-tab>
            </nz-tabset>
        </div>
        <div class="page-model" #scrollContainer>
            <div class="modal-content">
                <form nz-form [formGroup]="pageForm">
                    <!-- Page Name -->
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="7" nzRequired>模版名称</nz-form-label>
                        <nz-form-control [nzSpan]="17">
                            <!-- 限制只能输入大小写字母 -->
                            <input nz-input formControlName="name" placeholder="请输入模版名称(只能大小写字母)"
                                pattern="^[A-Za-z0-9\-]+$">
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="7" nzRequired>模版别名</nz-form-label>
                        <nz-form-control [nzSpan]="17">
                            <input nz-input formControlName="label" placeholder="请输入模版别名">
                        </nz-form-control>
                    </nz-form-item>
                    <!-- Remark -->
                    <nz-divider nzText="备注"></nz-divider>
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="7">备注</nz-form-label>
                        <nz-form-control [nzSpan]="17">
                            <textarea style="height: 100px;" nz-input formControlName="remark" placeholder="请输入备注">
                                </textarea>
                        </nz-form-control>
                    </nz-form-item>
                </form>
            </div>
            <nz-divider nzText="页面组件"></nz-divider>
            <div class="page-component-list" cdkDropList (cdkDropListDropped)="drop($event)">
                <div class="page-component-item" cdkDrag *ngFor="let item of pageComponentList; let i = index">
                    <div class="component-name-tag">{{ item.name }}</div>
                    <img [src]="item.image" alt="" class="component-img">
                    <button nz-button class="component-remove" nzType="primary" nzShape="circle" nzSize="large"
                        (click)="deleteComponetFromPageComponetList(i)">
                        <nz-icon nzType="minus" />
                    </button>
                </div>
            </div>
        </div>
    </div>
    <ng-template #footerTpl>
        <div style="float: right">
            <button nz-button style="margin-right: 8px;" (click)="closePageModelDrawer()">Cancel</button>
            <button nz-button nzType="primary" (click)="submitPageInfo()">提交</button>
        </div>
    </ng-template>
</nz-drawer>

<nz-modal [(nzVisible)]="deletePageModalVisible" nzTitle="删除模版" (nzOnCancel)="hideDeletePageModal()"
    (nzOnOk)="deletePage()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        @if (curEditPage) {
        确定要删除<span class="page-name">{{ curEditPage.name }}</span> 吗？
        }
    </div>
</nz-modal>