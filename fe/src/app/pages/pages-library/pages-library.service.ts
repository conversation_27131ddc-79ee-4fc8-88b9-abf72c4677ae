import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_URL } from '../../const';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PagesLibraryService {


  constructor(private http: HttpClient) { }

  // 首页数据获取
  getList(pageIndex: number, pageSize: number, filters: { [k: string]: string }): Observable<any> {
    let params = new HttpParams()
      .append('pn', <string><unknown>pageIndex)
      .append('ps', <string><unknown>pageSize);

    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params = params.append(key, filters[key]);
      }
    });
    return this.http.get(`${API_URL}/page-templates/list`, { params });
  }


  // 创建首页配置项
  createConfig(config: any): Observable<any> {
    return this.http.post(`${API_URL}/page-templates/create`, config);
  }

  // 更新首页配置项
  updatetConfig(config: any): Observable<any> {
    return this.http.post(`${API_URL}/page-templates/update`, config);
  }

  startPage(config: any): Observable<any> {
    return this.http.post(`${API_URL}/page-templates/startPage`, config);
  }

  // 删除首页配置项
  deleteConfig(id: string): Observable<any> {
    return this.http.post(`${API_URL}/page-templates/delete`, { id: id });
  }
}
