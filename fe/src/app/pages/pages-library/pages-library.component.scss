.ant-advanced-search-form {
    padding: 24px;
    background: #fbfbfb;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
}

button {
    margin-left: 8px;
}

.search-result-list {
    margin-top: 16px;
    border: 1px dashed #e9e9e9;
    border-radius: 6px;
    background-color: #fafafa;
    min-height: 200px;
    text-align: center;
    padding: 0 10px;
}

.page-model-edit-drawer {
    display: flex;
    height: 100%;

    .component-list {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        align-content: flex-start;
        gap: 20px;
        width: 70%;
        height: 100%;
        border-right: solid #999 1px;
        overflow-y: auto;
        flex: 0 0 auto;

        .component-list-container {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            gap: 24px;
        }

        .component-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
            width: 31%;
            border: solid #999 1px;
            border-radius: 12px;
            cursor: pointer;

            .item-name {
                font-size: 20px;
                line-height: 24px;
                padding: 4px 8px;
                word-wrap: break-word;
            }

            .item-img {
                width: 100%;
            }

            .item-remark {
                font-size: 14px;
                color: #999;
                margin-top: auto;
                padding: 4px 8px;
            }
        }
    }

    .page-model {
        width: 50%;
        padding: 0 0 0 24px;
        overflow: auto;

        .page-component-list {
            display: flex;
            flex-direction: column;
            gap: 40px;

            .page-component-item {
                display: flex;
                position: relative;
                min-height: 100px;
                border: 1px dashed #999;
                cursor: move;

                &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    transition: all 0.3s ease-in-out;
                }

                &:hover::before {
                    background: rgba(0, 0, 0, 0.3);

                }

                .component-img {
                    width: 100%;
                }
                
                .component-name-tag {
                    position: absolute;
                    top: 8px;
                    left: 8px;
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    z-index: 10;
                }

                .component-remove {
                    display: none;
                    position: absolute;
                    top: 24px;
                    right: 24px;
                    cursor: pointer;
                }

                &:hover .component-remove {
                    display: block;
                }

            }


        }
    }
}

.cdk-drag-preview {
    opacity: 0.6;
    border: none;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
        0 8px 10px 1px rgba(0, 0, 0, 0.14),
        0 3px 14px 2px rgba(0, 0, 0, 0.12);

    .component-remove {
        display: none;
    }

    ;

    .component-img {
        width: 100%;
    }
}

.cdk-drag-placeholder {
    opacity: 0;
}

.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .page-component-item:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.modal-content {

    .form-group-container {
        border: 1px dashed #9E9E9E;
        margin-bottom: 10px;
        // padding-bottom: 10px;
        position: relative;
    }

    .form-item-title {
        font-size: 16px;
        text-align: center;
        margin-bottom: 10px;
    }

    .delete-element-list {
        position: absolute;
        top: 5px;
        right: 5px;
    }

    .form-item {
        margin-bottom: 10px;
        position: relative;

        input {
            margin-bottom: 8px;
        }

        nz-select {
            margin-bottom: 8px;
        }

        .delete-form-item-btn {
            position: absolute;
            right: 10px;
        }

        .param-input {
            width: 50px;
        }

        &.count-type {
            margin-top: 10px;
        }

    }

    .add-form-item-btn {
        display: block;
        margin: 0 auto;
    }
}