import { Component, ElementRef, ViewChild } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PagesLibraryService } from '../pages-library/pages-library.service';
import { PagesManageService } from '../pages-manage/pages-manage.service';
import { TablePageSizeService } from '../../services/table-page-size.service';
import { CommonModule } from '@angular/common';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTableModule } from 'ng-zorro-antd/table';
import { ClipboardModule } from 'ngx-clipboard';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';


import { NzTreeFlatDataSource, NzTreeFlattener, NzTreeViewModule } from 'ng-zorro-antd/tree-view';
import { SelectionModel } from '@angular/cdk/collections';
import { FlatTreeControl } from '@angular/cdk/tree';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzImageModule } from 'ng-zorro-antd/image';
import { ImageUploadFormComponent } from '../../components/image-upload-form/image-upload-form.component';
import { VideoUploadFormComponent } from '../../components/video-upload-form/video-upload-form.component';
import { FileUploadFormComponent } from '../../components/file-upload-form/file-upload-form.component';
import { NzTabsModule, NZ_TAB_SET } from 'ng-zorro-antd/tabs';
import { Al_home } from '../../const';

import { QuillModule } from 'ngx-quill';
// 添加组件库服务导入
import { ComponentsLibraryService } from '../components-library/components-library.service';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';

@Component({
  selector: 'app-pages-manage',
  imports: [
    ReactiveFormsModule,
    CommonModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzTableModule,
    ClipboardModule,
    NzDividerModule,
    NzDrawerModule,
    NzModalModule,
    NzTreeViewModule,
    NzSelectModule,
    NzTabsModule,
    ImageUploadFormComponent,
    VideoUploadFormComponent,
    FileUploadFormComponent,
    NzIconModule,
    NzToolTipModule,
    FormsModule,
    QuillModule,
    CdkDropList,
    CdkDrag,
    NzSpinModule,
    NzPaginationModule
  ],
  providers: [{ provide: NZ_TAB_SET, useValue: {} }],
  templateUrl: './pages-manage.component.html',
  styleUrl: './pages-manage.component.scss'
})
export class PagesManageComponent {

  constructor(
    private fb: FormBuilder,
    private PagesLibraryService: PagesLibraryService,
    private PagesManageService: PagesManageService,
    private messageService: NzMessageService,
    private tablePageSizeService: TablePageSizeService,
    // 注入组件库服务
    private ComponentsLibraryService: ComponentsLibraryService
  ) {
    this.searchValidateForm = this.fb.group({});
    this.pageModelForm = this.fb.group({});
    this.elementDataForm = this.fb.group({});
    this.elementDataItemForm = this.fb.group({});
  }

  // 历史记录管理
  private history: any[] = [];
  private historyIndex: number = -1;
  private readonly MAX_HISTORY_LENGTH: number = 50; // 最大历史记录长度

  al_home = Al_home;

  RESOURCE_TYPE_OPTIONS = [
    {
      label: '文字',
      value: 'text'
    },
    {
      label: '选项',
      value: 'select'
    },
    {
      label: '图片',
      value: 'image'
    },
    {
      label: '视频',
      value: 'video'
    },
    {
      label: '富文本',
      value: 'richText'
    },
    {
      label: '文件',
      value: 'file'
    },
    {
      label: '列表',
      value: 'list'
    },
    {
      label: '对象',
      value: 'object'
    },
    {
      label: '调色盘',
      value: 'palette'
    }
  ];

  PAGE_TYPE_OPTIONS = [
    {
      label: '产品',
      value: 'product'
    },
    {
      label: '行业应用',
      value: 'industry'
    },
    {
      label: '其他',
      value: 'other'
    }
  ];
  
  THEME_OPTIONS = [
    {
      label: '亮色主题',
      value: 'light'
    },
    {
      label: '暗色主题',
      value: 'dark'
    }
  ];
  
  PAGE_TYPE_STR: { [key: string]: string } = {
    product: '产品',
    industry: '行业应用',
    other: '其他'
  };


  // 变量
  searchValidateForm: FormGroup;
  searchControlArray = [
    {
      name: 'name',
      labelName: '页面名称'
    }
  ];
  pageIndex: number = 1;
  pageSize: number = 20;
  listLoading: boolean = false;
  listData: any;
  total: number = 0;
  curPage: any = null;
  curEditPage: any = null;
  // curEditComponent: any = null;
  // pageModelDrawerVisible: boolean = false;
  // pageModelPreviewDrawerVisible: boolean = false;
  deletePageModalVisible: boolean = false;
  publishConfirmModalVisible: boolean = false;
  isOkLoading: boolean = false;
  pageModelForm: FormGroup;
  publishTargetId: string = ''; // 待发布的页面ID
  selectedPageType: string = '';
  
  // 组件选择相关变量
  componentSelectDrawerVisible: boolean = false;
  allComponentsList: any[] = [];
  componentsPageIndex: number = 1;
  componentsPageSize: number = 10;
  componentsTotal: number = 0;
  componentsListLoading: boolean = false;
  
  // #endregion
  // 生命周期钩子
  ngOnInit(): void {
    // 初始化页面大小
    this.pageSize = this.tablePageSizeService.getPageSize();

    this.searchControlArray.map((control) => {
      this.searchValidateForm.addControl(control.name, this.fb.control(''));
    });
    this.getList(this.pageIndex, this.pageSize, {});
    this.getPageModelList();
    this.initPageModelForm();
    this.initElementDataForm();
    this.initElementDataItemForm();
  }

  // #region 搜索
  resetForm(): void {
    this.searchValidateForm.reset();
    this.selectedPageType = '';
    this.search(true);
  }

  search(reset = false): void {
    let searchFilter: any = {};
    Object.keys(this.searchValidateForm.controls).forEach(k => {
      searchFilter[k] = this.searchValidateForm.controls[k]['value'];
    });
    if (reset) {
      searchFilter = {};
      this.pageIndex = 1;
    }
    if (this.selectedPageType) {
      searchFilter['pageType'] = this.selectedPageType;
    }
    this.getList(this.pageIndex, this.pageSize, searchFilter);
  }

  onPageIndexChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.search();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.tablePageSizeService.setPageSize(pageSize);
    this.search();
  }

  getList(pageIndex: number = 1, pageSize: number = 20, search: { [k: string]: string }): void {
    this.listLoading = true;
    this.PagesManageService.getList(pageIndex, pageSize, search).subscribe(res => {
      this.listData = res.data.list;
      this.total = res.data.total;
      this.pageIndex = res.data.pn;
      this.pageSize = res.data.ps;
      this.listLoading = false;
    });
  }

  // 发布草稿
  publishDraft(id: string): void {
    this.PagesManageService.publishDraft(id).subscribe(res => {
      if (res.code === 0) {
        this.messageService.success('发布成功');
        this.search(); // 重新加载列表数据
      } else {
        this.messageService.error(res.msg || '发布失败');
      }
    });
  }

  // #region: 页面模板组合
  componentList: any;
  pageModelDrawerVisible: boolean = false;

  showPageModelDrawer(): void {
    this.pageModelDrawerVisible = true;
    this.pageModelPreviewDrawerVisible = false;
    this.curEditComponent = null;
  }
  closePageModelDrawer(): void {
    this.pageModelDrawerVisible = false;
    if (this.curPage) {
      this.initPageModelForm();
      this.pageComponentList = [];
      this.curPage = null;
    }
  }
  getPageModelList(): void {
    this.PagesLibraryService.getList(1, 1000, {}).subscribe(res => {
      this.componentList = res.data.list;
    });
  }

  // 获取所有组件的方法
  getAllComponentsList(pageIndex: number = 1, pageSize: number = 10): void {
    this.componentsListLoading = true;
    this.ComponentsLibraryService.getList(pageIndex, pageSize, {}).subscribe(res => {
      this.allComponentsList = res.data.list;
      this.componentsTotal = res.data.total;
      this.componentsPageIndex = res.data.pn;
      this.componentsPageSize = res.data.ps;
      this.componentsListLoading = false;
    });
  }

   // 组件分页相关方法
  onComponentsPageIndexChange(pageIndex: number): void {
    this.componentsPageIndex = pageIndex;
    this.getAllComponentsList(this.componentsPageIndex, this.componentsPageSize);
  }

  onComponentsPageSizeChange(pageSize: number): void {
    this.componentsPageSize = pageSize;
    this.componentsPageIndex = 1;
    this.getAllComponentsList(this.componentsPageIndex, this.componentsPageSize);
  }

  // 添加显示组件选择抽屉的方法
  showComponentSelectDrawer(): void {
    this.componentSelectDrawerVisible = true;
    this.getAllComponentsList(this.componentsPageIndex, this.componentsPageSize);
  }

  // 添加隐藏组件选择抽屉的方法
  hideComponentSelectDrawer(): void {
    this.componentSelectDrawerVisible = false;
  }

  // 添加选择组件并插入到页面中的方法
  selectComponentAndInsert(component: any): void {
    // 复制组件并添加到当前页面组件列表中
    const componentCopy = this.copyObject(component);
    this.pageComponentList.push(componentCopy);
    
    // 保存状态到历史记录
    this.saveState();
    
    // 关闭组件选择抽屉
    // this.hideComponentSelectDrawer();
    
    this.messageService.success(`组件"${component.label}"已添加到页面中`);
  }

  pageComponentList: any[] = [];
  curSelectPageModel: any = {};
  pageModelPreviewDrawerVisible: boolean = false;
  selPageModel(item: any): void {
    this.curSelectPageModel = item;
    this.pageModelPreviewDrawerVisible = true;
    this.pageComponentList = this.copyObject(item.componentList);
    this.setPageModelForm(item);

    // 保存初始状态
    this.history = [];
    this.historyIndex = -1;
    this.saveState();
  }

  selEditpageModel(item: any): void {
    this.pageModelPreviewDrawerVisible = true;
    this.pageComponentList = [...item];

    // 保存初始状态
    this.history = [];
    this.historyIndex = -1;
    this.saveState();
  }

  // 删除组件方法
  deleteComponent(index: number): void {
    this.pageComponentList.splice(index, 1);
    this.saveState(); // 保存状态
  }

  // 上移组件方法
  moveComponentUp(index: number): void {
    if (index > 0) {
      const temp = this.pageComponentList[index];
      this.pageComponentList[index] = this.pageComponentList[index - 1];
      this.pageComponentList[index - 1] = temp;
      this.saveState(); // 保存状态
    }
  }

  // 下移组件方法
  moveComponentDown(index: number): void {
    if (index < this.pageComponentList.length - 1) {
      const temp = this.pageComponentList[index];
      this.pageComponentList[index] = this.pageComponentList[index + 1];
      this.pageComponentList[index + 1] = temp;
      this.saveState(); // 保存状态
    }
  }

  // 拖拽排序处理方法
  dropComponent(event: CdkDragDrop<any[]>): void {
    moveItemInArray(this.pageComponentList, event.previousIndex, event.currentIndex);
    this.saveState(); // 保存状态
  }

  curEditComponent: any = null;
  selComponent(item: any): void {
    this.curEditComponent = item;

    this.setDefaultValue(this.curEditComponent.elements.children)

    // 初始化_activeTabIndex属性
    this.initializeActiveTabIndices(this.curEditComponent.elements.children);

    console.log(item);
  }

  setDefaultValue(list: any): void {
    list.map((item: any) => {
      if (!item.value && item.defaultValue) {
        item.value = item.defaultValue;
      }
      item.children && this.setDefaultValue(item.children)
    })
  }

  // 初始化tab索引属性
  initializeActiveTabIndices(list: any[]): void {
    list.forEach(item => {
      if (item.contentType && item.contentType.includes('list')) {
        // 确保每个list类型的元素都有_activeTabIndex属性
        if (item._activeTabIndex === undefined) {
          item._activeTabIndex = 0;
        }
        // 递归处理子元素
        if (item.value && Array.isArray(item.value)) {
          item.value.forEach((tab: any) => {
            if (Array.isArray(tab)) {
              this.initializeActiveTabIndices(tab);
            }
          });
        }
      }
      // 处理对象类型的子元素
      if (item.children) {
        this.initializeActiveTabIndices(item.children);
      }
    });
  }

  submitElementNode(): void {
    // if (this.elementForm.invalid) {
    //   // 递归检查所有子表单组
    //   this.validateAllFormFields(this.elementForm);
    //   this.messageService.error('请检查表单填写是否正确！');
    //   return;
    // }
    let contentType = this.elementDataItemForm.get('contentTypeValue')?.value;
    if (contentType === 'list') {
      this.curElementDataItem.value = this.curElementDataItem.value || [];
      this.curElementDataItem.value.push(this.copyObject(this.curElementDataItem.children));
      // 在添加新tab后，激活最新添加的tab
      setTimeout(() => {
        this.curElementDataItem._activeTabIndex = this.curElementDataItem.value.length - 1;
      }, 0);
      console.log(this.curElementDataItem.value);
    } else if (contentType === 'object') {
      // 对于object类型，确保children存在并且contentType正确设置
      this.curElementDataItem.contentType = [contentType];
      // object类型的value通常是undefined或者是children的引用
      if (!this.curElementDataItem.children) {
        this.curElementDataItem.children = [];
      }
    } else {
      this.curElementDataItem.value = this.elementDataItemForm.get('value')?.value;
      this.curElementDataItem.contentType = [contentType];
    }
    this.hideElementDataItemDrawer();
  }

  // 直接添加tab而不打开抽屉
  addNewTab(item: any): void {
    // 直接添加一个新的tab项
    item.value = item.value || [];
    item.value.push(this.copyObject(item.children));

    // 激活最新添加的tab
    setTimeout(() => {
      item._activeTabIndex = item.value.length - 1;
    }, 0);
  }

  // 检查tab中是否有未填写的内容
  hasUnfilledContent(tab: any[]): boolean {
    if (!Array.isArray(tab)) return false;

    for (const item of tab) {
      // 检查当前层级的元素
      if (this.isUnfilledItem(item)) {
        return true;
      }

      // 递归检查嵌套的list元素
      if (item.contentType && item.contentType.includes('list') && item.value) {
        if (Array.isArray(item.value)) {
          for (const subTab of item.value) {
            if (this.hasUnfilledContent(subTab)) {
              return true;
            }
          }
        }
      }

      // 递归检查嵌套的对象元素
      if (item.contentType && item.contentType.includes('object') && item.children) {
        if (this.hasUnfilledContent(item.children)) {
          return true;
        }
      }
    }

    return false;
  }

  // 判断单个元素是否未填写
  isUnfilledItem(item: any): boolean {
    // 如果是list或object类型，不直接判断，由专门的方法处理
    if (item.contentType &&
      (item.contentType.includes('list') || item.contentType.includes('object'))) {
      return false;
    }

    // 对于普通类型，检查value是否存在且不为空
    return !(item.value || item.value === 0);
  }

  // 检查整个组件是否有未填写的字段
  hasUnfilledFields(component: any): boolean {
    if (!component || !component.elements || !component.elements.children) {
      return false;
    }

    return this.hasUnfilledContent(component.elements.children);
  }

  copySuccess(typeName: string): void {
    this.messageService.success(`复制${typeName}成功`);
  }

  copyObject(obj: any): any {
    return JSON.parse(JSON.stringify(obj));
  }

  // 保存当前状态到历史记录
  private saveState(): void {
    // 删除未来的历史记录（如果有的话）
    if (this.historyIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.historyIndex + 1);
    }

    // 添加当前状态到历史记录
    const currentState = this.copyObject(this.pageComponentList);
    this.history.push(currentState);

    // 限制历史记录长度
    if (this.history.length > this.MAX_HISTORY_LENGTH) {
      this.history.shift();
    } else {
      this.historyIndex++;
    }
  }

  // 撤销操作
  undo(): void {
    if (this.canUndo()) {
      this.historyIndex--;
      this.pageComponentList = this.copyObject(this.history[this.historyIndex]);
    }
  }

  // 重做操作
  redo(): void {
    if (this.canRedo()) {
      this.historyIndex++;
      this.pageComponentList = this.copyObject(this.history[this.historyIndex]);
    }
  }

  // 检查是否可以撤销
  canUndo(): boolean {
    return this.historyIndex > 0;
  }

  // 检查是否可以重做
  canRedo(): boolean {
    return this.historyIndex < this.history.length - 1;
  }

  // 保存草稿
  saveDraft(): void {
    if (this.curPage) {
      // 更新页面的草稿
      const draftData = {
        ...this.pageModelForm.value,
        componentList: this.pageComponentList
      };

      this.PagesManageService.saveDraft(this.curPage._id, draftData).subscribe(res => {
        if (res.code === 0) {
          this.messageService.success('草稿保存成功，待发布');
          this.closePageModelDrawer();
          this.search();
        } else {
          this.messageService.error('草稿保存失败: ' + res.msg);
        }
      });
    } else {
      // 创建新页面时不能保存草稿，需要先创建页面
      this.messageService.warning('请先创建页面再保存草稿');
    }
  }

  submitPageInfo(): void {
    if (this.pageModelForm.invalid) {
      // 递归检查所有子表单组
      this.validateAllFormFields(this.pageModelForm);
      this.messageService.error('请检查表单填写是否正确！');
      return;
    }
    if (this.curPage) {
      this.pageModelForm.addControl('_id', this.fb.control(this.curPage._id));
      this.pageModelForm.addControl('componentList', this.fb.control(this.pageComponentList));
      this.PagesManageService.updatetConfig(this.pageModelForm.value).subscribe(res => {
        this.closePageModelDrawer();
        this.search();
      });
    } else {
      this.pageModelForm.addControl('componentList', this.fb.control(this.pageComponentList));
      this.PagesManageService.createConfig(this.pageModelForm.value).subscribe(res => {
        this.closePageModelDrawer();
        this.search();
      });
    }
    this.pageModelForm.reset();
    this.pageComponentList = [];
  }

  showEditPage(page: any): void {
    this.curEditComponent = null;
    this.initPageModelForm();
    this.curPage = page;
    let editingData = page.editingData || page;
    this.pageModelDrawerVisible = true;
    console.log(this.curPage)
    // this.pageComponentList = [...this.curPage.componentList];
    this.pageModelForm.patchValue(editingData);
    this.pageModelPreviewDrawerVisible = true;
    this.pageComponentList = this.copyObject(editingData.componentList);

    // 保存初始状态
    this.history = [];
    this.historyIndex = -1;
    this.saveState();
  }

  setPageModelForm(data: any): void {
    this.initPageModelForm();

    this.pageModelForm.patchValue(data);
    this.pageModelForm.controls['name'].setValue('');
    this.pageModelForm.controls['label'].setValue('');
    this.pageModelForm.controls['remark'].setValue('');
    this.pageModelForm.controls['theme'].setValue('light');
  }

  initPageModelForm(): void {
    this.pageModelForm = this.fb.group({
      name: ['', Validators.required],
      label: ['', Validators.required],
      pageType: ['', Validators.required],
      remark: [''],
      theme: ['light']
    });
  }

  elementDataForm: FormGroup;
  initElementDataForm(): void {
    this.elementDataForm = this.fb.group({});
  }

  elementDataItemDrawerVisible: boolean = false;
  curElementDataItem: any = {};
  showElementDataItemDrawer(item: any): void {
    this.elementDataItemDrawerVisible = true;
    this.curElementDataItem = item;
    this.initElementDataItemForm();
    if (item.defaultValue) {
      this.elementDataItemForm.controls['value'].setValue(item.defaultValue);
    }
    this.elementDataItemForm.patchValue(item);
    this.elementDataItemForm.controls['contentTypeValue'].setValue(item.contentType[0]);
  }
  hideElementDataItemDrawer(): void {
    this.elementDataItemDrawerVisible = false;
  }

  elementDataItemForm: FormGroup;
  initElementDataItemForm(): void {
    this.elementDataItemForm = this.fb.group({
      value: ['', Validators.required],
      contentType: [['text'], Validators.required],
      options: [[]],
      contentTypeValue: [''],
    });
  }

  getElementTypeOptions(elementForm: FormGroup): any[] {
    let options = elementForm.controls['contentType'].value;
    return this.RESOURCE_TYPE_OPTIONS.filter(item => options.includes(item.value))
  }
  getElementContentOptions(elementForm: FormGroup): any[] {
    let options = elementForm.controls['options'].value;
    console.log(options);
    return options.map((item: string) => {
      return {
        label: item,
        value: item
      }
    })
  }

  getKeys(item: any) {
    return Object.keys(item);
  }
  showDeletePageModal(page: any): void {
    this.curEditPage = page;
    this.deletePageModalVisible = true;
  }

  hideDeletePageModal(): void {
    this.deletePageModalVisible = false;
    this.curEditPage = null;
  }

  deletePage(): void {
    this.isOkLoading = true;
    this.PagesManageService.deleteConfig(this.curEditPage._id).subscribe(res => {
      console.log(res);
      if (res.code === 0) {
        this.messageService.success('删除成功');
        this.hideDeletePageModal();
        this.search();
        this.isOkLoading = false;
      } else {
        this.messageService.error('删除失败,' + res.msg);
      }
    });
  }

  // 显示发布确认框
  showPublishConfirm(id: string): void {
    this.publishTargetId = id;
    this.publishConfirmModalVisible = true;
  }

  // 隐藏发布确认框
  hidePublishConfirm(): void {
    this.publishConfirmModalVisible = false;
    this.publishTargetId = '';
  }

  // 确认发布草稿
  confirmPublish(): void {
    if (this.publishTargetId) {
      this.isOkLoading = true;
      this.PagesManageService.publishDraft(this.publishTargetId).subscribe(res => {
        if (res.code === 0) {
          this.messageService.success('发布成功');
          this.hidePublishConfirm();
          this.closePageModelDrawer();
          this.getList(this.pageIndex, this.pageSize, {});
        } else {
          this.messageService.error('发布失败: ' + res.msg);
        }
        this.isOkLoading = false;
      });
    }
  }

  // 递归验证所有表单字段
  private validateAllFormFields(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(field => {
      const control = formGroup.get(field);
      if (control instanceof FormArray) {
        control.controls.forEach((c: AbstractControl) => {
          if (c instanceof FormGroup) {
            this.validateAllFormFields(c);
          } else {
            c.markAsDirty();
            c.updateValueAndValidity({ onlySelf: true });
          }
        });
      } else if (control instanceof FormGroup) {
        this.validateAllFormFields(control);
      } else {
        control?.markAsDirty();
        control?.updateValueAndValidity({ onlySelf: true });
      }
    });
  }

  closeTab(parent: any, { index }: { index: number }, level: number): void {
    parent.value.splice(index, 1);
    // 如果关闭的tab在当前激活的tab之前或就是当前激活的tab，则需要调整激活的tab索引
    if (parent._activeTabIndex >= index && parent._activeTabIndex > 0) {
      parent._activeTabIndex = parent._activeTabIndex - 1;
    }
    // 如果列表为空，重置激活索引
    if (parent.value.length === 0) {
      parent._activeTabIndex = 0;
    }
  }

  // newTab(item:any): void {
  //   this.tabs.push('New Tab');
  //   this.selectedIndex = this.tabs.length;
  // }

  content = '';
  editorModules = {
    toolbar: [
      ['bold', 'italic'],
      ['link', 'image']
    ]
  };

  ngOnDestroy(): void {
  }

}