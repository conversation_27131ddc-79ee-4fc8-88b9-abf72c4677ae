import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_URL } from '../../const';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PagesManageService {

  constructor(private http: HttpClient) { }

  // 首页数据获取
  getList(pageIndex: number, pageSize: number, filters: { [k: string]: string }): Observable<any> {
    let params = new HttpParams()
      .append('pn', <string><unknown>pageIndex)
      .append('ps', <string><unknown>pageSize);

    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params = params.append(key, filters[key]);
      }
    });
    return this.http.get(`${API_URL}/page-manage/list`, { params });
  }

  // 创建首页配置项
  createConfig(config: any): Observable<any> {
    return this.http.post(`${API_URL}/page-manage/create`, config);
  }

  // 更新首页配置项
  updatetConfig(config: any): Observable<any> {
    return this.http.post(`${API_URL}/page-manage/update`, config);
  }

  // 保存草稿
  saveDraft(id: string, draftData: any): Observable<any> {
    return this.http.post(`${API_URL}/page-manage/save-draft`, { id, draftData });
  }

  // 发布草稿
  publishDraft(id: string): Observable<any> {
    return this.http.post(`${API_URL}/page-manage/publish`, { id });
  }

  // 获取预览数据
  getPreviewData(id: string): Observable<any> {
    let params = new HttpParams().append('id', id);
    return this.http.get(`${API_URL}/page-manage/preview`, { params });
  }

  startPage(config: any): Observable<any> {
    return this.http.post(`${API_URL}/page-manage/startPage`, config);
  }

  // 删除首页配置项
  deleteConfig(id: string): Observable<any> {
    return this.http.post(`${API_URL}/page-manage/delete`, { id: id });
  }
}