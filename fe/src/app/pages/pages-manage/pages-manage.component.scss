.ant-advanced-search-form {
    padding: 24px;
    background: #fbfbfb;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
}

button {
    margin-left: 8px;
}

.ant-form-item {
    margin-bottom: 12px;
}

.search-result-list {
    margin-top: 12px;
    overflow: auto;
}

.publish-draft-link {
    color: #1890ff;
    font-weight: bold;

    &:hover {
        color: #40a9ff;
        text-decoration: underline;
    }
}

.page-model-edit-drawer {
    display: flex;
    height: 100%;

    .component-list {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        align-content: flex-start;
        gap: 20px;
        width: 240px;
        height: 100%;
        padding-right: 24px;
        // border-right: solid #999 1px;
        overflow-y: auto;
        flex: 0 0 auto;

        .component-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
            width: 100%;
            border: solid #999 1px;
            border-radius: 12px;
            cursor: pointer;

            .item-name {
                font-size: 20px;
                line-height: 24px;
                padding: 4px 8px;
                word-wrap: break-word;
            }

            .item-img {
                width: 100%;
            }

            .item-remark {
                font-size: 14px;
                color: #999;
                margin-top: auto;
                padding: 4px 8px;
            }
        }
    }



    .page-model {
        width: 100%;
        padding: 0 0 0 24px;
        overflow: auto;
    }

    .tree-node-name {
        white-space: nowrap;
    }

    .tree-node-value {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.page-model-preview {
    display: flex;
    flex-direction: column;
    height: 100%;

    .history-controls {
        display: flex;
        gap: 8px;
        padding: 8px;
        border-bottom: 1px solid #e8e8e8;
        background-color: #fafafa;
        flex-shrink: 0;

        .history-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }
    }

    .component-list-container {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow-y: auto;
        padding: 40px 0;
        gap: 40px;
    }

    .page-component-item {
        display: flex;
        position: relative;
        min-height: 100px;
        border: 1px dashed #999;
        cursor: move;

        .component-img-container {
            position: relative;
            width: 100%;

            .component-img {
                width: 100%;
                height: 100%;
                cursor: pointer;
            }

            .unfilled-indicator {
                position: absolute;
                top: 8px;
                right: 8px;
                width: 24px;
                height: 24px;
                background-color: #ff4d4f;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 14px;
                box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
                z-index: 10;
                animation: pulse 2s infinite;

                nz-icon {
                    font-size: 12px;
                }

                &:hover {
                    background-color: #ff7875;
                    transform: scale(1.1);
                    transition: all 0.2s ease;
                }
            }
        }

        .component-name-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10;
        }

        .delete-component {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background-color: #ff4d4f;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
            z-index: 10;
            cursor: pointer;
            opacity: 0;
            transition: all 0.3s ease;

            &:hover {
                background-color: #ff7875;
                transform: scale(1.1);
            }
        }

        .component-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            flex-direction: column;
            gap: 4px;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;

            .move-up,
            .move-down {
                width: 24px;
                height: 24px;
                background-color: #1890ff;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 14px;
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover:not(.disabled) {
                    background-color: #40a9ff;
                    transform: scale(1.1);
                }

                &.disabled {
                    background-color: #cccccc;
                    cursor: not-allowed;
                    opacity: 0.5;
                }
            }
        }

        .drag-handle {
            position: absolute;
            bottom: 8px;
            left: 8px;
            width: 24px;
            height: 24px;
            background-color: #52c41a;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
            z-index: 10;
            cursor: move;
            opacity: 0;
            transition: all 0.3s ease;

            &:hover {
                background-color: #73d13d;
                transform: scale(1.1);
            }
        }

        &:hover {

            .delete-component,
            .component-actions,
            .drag-handle {
                opacity: 1;
            }
        }

        &.cdk-drag-preview {
            box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
                0 8px 10px 1px rgba(0, 0, 0, 0.14),
                0 3px 14px 2px rgba(0, 0, 0, 0.12);
            opacity: 0.8;

            .component-img-container {
                .component-img {
                    opacity: 0.8;
                }
            }
        }

        &.cdk-drag-placeholder {
            opacity: 0.5;
            background: #fafafa;
        }

        &.cdk-drag-animating {
            transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
        }
    }

    // 拖拽列表动画
    &.cdk-drop-list-dragging .page-component-item:not(.cdk-drag-placeholder) {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
}

// 拖拽预览样式 - 保持与原始样式一致
.page-component-drag-preview {
    display: flex;
    position: relative;
    min-height: 100px;
    border: 1px dashed #999;
    cursor: move;
    opacity: 0.8;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
        0 8px 10px 1px rgba(0, 0, 0, 0.14),
        0 3px 14px 2px rgba(0, 0, 0, 0.12);

    .component-name-tag {
        position: absolute;
        top: 8px;
        left: 8px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 10;
    }

    .component-img-container {
        position: relative;
        width: 100%;

        .component-img {
            width: 100%;
            height: 100%;
            cursor: pointer;
            opacity: 0.8;
        }
    }

    .delete-component,
    .component-actions,
    .drag-handle {
        opacity: 0; // 拖拽预览时不显示操作按钮
    }
}

.form-item {
    margin-bottom: 10px;
    position: relative;
    margin: 0 24px;

    input {
        margin-bottom: 8px;
    }

    textarea {
        margin-bottom: 8px;
        font-family: inherit;
        line-height: 1.5;
    }

    nz-select {
        margin-bottom: 8px;
    }

    .delete-form-item-btn {
        position: absolute;
        right: 10px;
    }

    .param-input {
        width: 50px;
    }

    &.count-type {
        margin-top: 10px;
    }

}

.element-datas {

    .element-name {
        margin: 16px 24px;
    }

    .element-data-items {
        margin: 0 24px;

        .element-data-item {
            margin-bottom: 8px;

            .element-data-name {
                cursor: pointer;
            }

            .element-data-value {
                &.empty {
                    color: #ff4d4f; // 红色，表示未填写
                    font-style: italic;
                    background-color: #fff2f0; // 浅红色背景
                    border: 1px dashed #ffccc7; // 虚线边框
                    padding: 2px 6px;
                    border-radius: 4px;
                }

                &.filled {
                    color: #52c41a; // 绿色，表示已填写
                }
            }
        }
    }
}

.object-children {
    margin-left: 20px;
    padding-left: 10px;
    border-left: 2px solid #e8e8e8;

    .element-data-item {
        margin-bottom: 6px;

        .element-data-name {
            cursor: pointer;
            color: #1890ff;

            &:hover {
                text-decoration: underline;
            }
        }

        .element-data-value {
            margin-left: 8px;
            color: #666;

            &.empty {
                color: #ff4d4f; // 红色，表示未填写
                font-style: italic;
                background-color: #fff2f0; // 浅红色背景
                border: 1px dashed #ffccc7; // 虚线边框
                padding: 2px 6px;
                border-radius: 4px;
            }

            &.filled {
                color: #52c41a; // 绿色，表示已填写
            }
        }
    }
}

// 新增：多层list的层级显示样式
.list-container {
    position: relative;

    .list-level-indicator {
        margin-bottom: 8px;

        .level-badge {
            display: inline-block;
            padding: 2px 8px;
            background-color: #f0f0f0;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }
    }

    // 未填写内容提醒标识
    .unfilled-indicator {
        display: inline-block;
        min-width: 18px;
        height: 18px;
        line-height: 18px;
        text-align: center;
        background-color: #ff4d4f;
        color: #ffffff;
        font-size: 12px;
        font-weight: bold;
        border-radius: 9px;
        margin-left: 6px;
        vertical-align: middle;
        box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
        position: relative;
        top: -1px;
    }

    // 移除了未使用的层级背景色样式

    .element-data-value {
        &.empty {
            color: #ff4d4f; // 红色，表示未填写
            font-style: italic;
            background-color: #fff2f0; // 浅红色背景
            border: 1px dashed #ffccc7; // 虚线边框
            padding: 2px 6px;
            border-radius: 4px;
        }

        &.filled {
            color: #52c41a; // 绿色，表示已填写
        }
    }

    // List中元素的层级显示
    .element-data-item {
        margin-bottom: 6px;
        transition: all 0.3s ease;

        &:hover {
            background-color: rgba(24, 144, 255, 0.05);
            border-radius: 4px;
        }

        .element-data-name {
            cursor: pointer;
            color: #1890ff;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }

        .element-data-value {
            margin-left: 8px;
            color: #666;

            &.empty {
                color: #ff4d4f; // 红色，表示未填写
                font-style: italic;
                background-color: #fff2f0; // 浅红色背景
                border: 1px dashed #ffccc7; // 虚线边框
                padding: 2px 6px;
                border-radius: 4px;
            }

            &.filled {
                color: #52c41a; // 绿色，表示已填写
            }
        }
    }
}

@keyframes pulse {

    0%,
    100% {
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
    }

    50% {
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.6), 0 0 0 4px rgba(255, 77, 79, 0.2);
    }
}


.add-component-btn-container {
    margin-bottom: 16px;
}

.add-component-btn {
    height: 48px;
    border-style: dashed;
    margin: 0 auto;
}

// 添加组件选择抽屉样式
.component-select-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .component-grid {
        flex: 1;
        overflow-y: auto;

        .component-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            margin-bottom: 12px;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                border-color: #1890ff;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }

            .component-image {
                width: 80px;
                height: 80px;
                flex-shrink: 0;
                margin-right: 12px;
                border: 1px solid #e8e8e8;
                border-radius: 4px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                }
            }

            .component-info {
                flex: 1;

                .component-label {
                    font-weight: bold;
                    font-size: 16px;
                    margin-bottom: 4px;
                }

                .component-name {
                    font-size: 12px;
                    color: #888;
                }
            }
        }
    }

    .component-pagination {
        padding: 16px 0;
        border-top: 1px solid #e8e8e8;
        text-align: right;
    }
}