<form nz-form [formGroup]="searchValidateForm" class="ant-advanced-search-form">
    <div nz-row [nzGutter]="24">
        @for (control of searchControlArray; track control['name']) {
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>{{ control['labelName'] }}</nz-form-label>
                <nz-form-control>
                    <input nz-input [placeholder]="control['labelName']" [formControlName]="control['name']"
                        [attr.id]="control['name']" />
                </nz-form-control>
            </nz-form-item>
        </div>
        }
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>页面类型</nz-form-label>
                <nz-form-control>
                    <nz-select [(ngModel)]="selectedPageType" (ngModelChange)="search()"
                        [ngModelOptions]="{standalone: true}" name="pageType" nzPlaceHolder="选择页面类型"
                        style="width: 100%">
                        <nz-option nzValue="" nzLabel="全部"></nz-option>
                        @for (option of PAGE_TYPE_OPTIONS; track $index) {
                        <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        }
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
        </div>
    </div>
    <div nz-row>
        <div nz-col [nzSpan]="24" class="search-area">
            <button nz-button [nzType]="'primary'" (click)="search()">搜索</button>
            <button nz-button (click)="resetForm()">重置</button>
            <button nz-button [nzType]="'primary'" (click)="showPageModelDrawer()">新增页面</button>
        </div>
    </div>
</form>

<div class="search-result-list">
    <nz-table #columnTable nzShowSizeChanger nzShowQuickJumper [nzData]="listData" [nzFrontPagination]="false"
        nzPaginationType="small" [nzPageSizeOptions]="[10,20,30,40,50]" [(nzPageSize)]="pageSize"
        [(nzPageIndex)]="pageIndex" [nzTotal]="total" [nzLoading]="listLoading"
        (nzPageIndexChange)="onPageIndexChange(pageIndex)" (nzPageSizeChange)="onPageSizeChange(pageSize)"
        [nzScroll]="{ x: '1000px' }">
        <thead>
            <tr>
                <th width="50px">编号</th>
                <th width="100px">名称</th>
                <th width="180px">页面label</th>
                <th width="100px">页面类型</th>
                <th width="180px">修改时间</th>
                <th width="180px">创建时间</th>
                <th width="180px">ID & 链接</th>
                <th nzRight width="180px">操作</th>
            </tr>
        </thead>
        <tbody>
            @for (data of columnTable.data; track data['_id']) {
            <tr>
                <td width="50px">{{(pageIndex - 1) * pageSize + $index + 1}}</td>
                <td width="100px">{{data['name']}}</td>
                <td width="180px">{{data['label']}}</td>
                <td width="100px">{{PAGE_TYPE_STR[data['pageType']]}}</td>
                <td width="180px">{{data['updated_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="180px">{{data['created_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="180px">
                    <span ngxClipboard [cbContent]="data['_id']" (cbOnSuccess)="copySuccess('ID')"
                        style="cursor: pointer; color: blue; text-decoration: underline;" nz-tooltip
                        nzTooltipTitle="点击复制ID">{{data['_id']}}</span>
                    <br />
                    <span ngxClipboard [cbContent]="al_home+'/page/'+data['_id']" (cbOnSuccess)="copySuccess('链接')"
                        style="cursor: pointer; color: blue; text-decoration: underline;" nz-tooltip
                        nzTooltipTitle="点击复制链接">{{al_home+'/page/'+data['_id']}}</span>
                </td>
                <td nzRight width="180px">
                    <a (click)="showEditPage(data)">编辑</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="showDeletePageModal(data)">删除</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a [href]="al_home+'/preview/'+data['_id']" target="_blank">预览</a>
                    <ng-container *ngIf="data.hasDraft">
                        <nz-divider nzType="vertical"></nz-divider>
                        <a (click)="showPublishConfirm(data._id)" class="publish-draft-link">发布更新</a>
                    </ng-container>
                </td>
            </tr>
            }
        </tbody>
    </nz-table>
</div>

<nz-drawer [nzClosable]="true" [nzVisible]="pageModelDrawerVisible" nzPlacement="right" nzTitle="页面信息"
    [nzWidth]="'100vw'" [nzFooter]="footerTpl" [nzZIndex]="1" (nzOnClose)="closePageModelDrawer()">
    <div *nzDrawerContent class="page-model-edit-drawer">
        <div class="component-list">
            <nz-drawer [nzClosable]="false" [nzMask]="false" [nzMaskClosable]="false" [nzPlacement]="'left'"
                [nzVisible]="true" [nzTitle]="curSelectPageModel.label" [nzWidth]="240"
                (nzOnClose)="pageModelPreviewDrawerVisible = false">
                <div *nzDrawerContent class="page-model-preview" cdkDropList
                    (cdkDropListDropped)="dropComponent($event)">
                    <!-- 撤销/重做按钮 -->
                    <div class="history-controls">
                        <button nz-button nzType="default" (click)="undo()" [disabled]="!canUndo()" nz-tooltip="撤销"
                            nzTooltipPlacement="top" class="history-btn">
                            <span nz-icon nzType="undo" nzTheme="outline"></span>
                        </button>
                        <button nz-button nzType="default" (click)="redo()" [disabled]="!canRedo()" nz-tooltip="重做"
                            nzTooltipPlacement="top" class="history-btn">
                            <span nz-icon nzType="redo" nzTheme="outline"></span>
                        </button>
                    </div>
                    <!-- 组件列表容器 -->
                    <div class="component-list-container">
                        <!-- 添加"添加组件"按钮 -->
                        <div class="add-component-btn-container">
                            <button nz-button nzType="dashed" (click)="showComponentSelectDrawer()"
                                class="add-component-btn" nzBlock>
                                <span nz-icon nzType="plus"></span>
                                添加组件
                            </button>
                        </div>

                        <div class="page-component-item" cdkDrag [cdkDragPreviewClass]="'page-component-drag-preview'"
                            *ngFor="let item of pageComponentList; let i = index">
                            <div class="component-name-tag">{{ item.name }}</div>
                            <div class="component-img-container" (click)="selComponent(item)">
                                <img [src]="item.image" alt="" class="component-img">
                                <!-- 未填写字段提醒 -->
                                <div class="unfilled-indicator" *ngIf="hasUnfilledFields(item)" nz-tooltip="该组件有未填写的字段"
                                    nzTooltipPlacement="top">
                                    <nz-icon nzType="exclamation-circle" nzTheme="fill"></nz-icon>
                                </div>
                            </div>
                            <!-- 删除组件图标 -->
                            <div class="delete-component" (click)="deleteComponent(i)" nz-tooltip="删除组件"
                                nzTooltipPlacement="top">
                                <nz-icon nzType="delete" nzTheme="outline"></nz-icon>
                            </div>
                            <!-- 拖拽指示器 -->
                            <div class="drag-handle" cdkDragHandle nz-tooltip="拖拽排序" nzTooltipPlacement="right">
                                <nz-icon nzType="drag" nzTheme="outline"></nz-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </nz-drawer>
        </div>
        <div class="page-model">
            <div class="modal-content">
                <form nz-form [formGroup]="pageModelForm">
                    <nz-form-item class="form-item">
                        <nz-form-label nzRequired>页面名称</nz-form-label>
                        <nz-form-control>
                            <input nz-input formControlName="name" placeholder="请输入页面名称">
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label nzRequired>页面别名</nz-form-label>
                        <nz-form-control>
                            <input nz-input formControlName="label" placeholder="请输入页面别名">
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label nzRequired>页面类型</nz-form-label>
                        <nz-form-control>
                            <nz-select nzPlaceHolder="选择页面类型" formControlName="pageType" style="width: 100%">
                                @for (option of PAGE_TYPE_OPTIONS; track $index) {
                                <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                                }
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label>页面主题</nz-form-label>
                        <nz-form-control>
                            <nz-select nzPlaceHolder="选择页面主题" formControlName="theme" style="width: 100%">
                                @for (option of THEME_OPTIONS; track $index) {
                                <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                                }
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                    <nz-divider nzText="备注"></nz-divider>
                    <nz-form-item class="form-item">
                        <nz-form-label>备注</nz-form-label>
                        <nz-form-control>
                            <textarea style="height: 100px;" nz-input formControlName="remark" placeholder="请输入备注">
                                </textarea>
                        </nz-form-control>
                    </nz-form-item>
                </form>
                <nz-divider nzText="组件数据"></nz-divider>
                <ng-container *ngIf="curEditComponent && !curEditComponent.isGlobal">
                    <div class="element-datas">
                        <h2 class="element-name">{{curEditComponent.label}}组件数据</h2>
                        <div class="element-data-items">
                            <div class="element-data-item" *ngFor="let item of curEditComponent.elements.children">
                                <span class="element-data-name"
                                    (click)="showElementDataItemDrawer(item)">{{item.name}}</span>:
                                <span class="element-data-value"
                                    [ngClass]="{ 'empty': !(item.value || item.value === 0), 'filled': (item.value || item.value === 0) }"
                                    *ngIf="!item.contentType.includes('list') && !item.contentType.includes('object')">
                                    {{item.value ||'未输入' }}
                                </span>
                                <ng-container *ngIf="item.contentType.includes('list')">
                                    <ng-container
                                        *ngTemplateOutlet="recursiveTab; context: { $implicit: item, level: 0,}">
                                    </ng-container>
                                </ng-container>
                                <ng-container *ngIf="item.contentType.includes('object')">
                                    <ng-container
                                        *ngTemplateOutlet="objectTemplate; context: { $implicit: item, level: 0 }">
                                    </ng-container>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </ng-container>
                <div class="global-component" *ngIf="curEditComponent && curEditComponent.isGlobal">
                    全局组件数据,不可编辑
                </div>
            </div>
        </div>
    </div>
    <ng-template #recursiveTab let-item let-level="level">
        <div class="list-container" [style.margin-left.px]="20" [style.padding-left.px]="level > 0 ? 10 : 0"
            [style.border-left]="level > 0 ? '2px solid #e8e8e8' : 'none'">
            <nz-tabset [nzAnimated]="false" [nzSelectedIndex]="item._activeTabIndex || 0"
                (nzSelectedIndexChange)="item._activeTabIndex = $event" nzType="editable-card" (nzAdd)="addNewTab(item)"
                (nzClose)="closeTab(item, $event, 0)">
                @for (tab of item.value; let i = $index; track i) {
                <nz-tab nzClosable [nzTitle]="tabTitleTemplate">
                    <ng-template #tabTitleTemplate>
                        <span>{{ item.name }} {{ i }}</span>
                        <span *ngIf="hasUnfilledContent(tab)" class="unfilled-indicator" title="该Tab中有未填写的内容">!</span>
                    </ng-template>
                    <div class="element-data-item" *ngFor="let childItem of tab">
                        <span class="element-data-name"
                            (click)="showElementDataItemDrawer(childItem)">{{childItem.name}}</span>:
                        <span class="element-data-value"
                            [ngClass]="{ 'empty': !(childItem.value || childItem.value === 0), 'filled': (childItem.value || childItem.value === 0) }"
                            *ngIf="!childItem.contentType.includes('list') && !childItem.contentType.includes('object')">
                            {{childItem.value ||'未输入' }}
                        </span>
                        <ng-container *ngIf="childItem.contentType.includes('list')">
                            <ng-container
                                *ngTemplateOutlet="recursiveTab; context: { $implicit: childItem, level: level + 1, tabIndex: item._activeTabIndex }">
                            </ng-container>
                        </ng-container>
                        <ng-container *ngIf="childItem.contentType.includes('object')">
                            <ng-container
                                *ngTemplateOutlet="objectTemplate; context: { $implicit: childItem, level: (level || 0) + 1 }">
                            </ng-container>
                        </ng-container>
                    </div>
                </nz-tab>
                }
            </nz-tabset>
        </div>
    </ng-template>
    <ng-template #objectTemplate let-item let-level="level">
        <div class="object-children" [style.margin-left.px]="20" [style.padding-left.px]="level > 0 ? 10 : 0"
            [style.border-left]="level > 0 ? '2px solid #e8e8e8' : 'none'">
            <div class="element-data-item" *ngFor="let childItem of item.children">
                <span class="element-data-name"
                    (click)="showElementDataItemDrawer(childItem)">{{childItem.name}}</span>:
                <span class="element-data-value"
                    [ngClass]="{ 'empty': !(childItem.value || childItem.value === 0), 'filled': (childItem.value || childItem.value === 0) }"
                    *ngIf="!childItem.contentType.includes('list') && !childItem.contentType.includes('object')">
                    {{childItem.value ||'未输入' }}
                </span>
                <ng-container *ngIf="childItem.contentType.includes('list')">
                    <ng-container
                        *ngTemplateOutlet="recursiveTab; context: { $implicit: childItem, level: (level || 0) + 1 }">
                    </ng-container>
                </ng-container>
                <ng-container *ngIf="childItem.contentType.includes('object')">
                    <ng-container
                        *ngTemplateOutlet="objectTemplate; context: { $implicit: childItem, level: (level || 0) + 1 }">
                    </ng-container>
                </ng-container>
            </div>
        </div>
    </ng-template>
    <ng-template #footerTpl>
        <div style="float: right">
            <button nz-button style="margin-right: 8px;" (click)="closePageModelDrawer()">Cancel</button>
            <button nz-button nzType="primary" (click)="saveDraft()" *ngIf="curPage">保存草稿</button>
            <button nz-button nzType="primary" (click)="showPublishConfirm(curPage._id)"
                *ngIf="curPage && curPage.hasDraft">发布</button>
            <button nz-button nzType="primary" (click)="submitPageInfo()" *ngIf="!curPage">提交</button>
        </div>
    </ng-template>
</nz-drawer>

<!-- <ng-template #objectTemplate let-item let-level="level">
    <div class="object-children">
        <div class="element-data-item" *ngFor="let childItem of item.children">
            <span class="element-data-name"
                (click)="showElementDataItemDrawer(childItem)">{{childItem.name}}</span>:
            <span class="element-data-value" *ngIf="!childItem.contentType.includes('list') && !childItem.contentType.includes('object')">
                {{childItem.value ||'未输入' }}
            </span>
            <ng-container *ngIf="childItem.contentType.includes('list')">
                <ng-container
                    *ngTemplateOutlet="recursiveTab; context: { $implicit: childItem, level: (level || 0) + 1}">
                </ng-container>
            </ng-container>
            <ng-container *ngIf="childItem.contentType.includes('object')">
                <ng-container
                    *ngTemplateOutlet="objectTemplate; context: { $implicit: childItem, level: (level || 0) + 1 }">
                </ng-container>
            </ng-container>
        </div>
    </div>
</ng-template> -->

<!-- 元素内容编辑抽屉 -->
<nz-drawer [nzClosable]="true" [nzVisible]="elementDataItemDrawerVisible" nzPlacement="right"
    [nzTitle]="curElementDataItem.name" [nzWidth]="'1000px'" [nzMaskClosable]="true" [nzFooter]="footerTpl2"
    [nzZIndex]="2" (nzOnClose)="hideElementDataItemDrawer()">
    <div *nzDrawerContent class="page-model-edit-drawer">
        <form nz-form [formGroup]="elementDataItemForm" style="width: 100%;">
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>元素类型</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <nz-select nzPlaceHolder="选择元素类型" formControlName="contentTypeValue" style="width: 100%">
                        @for (option of getElementTypeOptions(elementDataItemForm); track $index) {
                        <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        }
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'text'">
                <nz-form-label [nzSpan]="6" nzRequired>元素内容</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <textarea nz-input formControlName="value" placeholder="请输入元素内容（支持换行和空格格式）" rows="4"
                        style="resize: vertical;"></textarea>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item"
                *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'richText'">
                <nz-form-label nzRequired>元素内容</nz-form-label>
                <nz-form-control>
                    <quill-editor formControlName="value" [style]="{width: '100%', height: '300px'}"
                        [placeholder]="'请输入内容...'"></quill-editor>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'list'">
                <nz-form-label nzRequired>新增{{curElementDataItem.name}}列表</nz-form-label>
                <nz-form-control>
                    <button nz-button [nzType]="'primary'" (click)="submitElementNode()">新增列表</button>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'image'">
                <nz-form-label [nzSpan]="6" nzRequired>元素图片</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-image-upload-form uploadUrl="/upload-file/image"
                        formControlName="value"></app-image-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'video'">
                <nz-form-label [nzSpan]="6" nzRequired>元素视频</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-video-upload-form uploadUrl="/upload-file/video" formControlName="value">
                    </app-video-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'file'">
                <nz-form-label [nzSpan]="6" nzRequired>元素文件</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-file-upload-form uploadUrl="/upload-file/file" formControlName="value">
                    </app-file-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'select'">
                <nz-form-label [nzSpan]="6" nzRequired>内容选择</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <nz-select nzPlaceHolder="选择元素类型" formControlName="value" style="width: 100%">
                        @for (option of getElementContentOptions(elementDataItemForm); track $index) {
                        <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        }
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item"
                *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'palette'">
                <nz-form-label [nzSpan]="6" nzRequired>选择颜色</nz-form-label>
                <nz-form-control [nzSpan]="4">
                    <input nz-input formControlName="value" placeholder="请输入颜色值或点击选择颜色" type="color"
                        style="height: 40px;">
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'object'">
                <nz-form-label nzRequired>编辑{{curElementDataItem.name}}对象</nz-form-label>
                <nz-form-control>
                    <button nz-button [nzType]="'primary'" (click)="submitElementNode()">确认对象类型</button>
                </nz-form-control>
            </nz-form-item>

        </form>
    </div>
    <ng-template #footerTpl2>
        <div style="float: right">
            <button nz-button style="margin-right: 8px;" (click)="hideElementDataItemDrawer()">Cancel</button>
            <button nz-button nzType="primary" (click)="submitElementNode()">提交</button>
        </div>
    </ng-template>
</nz-drawer>

<!-- 添加组件选择抽屉 -->
<nz-drawer [nzClosable]="true" [nzMask]="true" [nzMaskClosable]="true" [nzPlacement]="'right'"
    [nzVisible]="componentSelectDrawerVisible" nzPlacement="right" nzTitle="选择组件" [nzWidth]="'600px'"
    (nzOnClose)="hideComponentSelectDrawer()">
    <div *nzDrawerContent>
        <div class="component-select-container">
            <div class="component-grid">
                <nz-spin [nzSpinning]="componentsListLoading">
                    <div class="component-item" *ngFor="let component of allComponentsList"
                        (click)="selectComponentAndInsert(component)">
                        <div class="component-image">
                            <img [src]="component.image" [alt]="component.label" />
                        </div>
                        <div class="component-info">
                            <div class="component-label">{{ component.label }}</div>
                            <div class="component-name">{{ component.name }}</div>
                        </div>
                    </div>
                </nz-spin>
            </div>

            <!-- 组件分页 -->
            <div class="component-pagination" *ngIf="componentsTotal > componentsPageSize">
                <nz-pagination [nzPageIndex]="componentsPageIndex" [nzPageSize]="componentsPageSize"
                    [nzTotal]="componentsTotal" (nzPageIndexChange)="onComponentsPageIndexChange($event)"
                    (nzPageSizeChange)="onComponentsPageSizeChange($event)">
                </nz-pagination>
            </div>
        </div>
    </div>
</nz-drawer>

<nz-modal [(nzVisible)]="deletePageModalVisible" nzTitle="删除页面" (nzOnCancel)="hideDeletePageModal()"
    (nzOnOk)="deletePage()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        @if (curEditPage) {
        确定要删除<span class="page-name">{{ curEditPage.name }}</span> 吗？
        }
    </div>
</nz-modal>

<nz-modal [(nzVisible)]="publishConfirmModalVisible" nzTitle="发布确认" (nzOnCancel)="hidePublishConfirm()"
    (nzOnOk)="confirmPublish()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        确定要发布此页面的更新吗？发布后用户将看到最新的内容。
    </div>
</nz-modal>