import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_URL } from '../../const';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ComponentsLibraryService {

  constructor(private http: HttpClient) { }

  // 首页数据获取
  getList(pageIndex: number, pageSize: number, filters: { [k: string]: string }): Observable<any> {
    let params = new HttpParams()
      .append('pn', <string><unknown>pageIndex)
      .append('ps', <string><unknown>pageSize);

    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params = params.append(key, filters[key]);
      }
    });
    return this.http.get(`${API_URL}/components/list`, {params}); 
  }

  getGlobalComponents(pageIndex: number, pageSize: number, filters: { [k: string]: string }): Observable<any> {
    let params = new HttpParams()
      .append('pn', <string><unknown>pageIndex)
      .append('ps', <string><unknown>pageSize);

    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params = params.append(key, filters[key]);
      }
    });
    return this.http.get(`${API_URL}/components/global-list`, {params}); 
  }

  getListStatic(): Observable<any> {
   return this.http.get(`${API_URL}/components/list2`); 
  }

  // 创建首页配置项
  createComponentConfig(config: any): Observable<any> {
    return this.http.post(`${API_URL}/components/create`, config); 
  }

  // 更新首页配置项
  updateComponentConfig(config: any): Observable<any> {
    return this.http.post(`${API_URL}/components/update`, config); 
  }

  // 保存草稿
  saveDraft(id: string, draftData: any): Observable<any> {
    return this.http.post(`${API_URL}/components/save-draft`, { id, draftData });
  }

  // 发布草稿
  publishDraft(id: string): Observable<any> {
    return this.http.post(`${API_URL}/components/publish`, { id });
  }

  // 获取预览数据
  getPreviewData(id: string): Observable<any> {
    let params = new HttpParams().append('id', id);
    return this.http.get(`${API_URL}/components/preview`, { params });
  }

  startComponent(config: any): Observable<any> {
    return this.http.post(`${API_URL}/components/startComponent`, config); 
  }

  // 删除首页配置项
  deleteComponentConfig(id: string): Observable<any> {
    return this.http.post(`${API_URL}/components/delete`, {id: id});
  }
}