import { CommonModule } from '@angular/common';
import { Component, NgModule } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { ComponentsLibraryService } from './components-library.service';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ClipboardModule } from 'ngx-clipboard';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { ImageUploadFormComponent } from '../../components/image-upload-form/image-upload-form.component';
import { VideoUploadFormComponent } from '../../components/video-upload-form/video-upload-form.component';
import { FileUploadFormComponent } from '../../components/file-upload-form/file-upload-form.component';
import { NzImageModule } from 'ng-zorro-antd/image';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzTreeFlatDataSource, NzTreeFlattener, NzTreeViewModule } from 'ng-zorro-antd/tree-view';
import { SelectionModel } from '@angular/cdk/collections';
import { FlatTreeControl } from '@angular/cdk/tree';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { TablePageSizeService } from '../../services/table-page-size.service';

interface TreeNode {
    name: string;
    key: string;
    contentType?: string[];
    required?: boolean;
    children?: TreeNode[];
    defaultValueType?: string;
    defaultValue?: string;
    options?: string[];
}

const TREE_DATA: TreeNode[] = [
    {
        name: '组件数据',
        key: 'data',
        contentType: ['object'],
    }
];

interface FlatNode {
    expandable: boolean;
    name: string;
    key: string;
    level: number;
    // contentType?: string[];
}

@Component({
    selector: 'app-components-library',
    imports: [
        ReactiveFormsModule,
        CommonModule,
        NzButtonModule,
        NzFormModule,
        ClipboardModule,
        NzToolTipModule,
        NzInputModule,
        NzIconModule,
        NzModalModule,
        NzTableModule,
        NzDividerModule,
        NzTreeModule,
        NzCardModule,
        NzPopconfirmModule,
        NzSelectModule,
        ImageUploadFormComponent,
        VideoUploadFormComponent,
        FileUploadFormComponent,
        NzImageModule,
        NzTreeViewModule,
        NzDrawerModule,
        FormsModule,
        NzTagModule,
    ],
    templateUrl: './components-library.component.html',
    styleUrl: './components-library.component.scss'
})
export class ComponentsLibraryComponent {

    optionValue: string = '';

    // 常量
    STATUS_TYPE_STR: { [key: string]: string } = {
        0: '展示中',
        1: '未使用'
    };
    BTN_STATUS: { [key: string]: string } = {
        0: '停用',
        1: '启用'
    };
    COMPONENT_STATUS: { [key: string]: number } = {
        Active: 0, // 激活状态
        Inactive: 1, // 禁用状态
        Deleted: 3 // 已删除状态
    }
    RESOURCE_TYPE_OPTIONS = [
        {
            label: '文字',
            value: 'text'
        },
        {
            label: '选择器',
            value: 'select'
        },
        {
            label: '图片',
            value: 'image'
        },
        {
            label: '视频',
            value: 'video'
        },
        {
            label: '富文本',
            value: 'richText'
        },
        {
            label: '文件',
            value: 'file'
        },
        {
            label: '列表',
            value: 'list'
        },
        {
            label: '对象',
            value: 'object'
        },
        {
            label: '调色盘',
            value: 'palette'
        }
    ];

    DEFAULT_VALUE_TYPE_OPTIONS = [
        {
            label: '文字',
            value: 'text'
        },
        {
            label: '图片',
            value: 'image'
        },
        {
            label: '视频',
            value: 'video'
        },
        {
            label: '文件',
            value: 'file'
        },
        {
            label: '调色盘',
            value: 'palette'
        },
    ];

    ELEMENT_TYPE_OPTIONS = [
        {
            label: '单一',
            value: 'single'
        },
        {
            label: '多个',
            value: 'array'
        },
    ];

    REQUIRE_TYPE_OPTIONS = [
        {
            label: '必填',
            value: true
        },
        {
            label: '非必填',
            value: false
        }
    ]

    // 变量
    searchValidateForm: FormGroup;
    searchControlArray = [
        {
            name: 'name',
            labelName: '组件名称'
        }
    ];
    pageIndex: number = 1;
    pageSize: number = 20;
    listLoading: boolean = false;
    listData: any;
    total: number = 0;
    curComponent: any = null;
    componentForm: FormGroup;
    componentInfoModalVisiable: boolean = false;
    isOkLoading: boolean = false;
    deleteComponentModalVisible: boolean = false;
    curEditComponent: any = null;
    SetComponentStatusModalVisible: boolean = false;

    constructor(
        private fb: FormBuilder,
        private ComponentsLibraryService: ComponentsLibraryService,
        private messageService: NzMessageService,
        private tablePageSizeService: TablePageSizeService
    ) {
        this.searchValidateForm = this.fb.group({});
        this.componentForm = this.fb.group({});
        this.elementForm = this.fb.group({});
    }

    // 生命周期钩子
    ngOnInit(): void {
        // 初始化页面大小
        this.pageSize = this.tablePageSizeService.getPageSize();

        this.searchControlArray.map((control) => {
            this.searchValidateForm.addControl(control.name, this.fb.control(''));
        });
        this.getList(this.pageIndex, this.pageSize, {});
        this.initComponentForm();
        this.treeData = JSON.parse(JSON.stringify(TREE_DATA));
        this.dataSource.setData(this.treeData);
        this.treeControl.expandAll();

        this.initElementForm();
    }

    // #region 搜索
    resetForm(): void {
        this.searchValidateForm.reset();
    }

    search(reset = false): void {
        let searchFilter: any = {};
        Object.keys(this.searchValidateForm.controls).forEach(k => {
            searchFilter[k] = this.searchValidateForm.controls[k]['value'];
        });
        if (reset) {
            searchFilter = {};
            this.pageIndex = 1;
        }
        this.getList(this.pageIndex, this.pageSize, searchFilter);
    }

    onPageIndexChange(pageIndex: number): void {
        this.pageIndex = pageIndex;
        this.search();
    }

    onPageSizeChange(pageSize: number): void {
        this.pageSize = pageSize;
        this.tablePageSizeService.setPageSize(pageSize);
        this.search();
    }

    getList(pageIndex: number = 1, pageSize: number = 20, search: { [k: string]: string }): void {
        this.listLoading = true;
        this.ComponentsLibraryService.getList(pageIndex, pageSize, search).subscribe(res => {
            console.log(res);
            this.listData = res.data.list;
            this.total = res.data.total;
            this.pageIndex = res.data.pn;
            this.pageSize = res.data.ps;
            this.listLoading = false;
        });
    }

    copySuccess(): void {
        this.messageService.success('复制成功');
    }
    // #endregion

    // #region 组件详情
    showComponentInfoModal(data?: any): void {
        this.componentInfoModalVisiable = true;
        this.initComponentForm();
        this.treeData = JSON.parse(JSON.stringify(TREE_DATA));
        this.dataSource.setData(this.treeData);
        this.treeControl.expandAll();
        if (data) {
            this.componentForm.patchValue(data);
        }
    }

    hideComponentInfoModal(): void {
        this.componentInfoModalVisiable = false;
        this.curComponent = null;
    }

    submitComponentInfo(): void {
        console.log(this.componentForm.value);
        if (this.componentForm.invalid) {
            // 递归检查所有子表单组
            this.validateAllFormFields(this.componentForm);
            this.messageService.error('请检查表单填写是否正确！');
            return;
        }
        if (this.curComponent) {
            this.componentForm.addControl('_id', this.fb.control(this.curComponent._id));
            this.componentForm.addControl('elements', this.fb.control(this.treeData[0]));
            this.ComponentsLibraryService.updateComponentConfig(this.componentForm.value).subscribe(res => {
                this.hideComponentInfoModal();
                this.getList(this.pageIndex, this.pageSize, {});
            });
        } else {
            this.componentForm.addControl('elements', this.fb.control(this.treeData[0]));
            this.ComponentsLibraryService.createComponentConfig(this.componentForm.value).subscribe(res => {
                this.hideComponentInfoModal();
                this.getList(this.pageIndex, this.pageSize, {});
            });
        }
    }

    editComponent(component: any): void {
        this.initComponentForm();
        this.curComponent = component;
        this.componentInfoModalVisiable = true;
        this.treeData = [JSON.parse(JSON.stringify(component.elements))];
        this.dataSource.setData(this.treeData);
        this.treeControl.expandAll();

        // 通用方法处理表单数组
        // const addFormGroups = (formArray: FormArray, componentArray: any[], createGroup: () => FormGroup) => {
        //     if (componentArray) {
        //         const neededGroups = componentArray.length - 1;
        //         for (let i = 0; i < neededGroups; i++) {
        //             formArray.push(createGroup());
        //         }
        //     }
        // };

        // // 处理各表单数组
        // addFormGroups(this.elements, component.elements, () => this.createElementList());
        // // 处理带嵌套结构的参数
        // component.elements.forEach((element: any, index: number) => {
        //     const listControl = this.elements.at(index);
        //     addFormGroups(this.getElementList(listControl), element.list, () => this.createElementGroup());
        // });
        this.componentForm.patchValue(component);
    }

    initComponentForm(): void {
        this.componentForm = this.fb.group({
            name: ['', Validators.required],
            label: ['', Validators.required],
            image: ['', Validators.required],
            remark: ['']
        });
    }

    showDeleteComponentModal(component: any): void {
        this.curEditComponent = component;
        this.deleteComponentModalVisible = true;
    }

    hideDeleteComponentModal(): void {
        this.deleteComponentModalVisible = false;
        this.curEditComponent = null;
    }

    deleteComponent(): void {
        this.isOkLoading = true;
        this.ComponentsLibraryService.deleteComponentConfig(this.curEditComponent._id).subscribe(res => {
            console.log(res);
            if (res.code === 0) {
                this.messageService.success('删除成功');
                this.hideDeleteComponentModal();
                this.getList(this.pageIndex, this.pageSize, {});
                this.isOkLoading = false;
            } else {
                this.messageService.error('删除失败,' + res.msg);
            }
        });
    }

    // 新增启动和停用组件的方法
    showSetComponentStatusModal(component: any): void {
        this.curEditComponent = component;
        this.SetComponentStatusModalVisible = true;
    }

    SetComponentStart(): void {
        this.isOkLoading = true;
        const newStatus = this.COMPONENT_STATUS['Active']
        this.ComponentsLibraryService.startComponent({ _id: this.curEditComponent._id }).subscribe(res => {
            console.log(res);
            if (res.code === 0) {
                this.messageService.success('操作成功');
                this.hideSetComponentStatusModal();
                this.getList(this.pageIndex, this.pageSize, {});
                this.isOkLoading = false;
            } else {
                this.messageService.error('操作失败,' + res.msg);
                this.isOkLoading = false;
            }
        });
    }

    hideSetComponentStatusModal(): void {
        this.SetComponentStatusModalVisible = false;
        this.curEditComponent = null;
    }

    // #endregion

    // 递归验证所有表单字段
    private validateAllFormFields(formGroup: FormGroup): void {
        Object.keys(formGroup.controls).forEach(field => {
            const control = formGroup.get(field);
            if (control instanceof FormArray) {
                control.controls.forEach((c: AbstractControl) => {
                    if (c instanceof FormGroup) {
                        this.validateAllFormFields(c);
                    } else {
                        c.markAsDirty();
                        c.updateValueAndValidity({ onlySelf: true });
                    }
                });
            } else if (control instanceof FormGroup) {
                this.validateAllFormFields(control);
            } else {
                control?.markAsDirty();
                control?.updateValueAndValidity({ onlySelf: true });
            }
        });
    }

    addOption(): void {
        if (this.optionValue.trim()) {
            const optionsArray = this.elementForm.get('options')?.value;
            optionsArray.push(this.optionValue);
            this.optionValue = '';
        }
    }
    removeOption(index: number): void {
        const optionsArray = this.elementForm.get('options')?.value;
        // const index = optionsArray.controls.indexOf(oIndex);
        optionsArray.removeAt(index);
    }

    private transformer = (node: TreeNode, level: number): FlatNode => {
        const existingNode = this.nestedNodeMap.get(node);
        const flatNode =
            existingNode && existingNode.key === node.key
                ? existingNode
                : {
                    expandable: !!node.contentType && (node.contentType.includes('object') || node.contentType.includes('list')),
                    name: node.name,
                    level,
                    key: node.key
                };
        flatNode.name = node.name;
        this.flatNodeMap.set(flatNode, node);
        this.nestedNodeMap.set(node, flatNode);
        return flatNode;
    };

    treeData = TREE_DATA;
    flatNodeMap = new Map<FlatNode, TreeNode>();
    nestedNodeMap = new Map<TreeNode, FlatNode>();
    selectListSelection = new SelectionModel<FlatNode>(true);

    treeControl = new FlatTreeControl<FlatNode>(
        node => node.level,
        node => node.expandable
    );
    treeFlattener = new NzTreeFlattener(
        this.transformer,
        node => node.level,
        node => node.expandable,
        node => node.children,
    );

    dataSource = new NzTreeFlatDataSource(this.treeControl, this.treeFlattener);

    hasChild = (_: number, node: any): boolean => node.expandable;
    hasNoContent = (_: number, node: FlatNode): boolean => node.name === '';
    trackBy = (_: number, node: FlatNode): string => `${node.key}-${node.name}`;

    delete(node: FlatNode): void {
        const originNode = this.flatNodeMap.get(node);

        const dfsParentNode = (): TreeNode | null => {
            const stack = [...this.treeData];
            while (stack.length > 0) {
                const n = stack.pop()!;
                if (n.children) {
                    if (n.children.find(e => e === originNode)) {
                        return n;
                    }

                    for (let i = n.children.length - 1; i >= 0; i--) {
                        stack.push(n.children[i]);
                    }
                }
            }
            return null;
        };

        const parentNode = dfsParentNode();
        if (parentNode && parentNode.children) {
            parentNode.children = parentNode.children.filter(e => e !== originNode);
        }

        this.dataSource.setData(this.treeData);
    }
    addNewNode(node: FlatNode): void {
        const parentNode = this.flatNodeMap.get(node);
        if (parentNode) {
            parentNode.children = parentNode.children || [];
            let newNode: any = {
                name: this.elementForm.get('label')?.value || '',
                key: this.elementForm.get('name')?.value || '',
                contentType: this.elementForm.get('contentType')?.value || '',
                required: this.elementForm.get('required')?.value || false,
                defaultValueType: this.elementForm.get('defaultValueType')?.value || '',
                defaultValue: this.elementForm.get('defaultValue')?.value || '',
                options: this.elementForm.get('options')?.value || [],
            }

            parentNode.children.push(newNode);
            this.dataSource.setData(this.treeData);
            this.treeControl.expand(node);
            this.hideElementDrawer();
        }
    }

    saveNode(node: FlatNode): void {
        const nestedNode = this.flatNodeMap.get(node);

        if (nestedNode) {
            nestedNode.name = this.elementForm.get('label')?.value || '';
            nestedNode.key = this.elementForm.get('name')?.value || '';
            nestedNode.contentType = this.elementForm.get('contentType')?.value || '';
            nestedNode.required = this.elementForm.get('required')?.value || false;
            nestedNode.defaultValueType = this.elementForm.get('defaultValueType')?.value || '';
            nestedNode.defaultValue = this.elementForm.get('defaultValue')?.value || '';
            nestedNode.options = this.elementForm.get('options')?.value || [];
            this.dataSource.setData(this.treeData);
            this.hideElementDrawer();
        }
    }


    elementDrawerVisible: boolean = false;
    elementForm: FormGroup;
    curElementNode: any = null;
    drawerType: string = 'add';
    showElementDrawer(node: FlatNode, type = 'add'): void {
        this.elementDrawerVisible = true;
        this.curElementNode = node;
        this.drawerType = type;
        this.initElementForm();
        if (type === 'edit') {
            const nestedNode = this.flatNodeMap.get(node);
            if (nestedNode) {
                this.elementForm.patchValue({
                    name: nestedNode.key,
                    label: nestedNode.name,
                    contentType: nestedNode.contentType,
                    required: nestedNode.required,
                    defaultValueType: nestedNode.defaultValueType,
                    defaultValue: nestedNode.defaultValue,
                    options: nestedNode.options,
                });
            }
        }
        console.log(this.curElementNode);

    }
    hideElementDrawer(): void {
        this.elementDrawerVisible = false;
    }

    initElementForm(): void {
        this.elementForm = this.fb.group({
            name: ['', Validators.required],
            label: ['', Validators.required],
            contentType: [['text'], Validators.required],
            required: [true, Validators.required],
            defaultValueType: [['text']],
            defaultValue: [''],
            options: [[]],
        });
    }

    submitElementNode(): void {
        if (this.elementForm.invalid) {
            // 递归检查所有子表单组
            this.validateAllFormFields(this.elementForm);
            this.messageService.error('请检查表单填写是否正确！');
            return;
        }
        if (this.drawerType === 'edit') {
            this.saveNode(this.curElementNode);
        } else {
            this.addNewNode(this.curElementNode);
        }
    }
}












