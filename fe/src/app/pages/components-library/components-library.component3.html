<form nz-form [formGroup]="searchValidateForm" class="ant-advanced-search-form">
    <div nz-row [nzGutter]="24">
        @for (control of searchControlArray; track control['name']) {
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>{{ control['labelName'] }}</nz-form-label>
                <nz-form-control>
                    <input nz-input [placeholder]="control['labelName']" [formControlName]="control['name']"
                        [attr.id]="control['name']" />
                </nz-form-control>
            </nz-form-item>
        </div>
        }
    </div>
    <div nz-row>
        <div nz-col [nzSpan]="24" class="search-area">
            <button nz-button [nzType]="'primary'" (click)="search()">搜索</button>
            <button nz-button (click)="resetForm()">重置</button>
            <button nz-button [nzType]="'primary'" (click)="showComponentInfoModal()">新增组件</button>
        </div>
    </div>
</form>

<div class="search-result-list">
    <nz-table #columnTable nzShowSizeChanger nzShowQuickJumper [nzData]="listData" [nzFrontPagination]="false"
        nzPaginationType="small" [nzPageSizeOptions]="[10,20,30,40,50]" [(nzPageSize)]="pageSize"
        [(nzPageIndex)]="pageIndex" [nzTotal]="total" [nzLoading]="listLoading"
        (nzPageIndexChange)="onPageIndexChange(pageIndex)" (nzPageSizeChange)="onPageSizeChange(pageSize)"
        [nzScroll]="{ x: '1000px' }">
        <thead>
            <tr>
                <th width="50px">编号</th>
                <th width="100px">名称</th>
                <th width="100px">组件label</th>
                <th width="200px">组件展示图</th>
                <th width="180px">创建时间</th>
                <th width="100px">ID</th>
                <th nzRight width="100px">操作</th>
            </tr>
        </thead>
        <tbody>
            @for (data of columnTable.data; track data['_id']) {
            <tr>
                <td width="50px">{{(pageIndex - 1) * pageSize + $index + 1}}</td>
                <td width="100px">{{data['name']}}</td>
                <td width="100px">{{data['label']}}</td>
                <td width="200px">
                    <img class="component-img" nz-image width="100%" [nzSrc]="data['image']" alt="" />
                </td>
                <td width="180px">{{data['created_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="100px">
                    <span ngxClipboard [cbContent]="data['_id']" (cbOnSuccess)="copySuccess()"
                        style="cursor: pointer; color: blue; text-decoration: underline;" nz-tooltip
                        nzTooltipTitle="点击复制ID">{{data['_id']}}</span>
                </td>
                <td nzRight width="100px">
                    <a (click)="editComponent(data)">编辑</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="showDeleteComponentModal(data)">删除</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <!-- 新增启动和停用按钮 -->
                    <a (click)="showSetComponentStatusModal(data)"
                        *ngIf="data['status'] == COMPONENT_STATUS['Inactive']">{{BTN_STATUS[data['status']]}}</a>
                </td>
            </tr>
            }
        </tbody>
    </nz-table>
</div>

<nz-modal [(nzVisible)]="componentInfoModalVisiable" nzTitle="组件信息" (nzOnCancel)="hideComponentInfoModal()"
    (nzOnOk)="submitComponentInfo()" [nzOkLoading]="isOkLoading" [nzWidth]="'100vw'">
    <div *nzModalContent class="modal-content">
        <form nz-form [formGroup]="dataForm">
            <!-- Component Name -->
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>组件名称</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <input nz-input formControlName="name" placeholder="请输入模版名称(只能大小写字母)" pattern="^[A-Za-z]+$">
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>组件label</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <input nz-input formControlName="label" placeholder="请输入组件label">
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>组件图片</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-image-upload-form uploadUrl="/upload-file/image"
                        formControlName="image"></app-image-upload-form>
                </nz-form-control>
            </nz-form-item>
            <!-- 组件元素 -->
            <nz-divider nzText="组件元素"></nz-divider>
            <div formArrayName="list">
                <div class="form-group-container"
                    *ngFor="let item of getFormArray(dataForm,'list').controls; let i = index" [formGroupName]="i">
                    <div class="form-item-title">元素 {{i+1}}</div>
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="2" nzRequired>元素名</nz-form-label>
                        <nz-form-control>
                            <input nz-input formControlName="name" placeholder="请输入组件name" pattern="[a-zA-Z0-9_]+">
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="2" nzRequired>元素label</nz-form-label>
                        <nz-form-control>
                            <input nz-input formControlName="label" placeholder="请输入组件label">
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="2" nzRequired>内容类型</nz-form-label>
                        <nz-form-control>
                            <nz-select nzPlaceHolder="选择元素类型(可多选)" formControlName="contentType" nzMode="multiple"
                                style="width: 100%">
                                @for (option of RESOURCE_TYPE_OPTIONS; track $index) {
                                <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                                }
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="2" nzRequired>是否必填</nz-form-label>
                        <nz-form-control>
                            <nz-select formControlName="required" style="width: 100%">
                                @for (option of REQUIRE_TYPE_OPTIONS; track $index) {
                                <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                                }
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                    @if(i> 0){
                    <button class="delete-form-item-btn delete-element-list" nz-button nzType="primary" nzSize="small"
                        nzShape="circle" nz-popconfirm nzPopconfirmTitle="确定删除吗?" nzOkText="确定" nzCancelText="取消"
                        (nzOnConfirm)="deleteItemFromFormArray(i, getFormArray(dataForm,'list'))" nzDanger>
                        <nz-icon nzType="delete" nzTheme="outline" />
                    </button>
                    }
                    <div formArrayName="list">
                        <nz-form-item class="form-item"
                            *ngFor="let el of getFormArray(item, 'list').controls; let j = index" [formGroupName]="j">
                            @if(j >= 0){
                            <button class="delete-form-item-btn" nz-button nzType="primary" nzSize="small"
                                nzShape="circle" nz-popconfirm nzPopconfirmTitle="确定删除吗?" nzOkText="确定"
                                nzCancelText="取消" (nzOnConfirm)="deleteItemFromFormArray(j, getFormArray(item, 'list'))"
                                nzDanger>
                                <nz-icon nzType="delete" nzTheme="outline" />
                            </button>
                            }
                            <nz-form-label [nzSpan]="2" nzRequired>元素名称</nz-form-label>
                            <div nz-col>
                                <nz-form-control>
                                    <input nz-input formControlName="name" placeholder="请输入模版名称(只能大小写字母)"
                                        pattern="^[A-Za-z]+$">
                                </nz-form-control>
                            </div>
                            <nz-form-label nzRequired>元素label</nz-form-label>
                            <div nz-col>
                                <nz-form-control>
                                    <input nz-input formControlName="label" placeholder="元素label">
                                </nz-form-control>
                            </div>
                            <nz-form-label nzRequired>内容类型</nz-form-label>
                            <div nz-col>
                                <nz-form-control>
                                    <nz-select nzPlaceHolder="选择元素类型(可多选)" formControlName="contentType"
                                        nzMode="multiple" style="width: 100%">
                                        @for (item of RESOURCE_TYPE_OPTIONS; track $index) {
                                        <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
                                        }
                                    </nz-select>
                                </nz-form-control>
                            </div>
                            <nz-form-label nzRequired>是否必填</nz-form-label>
                            <div nz-col>
                                <nz-form-control>
                                    <input nz-input formControlName="required" placeholder="是否必填">
                                </nz-form-control>
                            </div>
                        </nz-form-item>
                        <button class="add-form-item-btn" nz-button nzType="dashed"
                            (click)="addDataItemGroupToArray(getFormArray(item, 'list'))">增加子元素</button>
                    </div>
                    <!-- <nz-form-item class="form-item count-type">
                        <nz-form-label [nzSpan]="6" nzRequired>数量类型</nz-form-label>
                        <div nz-col [nzSpan]="14">
                            <nz-form-control>
                                <nz-select nzPlaceHolder="选择数量类型" formControlName="type" style="width: 100%">
                                    @for (item of ELEMENT_TYPE_OPTIONS; track $index) {
                                    <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                        </div>
                    </nz-form-item>
                    <nz-form-item class="form-item count-type">
                        <nz-form-label [nzSpan]="6" nzRequired>此元素总名</nz-form-label>
                        <div nz-col [nzSpan]="14">
                            <nz-form-control>
                                <input nz-input formControlName="name" placeholder="元素块名称(只能大小写字母)"
                                    pattern="^[A-Za-z]+$">
                            </nz-form-control>
                        </div>
                    </nz-form-item> -->
                </div>
                <button class="add-form-item-btn" nz-button nzType="dashed"
                    (click)="addDataItemGroupToArray(getFormArray(dataForm,'list'))">增加组件元素</button>

            </div>

            <!-- Remark -->
            <nz-divider nzText="备注"></nz-divider>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6">备注</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <input nz-input formControlName="remark" placeholder="请输入备注">
                </nz-form-control>
            </nz-form-item>

        </form>
    </div>
</nz-modal>

<nz-modal [(nzVisible)]="deleteComponentModalVisible" nzTitle="删除组件" (nzOnCancel)="hideDeleteComponentModal()"
    (nzOnOk)="deleteComponent()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        @if (curEditComponent) {
        确定要删除<span class="component-name">{{ curEditComponent.name }}</span> 吗？
        }
    </div>
</nz-modal>