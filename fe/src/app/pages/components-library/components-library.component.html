<form nz-form [formGroup]="searchValidateForm" class="ant-advanced-search-form">
    <div nz-row [nzGutter]="24">
        @for (control of searchControlArray; track control['name']) {
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>{{ control['labelName'] }}</nz-form-label>
                <nz-form-control>
                    <input nz-input [placeholder]="control['labelName']" [formControlName]="control['name']"
                        [attr.id]="control['name']" />
                </nz-form-control>
            </nz-form-item>
        </div>
        }
    </div>
    <div nz-row>
        <div nz-col [nzSpan]="24" class="search-area">
            <button nz-button [nzType]="'primary'" (click)="search()">搜索</button>
            <button nz-button (click)="resetForm()">重置</button>
            <button nz-button [nzType]="'primary'" (click)="showComponentInfoModal()">新增组件</button>
        </div>
    </div>
</form>

<div class="search-result-list">
    <nz-table #columnTable nzShowSizeChanger nzShowQuickJumper [nzData]="listData" [nzFrontPagination]="false"
        nzPaginationType="small" [nzPageSizeOptions]="[10,20,30,40,50]" [(nzPageSize)]="pageSize"
        [(nzPageIndex)]="pageIndex" [nzTotal]="total" [nzLoading]="listLoading"
        (nzPageIndexChange)="onPageIndexChange(pageIndex)" (nzPageSizeChange)="onPageSizeChange(pageSize)"
        [nzScroll]="{ x: '1000px' }">
        <thead>
            <tr>
                <th width="50px">编号</th>
                <th width="100px">名称</th>
                <th width="100px">组件label</th>
                <th width="200px">组件展示图</th>
                <th width="180px">创建时间</th>
                <th width="100px">ID</th>
                <th nzRight width="100px">操作</th>
            </tr>
        </thead>
        <tbody>
            @for (data of columnTable.data; track data['_id']) {
            <tr>
                <!-- <td width="50px">{{(pageIndex - 1) * pageSize + $index + 1}}</td> -->
                <td width="50px">{{$index + 1}}</td>
                <td width="100px">{{data['name']}}</td>
                <td width="100px">{{data['label']}}</td>
                <td width="200px">
                    <img class="component-img" nz-image width="100%" [nzSrc]="data['image']" alt="" />
                </td>
                <td width="180px">{{data['created_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="100px">
                    <span ngxClipboard [cbContent]="data['_id']" (cbOnSuccess)="copySuccess()"
                        style="cursor: pointer; color: blue; text-decoration: underline;" nz-tooltip
                        nzTooltipTitle="点击复制ID">{{data['_id']}}</span>
                </td>
                <td nzRight width="100px">
                    <a (click)="editComponent(data)">编辑</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="showDeleteComponentModal(data)">删除</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <!-- 新增启动和停用按钮 -->
                    <!-- <a (click)="showSetComponentStatusModal(data)"
                        *ngIf="data['status'] == COMPONENT_STATUS['Inactive']">{{BTN_STATUS[data['status']]}}</a> -->
                </td>
            </tr>
            }
        </tbody>
    </nz-table>
</div>

<nz-modal [(nzVisible)]="componentInfoModalVisiable" nzTitle="组件信息" (nzOnCancel)="hideComponentInfoModal()"
    (nzOnOk)="submitComponentInfo()" [nzOkLoading]="isOkLoading" [nzWidth]="'100vw'" nzZIndex="1"
    [nzStyle]="{top: '10px'}">
    <div *nzModalContent class="modal-content">
        <form nz-form [formGroup]="componentForm">
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>组件名称</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <input nz-input formControlName="name" placeholder="请输入模版名称(只能大小写字母)" pattern="^[A-Za-z0-9]+$">
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>组件label</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <input nz-input formControlName="label" placeholder="请输入组件label">
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>组件图片</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-image-upload-form uploadUrl="/upload-file/image"
                        formControlName="image"></app-image-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-divider nzText="组件元素"></nz-divider>
            <nz-tree-view [nzTreeControl]="treeControl" [nzDataSource]="dataSource" [trackBy]="trackBy">
                <nz-tree-node *nzTreeNodeDef="let node" nzTreeNodeIndentLine>
                    <nz-tree-node-option [nzDisabled]="node.disabled"
                        [nzSelected]="selectListSelection.isSelected(node)"
                        (nzClick)="selectListSelection.toggle(node)">
                        <span (click)="showElementDrawer(node,'edit')">{{ node.name }}</span>
                    </nz-tree-node-option>
                    <button nz-button nzType="text" nzSize="small" (click)="delete(node)">
                        <span nz-icon nzType="minus" nzTheme="outline"></span>
                    </button>
                </nz-tree-node>
                <nz-tree-node *nzTreeNodeDef="let node; when: hasChild" nzTreeNodeIndentLine>
                    <nz-tree-node-toggle>
                        <span nz-icon nzType="caret-down" nzTreeNodeToggleRotateIcon></span>
                    </nz-tree-node-toggle>
                    <span (click)="showElementDrawer(node, 'edit')">{{ node.name }}</span>
                    <button nz-button nzType="text" nzSize="small" (click)="showElementDrawer(node)">
                        <span nz-icon nzType="plus" nzTheme="outline"></span>
                    </button>
                    <button nz-button nzType="text" nzSize="small" (click)="delete(node)">
                        <span nz-icon nzType="minus" nzTheme="outline"></span>
                    </button>
                </nz-tree-node>
            </nz-tree-view>

            <nz-divider nzText="备注"></nz-divider>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6">备注</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <input nz-input formControlName="remark" placeholder="请输入备注">
                </nz-form-control>
            </nz-form-item>

        </form>
    </div>
</nz-modal>

<nz-drawer [nzClosable]="true" [nzVisible]="elementDrawerVisible" nzPlacement="right" nzTitle="元素信息" [nzWidth]="'500px'"
    [nzMaskClosable]="true" [nzFooter]="footerTpl" [nzZIndex]="2" (nzOnClose)="hideElementDrawer()">
    <div *nzDrawerContent class="page-model-edit-drawer">
        <form nz-form [formGroup]="elementForm">
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>元素名称</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <input nz-input formControlName="name" placeholder="请输入模版名称(只能大小写字母)" pattern="^[A-Za-z]+$">
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>元素label</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <input nz-input formControlName="label" placeholder="请输入组件label">
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>元素类型</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <nz-select nzPlaceHolder="选择元素类型(可多选)" formControlName="contentType" nzMode="multiple"
                        style="width: 100%">
                        @for (option of RESOURCE_TYPE_OPTIONS; track $index) {
                        <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        }
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementForm.controls['contentType'].value.includes('select')">
                <nz-form-label [nzSpan]="6" nzRequired>选项</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <div class="options" style="margin-bottom: 8px;">
                        @for (tag of elementForm.controls['options'].value; track tag) {
                        <nz-tag [nzMode]="'closeable'" (nzOnClose)="removeOption($index)" style="margin-bottom: 8px;">
                            {{ tag}}
                        </nz-tag>
                        }
                    </div>
                    <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                        <input type="text" nz-input placeholder="请输入选项" [(ngModel)]="optionValue"
                            [ngModelOptions]="{standalone: true}" />
                    </nz-input-group>
                    <ng-template #suffixIconButton>
                        <button nz-button nzType="primary" nzSearch><span nz-icon nzType="plus"
                                (click)="addOption()"></span></button>
                    </ng-template>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>是否必填</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <nz-select formControlName="required" style="width: 100%">
                        @for (option of REQUIRE_TYPE_OPTIONS; track $index) {
                        <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        }
                    </nz-select>
                </nz-form-control>
            </nz-form-item>

            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6">默认值类型</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <nz-select nzPlaceHolder="选择默认值类型" formControlName="defaultValueType" style="width: 100%">
                        @for (option of DEFAULT_VALUE_TYPE_OPTIONS; track $index) {
                        <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        }
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementForm.controls['defaultValueType'].value === 'text'">
                <nz-form-label [nzSpan]="6">默认值</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <input nz-input formControlName="defaultValue" placeholder="请输入默认值">
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementForm.controls['defaultValueType'].value === 'image'">
                <nz-form-label [nzSpan]="6">默认图片</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-image-upload-form uploadUrl="/upload-file/image"
                        formControlName="defaultValue"></app-image-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item *ngIf="elementForm.controls['defaultValueType'].value === 'video'">
                <nz-form-label [nzSpan]="6">默认视频</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-video-upload-form uploadUrl="/upload-file/video" formControlName="defaultValue">
                    </app-video-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item *ngIf="elementForm.controls['defaultValueType'].value === 'file'">
                <nz-form-label [nzSpan]="6">默认文件</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-file-upload-form uploadUrl="/upload-file/file" formControlName="defaultValue">
                    </app-file-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementForm.controls['defaultValueType'].value === 'palette'">
                <nz-form-label [nzSpan]="6">默认颜色</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <input nz-input formControlName="defaultValue" placeholder="请输入颜色值或点击选择颜色" type="color" style="height: 40px;">
                </nz-form-control>
            </nz-form-item>
        </form>
    </div>
    <ng-template #footerTpl>
        <div style="float: right">
            <button nz-button style="margin-right: 8px;" (click)="hideElementDrawer()">Cancel</button>
            <button nz-button nzType="primary" (click)="submitElementNode()">提交</button>
        </div>
    </ng-template>
</nz-drawer>

<nz-modal [(nzVisible)]="deleteComponentModalVisible" nzTitle="删除组件" (nzOnCancel)="hideDeleteComponentModal()"
    (nzOnOk)="deleteComponent()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        @if (curEditComponent) {
        确定要删除<span class="component-name">{{ curEditComponent.name }}</span> 吗？
        }
    </div>
</nz-modal>