<form nz-form [formGroup]="searchValidateForm" class="ant-advanced-search-form">
    <div nz-row [nzGutter]="24">
        @for (control of searchControlArray; track control['name']) {
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>{{ control['labelName'] }}</nz-form-label>
                <nz-form-control>
                    <input nz-input [placeholder]="control['labelName']" [formControlName]="control['name']"
                        [attr.id]="control['name']" />
                </nz-form-control>
            </nz-form-item>
        </div>
        }
    </div>
    <div nz-row>
        <div nz-col [nzSpan]="24" class="search-area">
            <button nz-button [nzType]="'primary'" (click)="search()">搜索</button>
            <button nz-button (click)="resetForm()">重置</button>
            <button nz-button [nzType]="'primary'" (click)="showHomeInfoModal()">新增首页</button>
        </div>
    </div>
</form>

<div class="search-result-list">
    <nz-table #columnTable nzShowSizeChanger nzShowQuickJumper [nzData]="listData" [nzFrontPagination]="false"
        nzPaginationType="small" [nzPageSizeOptions]="[10,20,30,40,50]" [(nzPageSize)]="pageSize"
        [(nzPageIndex)]="pageIndex" [nzTotal]="total" [nzLoading]="listLoading"
        (nzPageIndexChange)="onPageIndexChange(pageIndex)" (nzPageSizeChange)="onPageSizeChange(pageSize)"
        [nzScroll]="{ x: '1000px' }">
        <thead>
            <tr>
                <th width="50px">编号</th>
                <th width="100px">名称</th>
                <th width="180px">创建时间</th>
                <th width="100px">状态</th>
                <th width="100px">ID</th>
                <th nzRight width="100px">操作</th>
            </tr>
        </thead>
        <tbody>
            @for (data of columnTable.data; track data['_id']) {
            <tr>
                <td width="50px">{{(pageIndex - 1) * pageSize + $index + 1}}</td>
                <td width="100px">{{data['homeName']}}</td>
                <td width="180px">{{data['created_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="100px">{{STATUS_TYPE_STR[data['status']]}}</td>
                <td width="100px">
                    <span ngxClipboard [cbContent]="data['_id']" (cbOnSuccess)="copySuccess()"
                        style="cursor: pointer; color: blue; text-decoration: underline;" nz-tooltip
                        nzTooltipTitle="点击复制ID">{{data['_id']}}</span>
                </td>
                <td nzRight width="100px">
                    <a (click)="editHome(data)">编辑</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a (click)="showDeleteHomeModal(data)">删除</a>
                    <nz-divider nzType="vertical"></nz-divider>
                    <!-- 新增启动和停用按钮 -->
                    <a (click)="showSetHomeStatusModal(data)"
                        *ngIf="data['status'] == HOME_STATUS['Inactive']">{{BTN_STATUS[data['status']]}}</a>
                </td>
            </tr>
            }
        </tbody>
    </nz-table>
</div>

<nz-modal [(nzVisible)]="SetHomeStatusModalVisible" nzTitle="更改首页状态" (nzOnCancel)="hideSetHomeStatusModal()"
    (nzOnOk)="SetHomeStart()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        确定要{{BTN_STATUS[curEditHome['status']]}}<span class="home-name">{{ curEditHome.homeName }}</span> 吗？
    </div>
</nz-modal>

<nz-modal [(nzVisible)]="homeInfoModalVisiable" nzTitle="首页信息" (nzOnCancel)="hideHomeInfoModal()"
    (nzOnOk)="submitHomeInfo()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent class="modal-content">
        <form nz-form [formGroup]="homeForm">
            <!-- Home Name -->
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>首页名称</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <input nz-input formControlName="homeName" placeholder="请输入首页名称">
                </nz-form-control>
            </nz-form-item>

            <!-- Banner -->
            <nz-divider nzText="Banner区1"></nz-divider>
            <div formArrayName="banner1">
                <div *ngFor="let banner of banner1.controls; let i = index" [formGroupName]="i">
                    <nz-form-item class="form-item">
                        @if(i > 0){
                        <button class="delete-form-item-btn" nz-button nzType="primary" nzSize="small" nzShape="circle"
                            nz-popconfirm nzPopconfirmTitle="确定删除吗?" nzOkText="确定" nzCancelText="取消"
                            (nzOnConfirm)="deleteFormGroupFromArray(i, banner1)" nzDanger>
                            <nz-icon nzType="delete" nzTheme="outline" />
                        </button>
                        }
                        <nz-form-label [nzSpan]="6" nzRequired>Banner {{ i + 1 }}</nz-form-label>
                        <div nz-col [nzSpan]="14">
                            <nz-form-control>
                                <nz-select nzPlaceHolder="选择类型" formControlName="type" style="width: 100%">
                                    @for (item of RESOURCE_TYPE_OPTIONS; track $index) {
                                    <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'image'">
                                <app-image-upload-form uploadUrl="/upload-file/image"
                                    formControlName="url"></app-image-upload-form>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'video'">
                                <app-video-upload-form uploadUrl="/upload-file/video" formControlName="url">
                                </app-video-upload-form>
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="title" placeholder="标题">
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="subTitle" placeholder="副标题">
                            </nz-form-control>
                        </div>
                    </nz-form-item>
                </div>
                <button class="add-form-item-btn" nz-button nzType="dashed"
                    (click)="addFormGroupToArray(banner1)">增加Banner</button>
            </div>
            <nz-divider nzText="Banner区2"></nz-divider>
            <div formArrayName="banner2">
                <div *ngFor="let banner of banner2.controls; let i = index" [formGroupName]="i">
                    <nz-form-item class="form-item">
                        @if(i > 0){
                        <button class="delete-form-item-btn" nz-button nzType="primary" nzSize="small" nzShape="circle"
                            nz-popconfirm nzPopconfirmTitle="确定删除吗?" nzOkText="确定" nzCancelText="取消"
                            (nzOnConfirm)="deleteFormGroupFromArray(i, banner2)" nzDanger>
                            <nz-icon nzType="delete" nzTheme="outline" />
                        </button>
                        }
                        <nz-form-label [nzSpan]="6" nzRequired>Banner {{ i + 1 }}</nz-form-label>
                        <div nz-col [nzSpan]="14">
                            <nz-form-control>
                                <nz-select nzPlaceHolder="选择类型" formControlName="type" style="width: 100%">
                                    @for (item of RESOURCE_TYPE_OPTIONS; track $index) {
                                    <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'image'">
                                <app-image-upload-form uploadUrl="/upload-file/image"
                                    formControlName="url"></app-image-upload-form>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'video'">
                                <app-video-upload-form uploadUrl="/upload-file/video" formControlName="url">
                                </app-video-upload-form>
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="title" placeholder="标题">
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="subTitle" placeholder="副标题">
                            </nz-form-control>
                        </div>
                    </nz-form-item>
                </div>
                <button class="add-form-item-btn" nz-button nzType="dashed"
                    (click)="addFormGroupToArray(banner2)">增加Banner</button>
            </div>
            <nz-divider nzText="Banner区3"></nz-divider>
            <div formArrayName="banner3">
                <div *ngFor="let banner of banner3.controls; let i = index" [formGroupName]="i">
                    <nz-form-item class="form-item">
                        @if(i > 0){
                        <button class="delete-form-item-btn" nz-button nzType="primary" nzSize="small" nzShape="circle"
                            nz-popconfirm nzPopconfirmTitle="确定删除吗?" nzOkText="确定" nzCancelText="取消"
                            (nzOnConfirm)="deleteFormGroupFromArray(i, banner3)" nzDanger>
                            <nz-icon nzType="delete" nzTheme="outline" />
                        </button>
                        }
                        <nz-form-label [nzSpan]="6" nzRequired>Banner {{ i + 1 }}</nz-form-label>
                        <div nz-col [nzSpan]="14">
                            <nz-form-control>
                                <nz-select nzPlaceHolder="选择类型" formControlName="type" style="width: 100%">
                                    @for (item of RESOURCE_TYPE_OPTIONS; track $index) {
                                    <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'image'">
                                <app-image-upload-form uploadUrl="/upload-file/image"
                                    formControlName="url"></app-image-upload-form>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'video'">
                                <app-video-upload-form uploadUrl="/upload-file/video" formControlName="url">
                                </app-video-upload-form>
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="title" placeholder="标题">
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="subTitle" placeholder="副标题">
                            </nz-form-control>
                        </div>
                    </nz-form-item>
                </div>
                <button class="add-form-item-btn" nz-button nzType="dashed"
                    (click)="addFormGroupToArray(banner3)">增加Banner</button>
            </div>


            <nz-divider nzText="products"></nz-divider>
            <div formArrayName="products">
                <div *ngFor="let item of products.controls; let i = index" [formGroupName]="i">
                    <nz-form-item class="form-item">
                        @if(i > 0){
                        <button class="delete-form-item-btn" nz-button nzType="primary" nzSize="small" nzShape="circle"
                            nz-popconfirm nzPopconfirmTitle="确定删除吗?" nzOkText="确定" nzCancelText="取消"
                            (nzOnConfirm)="deleteFormGroupFromArray(i, products)" nzDanger>
                            <nz-icon nzType="delete" nzTheme="outline" />
                        </button>
                        }
                        <nz-form-label [nzSpan]="6" nzRequired>产品 {{ i + 1 }}</nz-form-label>
                        <div nz-col [nzSpan]="14">
                            <nz-form-control>
                                <nz-select nzPlaceHolder="选择类型" formControlName="type" style="width: 100%">
                                    @for (item of RESOURCE_TYPE_OPTIONS; track $index) {
                                    <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                            <nz-form-control *ngIf="item.value.type == 'image'">
                                <app-image-upload-form uploadUrl="/upload-file/image"
                                    formControlName="url"></app-image-upload-form>
                            </nz-form-control>
                            <nz-form-control *ngIf="item.value.type == 'video'">
                                <app-video-upload-form uploadUrl="/upload-file/video" formControlName="url">
                                </app-video-upload-form>
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="title" placeholder="标题">
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="subTitle" placeholder="副标题">
                            </nz-form-control>
                        </div>
                    </nz-form-item>
                </div>
                <button class="add-form-item-btn" nz-button nzType="dashed"
                    (click)="addFormGroupToArray(products)">增加Banner</button>
            </div>

            <nz-divider nzText="产品视频轮播区"></nz-divider>
            <div formArrayName="banner4">
                <div *ngFor="let banner of banner4.controls; let i = index" [formGroupName]="i">
                    <nz-form-item class="form-item">
                        @if(i > 0){
                        <button class="delete-form-item-btn" nz-button nzType="primary" nzSize="small" nzShape="circle"
                            nz-popconfirm nzPopconfirmTitle="确定删除吗?" nzOkText="确定" nzCancelText="取消"
                            (nzOnConfirm)="deleteFormGroupFromArray(i, banner4)" nzDanger>
                            <nz-icon nzType="delete" nzTheme="outline" />
                        </button>
                        }
                        <nz-form-label [nzSpan]="6" nzRequired>Banner {{ i + 1 }}</nz-form-label>
                        <div nz-col [nzSpan]="14">
                            <nz-form-control>
                                <nz-select nzPlaceHolder="选择类型" formControlName="type" style="width: 100%">
                                    @for (item of RESOURCE_TYPE_OPTIONS; track $index) {
                                    <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'image'">
                                <app-image-upload-form uploadUrl="/upload-file/image"
                                    formControlName="url"></app-image-upload-form>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'video'">
                                <app-video-upload-form uploadUrl="/upload-file/video" formControlName="url">
                                </app-video-upload-form>
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="title" placeholder="标题">
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="subTitle" placeholder="副标题">
                            </nz-form-control>
                        </div>
                    </nz-form-item>
                </div>
                <button class="add-form-item-btn" nz-button nzType="dashed"
                    (click)="addFormGroupToArray(banner4)">增加Banner</button>
            </div>

            <nz-divider nzText="解决方案"></nz-divider>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>解决方案标题</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <input nz-input formControlName="planBannerTitle" placeholder="请输入解决方案标题">
                </nz-form-control>
            </nz-form-item>
            <div formArrayName="planBanner">
                <div *ngFor="let banner of planBanner.controls; let i = index" [formGroupName]="i">
                    <nz-form-item class="form-item">
                        @if(i > 0){
                        <button class="delete-form-item-btn" nz-button nzType="primary" nzSize="small" nzShape="circle"
                            nz-popconfirm nzPopconfirmTitle="确定删除吗?" nzOkText="确定" nzCancelText="取消"
                            (nzOnConfirm)="deleteFormGroupFromArray(i, planBanner)" nzDanger>
                            <nz-icon nzType="delete" nzTheme="outline" />
                        </button>
                        }
                        <nz-form-label [nzSpan]="6" nzRequired>Banner {{ i + 1 }}</nz-form-label>
                        <div nz-col [nzSpan]="14">
                            <nz-form-control>
                                <nz-select nzPlaceHolder="选择类型" formControlName="type" style="width: 100%">
                                    @for (item of RESOURCE_TYPE_OPTIONS; track $index) {
                                    <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'image'">
                                <app-image-upload-form uploadUrl="/upload-file/image"
                                    formControlName="url"></app-image-upload-form>
                            </nz-form-control>
                            <nz-form-control *ngIf="banner.value.type == 'video'">
                                <app-video-upload-form uploadUrl="/upload-file/video" formControlName="url">
                                </app-video-upload-form>
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="title" placeholder="标题">
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="subTitle" placeholder="副标题">
                            </nz-form-control>
                        </div>
                    </nz-form-item>
                </div>
                <button class="add-form-item-btn" nz-button nzType="dashed"
                    (click)="addFormGroupToArray(planBanner)">增加Banner</button>
            </div>

            <!-- <nz-divider nzText="合作伙伴"></nz-divider>
            <div formArrayName="company">
                <div *ngFor="let item of company.controls; let i = index" [formGroupName]="i">
                    <nz-form-item class="form-item">
                        @if(i > 0){
                        <button class="delete-form-item-btn" nz-button nzType="primary" nzSize="small" nzShape="circle"
                            nz-popconfirm nzPopconfirmTitle="确定删除吗?" nzOkText="确定" nzCancelText="取消"
                            (nzOnConfirm)="deleteCompanyGroupFromArray(i, company)" nzDanger>
                            <nz-icon nzType="delete" nzTheme="outline" />
                        </button>
                        }
                        <nz-form-label [nzSpan]="6" nzRequired>伙伴 {{ i + 1 }}</nz-form-label>
                        <div nz-col [nzSpan]="14">
                            <nz-form-control *ngIf="item.value.type == 'image'">
                                <app-image-upload-form uploadUrl="/upload-file/image"
                                    formControlName="url"></app-image-upload-form>
                            </nz-form-control>
                            <nz-form-control>
                                <input nz-input formControlName="resourceUrl" placeholder="伙伴网址">
                            </nz-form-control>
                        </div>
                    </nz-form-item>
                </div>
                <button class="add-form-item-btn" nz-button nzType="dashed" (click)="addCompanyGroupToArray(company)">增加伙伴</button>
            </div> -->

            <!-- Remark -->
            <nz-divider nzText="备注"></nz-divider>
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6">备注</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <input nz-input formControlName="remark" placeholder="请输入备注">
                </nz-form-control>
            </nz-form-item>

        </form>
    </div>
</nz-modal>

<nz-modal [(nzVisible)]="deleteHomeModalVisible" nzTitle="删除首页" (nzOnCancel)="hideDeleteHomeModal()"
    (nzOnOk)="deleteHome()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        @if (curEditHome) {
        确定要删除<span class="home-name">{{ curEditHome.homeName }}</span> 吗？
        }
    </div>
</nz-modal>