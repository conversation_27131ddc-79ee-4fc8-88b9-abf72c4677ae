import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { HomeService } from './home.service';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { ImageUploadFormComponent } from '../../components/image-upload-form/image-upload-form.component';
import { VideoUploadFormComponent } from '../../components/video-upload-form/video-upload-form.component';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ClipboardModule } from 'ngx-clipboard';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzSelectModule } from 'ng-zorro-antd/select';


@Component({
  selector: 'app-home',
  imports: [
    ReactiveFormsModule,
    CommonModule,
    NzButtonModule,
    NzFormModule,
    ClipboardModule,
    NzToolTipModule,
    NzInputModule,
    NzIconModule,
    NzModalModule,
    NzTableModule,
    NzDividerModule,
    NzTreeModule,
    NzCardModule,
    NzPopconfirmModule,
    ImageUploadFormComponent,
    VideoUploadFormComponent,
    NzSelectModule,
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent {

  // 常量
  STATUS_TYPE_STR: { [key: string]: string } = {
    0: '展示中',
    1: '未使用'
  };
  BTN_STATUS: { [key: string]: string } = {
    0: '停用',
    1: '启用'
  };
  HOME_STATUS: { [key: string]: number } = {
    Active: 0, // 激活状态
    Inactive: 1, // 禁用状态
    Deleted: 3 // 已删除状态
  }
  RESOURCE_TYPE_OPTIONS = [
    {
      label: '图片',
      value: 'image'
    },
    {
      label: '视频',
      value: 'video'
    }
  ];

  // 变量
  searchValidateForm: FormGroup;
  searchControlArray = [
    {
      name: 'homeName',
      labelName: '首页名称'
    }
  ];
  pageIndex: number = 1;
  pageSize: number = 20;
  listLoading: boolean = false;
  listData: any;
  total: number = 0;
  curHome: any = null;
  homeForm: FormGroup;
  homeInfoModalVisiable: boolean = false;
  isOkLoading: boolean = false;
  deleteHomeModalVisible: boolean = false;
  curEditHome: any = null;
  SetHomeStatusModalVisible: boolean = false;

  constructor(
    private fb: FormBuilder,
    private homeService: HomeService,
    private messageService: NzMessageService
  ) {
    this.searchValidateForm = this.fb.group({});
    this.homeForm = this.fb.group({});
  }

  // 生命周期钩子
  ngOnInit(): void {
    this.searchControlArray.map((control) => {
      this.searchValidateForm.addControl(control.name, this.fb.control(''));
    });
    this.getList(this.pageIndex, this.pageSize, {});
    this.initHomeForm();
  }

  // #region 搜索
  resetForm(): void {
    this.searchValidateForm.reset();
  }

  search(reset = false): void {
    let searchFilter: any = {};
    Object.keys(this.searchValidateForm.controls).forEach(k => {
      searchFilter[k] = this.searchValidateForm.controls[k]['value'];
    });
    if (reset) {
      searchFilter = {};
      this.pageIndex = 1;
    }
    this.getList(this.pageIndex, this.pageSize, searchFilter);
  }

  onPageIndexChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.search();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.search();
  }

  getList(pageIndex: number = 1, pageSize: number = 20, search: { [k: string]: string }): void {
    this.listLoading = true;
    this.homeService.getList(pageIndex, pageSize, search).subscribe(res => {
      console.log(res);
      this.listData = res.data.list;
      this.total = res.data.total;
      this.pageIndex = res.data.pn;
      this.pageSize = res.data.ps;
      this.listLoading = false;
    });
  }

  copySuccess(): void {
    this.messageService.success('复制成功');
  }
  // #endregion

  // #region 首页详情
  showHomeInfoModal(data?: any): void {
    this.homeInfoModalVisiable = true;
    this.initHomeForm();
    if (data) {
      this.homeForm.patchValue(data);
    }
  }

  hideHomeInfoModal(): void {
    this.homeInfoModalVisiable = false;
  }

  submitHomeInfo(): void {
    console.log(this.homeForm.value);
    if (this.homeForm.invalid) {
      // 递归检查所有子表单组
      this.validateAllFormFields(this.homeForm);
      this.messageService.error('请检查表单填写是否正确！');
      return;
    }
    if (this.curHome) {
      this.homeForm.addControl('_id', this.fb.control(this.curHome._id));
      this.homeService.updateHomeConfig(this.homeForm.value).subscribe(res => {
        this.hideHomeInfoModal();
        this.getList(this.pageIndex, this.pageSize, {});
      });
    } else {
      this.homeService.createHomeConfig(this.homeForm.value).subscribe(res => {
        this.hideHomeInfoModal();
        this.getList(this.pageIndex, this.pageSize, {});
      });
    }
  }

  editHome(home: any): void {
    this.initHomeForm();
    this.curHome = home;
    this.homeInfoModalVisiable = true;

    // 通用方法处理表单数组
    const addFormGroups = (formArray: FormArray, homeArray: any[], createGroup: () => FormGroup) => {
      if (homeArray) {
        const neededGroups = homeArray.length - 1;
        for (let i = 0; i < neededGroups; i++) {
          formArray.push(createGroup());
        }
      }
    };

    // 处理各表单数组
    addFormGroups(this.banner1, home.banner1, () => this.createResourceGroup());
    addFormGroups(this.banner2, home.banner2, () => this.createResourceGroup());
    addFormGroups(this.banner3, home.banner3, () => this.createResourceGroup());
    addFormGroups(this.products, home.products, () => this.createResourceGroup());
    addFormGroups(this.planBanner, home.planBanner, () => this.createResourceGroup());
    // addFormGroups(this.company, home.company, () => this.createCompanyGroup());
    this.homeForm.patchValue(home);
  }

  initHomeForm(): void {
    this.homeForm = this.fb.group({
      homeName: ['', Validators.required],
      banner1: this.fb.array([this.createResourceGroup()]),
      banner2: this.fb.array([this.createResourceGroup()]),
      banner3: this.fb.array([this.createResourceGroup()]),
      products: this.fb.array([this.createResourceGroup()]),
      banner4: this.fb.array([this.createResourceGroup()]),
      planBannerTitle: ['', Validators.required],
      planBanner: this.fb.array([this.createResourceGroup()]),
      //合作伙伴
      // company: this.fb.array([this.createCompanyGroup()]),
      remark: ['']
    });
  }

  createResourceGroup(): FormGroup {
    return this.fb.group({
      type: [''],
      url: ['', Validators.required],
      title: ['', Validators.required],
      subTitle: ['', Validators.required],
      resourceId: [''],
      resourceUrl:[''],
    });
  }

  createCompanyGroup(): FormGroup {
    return this.fb.group({
      type: ['image'],
      url: ['', Validators.required],
      resourceUrl:['', Validators.required],
    });
  }


  get banner1(): FormArray {
    return this.homeForm.get('banner1') as FormArray;
  }

  get banner2(): FormArray {
    return this.homeForm.get('banner2') as FormArray;
  }

  get banner3(): FormArray {
    return this.homeForm.get('banner3') as FormArray;
  }

  get banner4(): FormArray {
    return this.homeForm.get('banner4') as FormArray;
  }

  get products(): FormArray {
    return this.homeForm.get('products') as FormArray;
  }

  get planBanner(): FormArray {
    return this.homeForm.get('planBanner') as FormArray;
  }

  get company(): FormArray {
    return this.homeForm.get('company') as FormArray;
  }

  addFormGroupToArray(formArray: FormArray): void {
    formArray.push(this.createResourceGroup());
  }

  deleteFormGroupFromArray(index: number, formArray: FormArray): void {
    formArray.removeAt(index);
  }

  addCompanyGroupToArray(formArray: FormArray): void {
    formArray.push(this.createCompanyGroup());
  }

  deleteCompanyGroupFromArray(index: number, formArray: FormArray): void {
    formArray.removeAt(index);
  }

  onSubmit(): void {
    console.log(this.homeForm.value);
  }

  showDeleteHomeModal(home: any): void {
    this.curEditHome = home;
    this.deleteHomeModalVisible = true;
  }

  hideDeleteHomeModal(): void {
    this.deleteHomeModalVisible = false;
    this.curEditHome = null;
  }

  deleteHome(): void {
    this.isOkLoading = true;
    this.homeService.deleteHomeConfig(this.curEditHome._id).subscribe(res => {
      console.log(res);
      if (res.code === 0) {
        this.messageService.success('删除成功');
        this.hideDeleteHomeModal();
        this.getList(this.pageIndex, this.pageSize, {});
        this.isOkLoading = false;
      } else {
        this.messageService.error('删除失败,' + res.msg);
      }
    });
  }

  // 新增启动和停用首页的方法
  showSetHomeStatusModal(home: any): void {
    this.curEditHome = home;
    this.SetHomeStatusModalVisible = true;
  }

  SetHomeStart(): void {
    this.isOkLoading = true;
    const newStatus = this.HOME_STATUS['Active']
    this.homeService.startHome({ _id: this.curEditHome._id }).subscribe(res => {
      console.log(res);
      if (res.code === 0) {
        this.messageService.success('操作成功');
        this.hideSetHomeStatusModal();
        this.getList(this.pageIndex, this.pageSize, {});
        this.isOkLoading = false;
      } else {
        this.messageService.error('操作失败,' + res.msg);
        this.isOkLoading = false;
      }
    });
  }

  hideSetHomeStatusModal(): void {
    this.SetHomeStatusModalVisible = false;
    this.curEditHome = null;
  }

  // #endregion

  // 递归验证所有表单字段
  private validateAllFormFields(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(field => {
      const control = formGroup.get(field);
      if (control instanceof FormArray) {
        control.controls.forEach((c: AbstractControl) => {
          if (c instanceof FormGroup) {
            this.validateAllFormFields(c);
          } else {
            c.markAsDirty();
            c.updateValueAndValidity({ onlySelf: true });
          }
        });
      } else if (control instanceof FormGroup) {
        this.validateAllFormFields(control);
      } else {
        control?.markAsDirty();
        control?.updateValueAndValidity({ onlySelf: true });
      }
    });
  }
}