import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzUploadModule, NzUploadFile } from 'ng-zorro-antd/upload';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

import { ProductDocumentsService, ProductDocument, ProductDocumentStatistics } from './product-documents.service';
import { ProductSeriesService } from '../product-series/product-series.service';
import { TablePageSizeService } from '../../services/table-page-size.service';

@Component({
  selector: 'app-product-documents',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzInputModule,
    NzFormModule,
    NzModalModule,
    NzPaginationModule,
    NzPopconfirmModule,
    NzIconModule,
    NzCardModule,
    NzSpaceModule,
    NzDividerModule,
    NzUploadModule,
    NzSelectModule,
    NzStatisticModule,
    NzGridModule,
    NzTagModule,
    NzToolTipModule,
  ],
  templateUrl: './product-documents.component.html',
  styleUrls: ['./product-documents.component.scss']
})
export class ProductDocumentsComponent implements OnInit {
  productDocumentsList: ProductDocument[] = [];
  loading = false;
  total = 0;
  pageIndex = 1;
  pageSize = 10;
  searchName = '';
  searchSeriesId = '';

  // 统计信息
  statistics: ProductDocumentStatistics | null = null;

  // 系列列表
  seriesList: { _id: string; name: string }[] = [];

  // 上传模态框
  isUploadModalVisible = false;
  uploadForm: FormGroup;
  documentFileList: NzUploadFile[] = [];
  modelFileList: NzUploadFile[] = [];
  uploading = false;

  // 编辑模态框
  isEditModalVisible = false;
  editForm: FormGroup;
  editingProductId: string | null = null;

  constructor(
    private productDocumentsService: ProductDocumentsService,
    private productSeriesService: ProductSeriesService,
    private message: NzMessageService,
    private fb: FormBuilder,
    private tablePageSizeService: TablePageSizeService
  ) {
    this.uploadForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      seriesId: ['', [Validators.required]],
      description: ['', [Validators.maxLength(500)]]
    });

    this.editForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      seriesId: ['', [Validators.required]],
      description: ['', [Validators.maxLength(500)]]
    });
  }

  ngOnInit(): void {
    this.pageSize = this.tablePageSizeService.getPageSize();
    this.loadProductDocumentsList();
    this.loadStatistics();
    this.loadSeriesList();
  }

  loadProductDocumentsList(): void {
    this.loading = true;
    this.productDocumentsService.getProductDocumentsList(
      this.pageIndex, 
      this.pageSize, 
      this.searchName, 
      this.searchSeriesId
    ).subscribe({
      next: (response) => {
        if (response.code === 0) {
          this.productDocumentsList = response.data.list;
          this.total = response.data.total;
        } else {
          this.message.error(response.msg || '获取产品文档列表失败');
        }
        this.loading = false;
      },
      error: (error) => {
        this.message.error('获取产品文档列表失败');
        this.loading = false;
      }
    });
  }

  loadStatistics(): void {
    this.productDocumentsService.getStatistics().subscribe({
      next: (response) => {
        if (response.code === 0) {
          this.statistics = response.data;
        }
      },
      error: (error) => {
        console.error('获取统计信息失败', error);
      }
    });
  }

  loadSeriesList(): void {
    this.productSeriesService.getSimpleSeriesList().subscribe({
      next: (response) => {
        if (response.code === 0) {
          this.seriesList = response.data;
        }
      },
      error: (error) => {
        console.error('获取系列列表失败', error);
      }
    });
  }

  onSearch(): void {
    this.pageIndex = 1;
    this.loadProductDocumentsList();
  }

  onPageIndexChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadProductDocumentsList();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.tablePageSizeService.setPageSize(pageSize);
    this.pageIndex = 1;
    this.loadProductDocumentsList();
  }

  showUploadModal(): void {
    this.uploadForm.reset();
    this.documentFileList = [];
    this.modelFileList = [];
    this.isUploadModalVisible = true;
  }

  showEditModal(product: ProductDocument): void {
    this.editingProductId = product._id;
    this.editForm.patchValue({
      name: product.name,
      seriesId: product.seriesId,
      description: product.description || ''
    });
    this.isEditModalVisible = true;
  }

  // 文档文件上传前的处理
  beforeDocumentUpload = (file: NzUploadFile): boolean => {
    this.documentFileList = [file];
    return false; // 阻止自动上传
  };

  // 模型文件上传前的处理
  beforeModelUpload = (file: NzUploadFile): boolean => {
    this.modelFileList = [file];
    return false; // 阻止自动上传
  };

  // 移除文档文件
  removeDocumentFile = (file: NzUploadFile): boolean => {
    this.documentFileList = [];
    return true;
  };

  // 移除模型文件
  removeModelFile = (file: NzUploadFile): boolean => {
    this.modelFileList = [];
    return true;
  };

  handleUploadOk(): void {
    if (this.uploadForm.valid && this.documentFileList.length > 0 && this.modelFileList.length > 0) {
      this.uploading = true;
      
      const formData = new FormData();
      formData.append('name', this.uploadForm.value.name);
      formData.append('seriesId', this.uploadForm.value.seriesId);
      if (this.uploadForm.value.description) {
        formData.append('description', this.uploadForm.value.description);
      }
      formData.append('documentFile', this.documentFileList[0] as any);
      formData.append('modelFile', this.modelFileList[0] as any);

      this.productDocumentsService.uploadProductDocument(formData).subscribe({
        next: (response) => {
          if (response.code === 0) {
            this.message.success('上传成功');
            this.isUploadModalVisible = false;
            this.loadProductDocumentsList();
            this.loadStatistics();
          } else {
            this.message.error(response.msg || '上传失败');
          }
          this.uploading = false;
        },
        error: (error) => {
          this.message.error('上传失败');
          this.uploading = false;
        }
      });
    } else {
      if (!this.documentFileList.length) {
        this.message.error('请选择文档文件');
      } else if (!this.modelFileList.length) {
        this.message.error('请选择模型文件');
      } else {
        Object.values(this.uploadForm.controls).forEach(control => {
          if (control.invalid) {
            control.markAsDirty();
            control.updateValueAndValidity({ onlySelf: true });
          }
        });
      }
    }
  }

  handleUploadCancel(): void {
    this.isUploadModalVisible = false;
  }

  handleEditOk(): void {
    if (this.editForm.valid && this.editingProductId) {
      this.productDocumentsService.updateProductDocument(this.editingProductId, this.editForm.value)
        .subscribe({
          next: (response) => {
            if (response.code === 0) {
              this.message.success('更新成功');
              this.isEditModalVisible = false;
              this.loadProductDocumentsList();
            } else {
              this.message.error(response.msg || '更新失败');
            }
          },
          error: (error) => {
            this.message.error('更新失败');
          }
        });
    } else {
      Object.values(this.editForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleEditCancel(): void {
    this.isEditModalVisible = false;
  }

  deleteProductDocument(id: string): void {
    this.productDocumentsService.deleteProductDocument(id).subscribe({
      next: (response) => {
        if (response.code === 0) {
          this.message.success('删除成功');
          this.loadProductDocumentsList();
          this.loadStatistics();
        } else {
          this.message.error(response.msg || '删除失败');
        }
      },
      error: (error) => {
        this.message.error('删除失败');
      }
    });
  }

  downloadFile(product: ProductDocument, fileType: 'document' | 'model'): void {
    const url = this.productDocumentsService.downloadFile(product._id, fileType);
    window.open(url, '_blank');
  }

  formatDate(timestamp: number): string {
    return new Date(timestamp).toLocaleString('zh-CN');
  }

  formatFileSize(bytes: number): string {
    return this.productDocumentsService.formatFileSize(bytes);
  }

  getFileTypeLabel(filename: string): string {
    return this.productDocumentsService.getFileTypeLabel(filename);
  }

  getSeriesName(seriesId: string): string {
    const series = this.seriesList.find(s => s._id === seriesId);
    return series ? series.name : '未知系列';
  }
}
