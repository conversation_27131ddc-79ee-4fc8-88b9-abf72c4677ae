:host {
  display: block;
  padding: 24px;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.table-container {
  // 添加响应式容器，支持水平滚动
  // overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: 16px; // 减少底部边距，因为分页组件现在是独立的

  // 确保在小屏幕上也能正确显示表格
  ::ng-deep .ant-table-wrapper {
    // min-width: 1200px;
  }
}

// 独立分页组件的样式
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 0;
  background: #fff;

  // 确保分页组件在小屏幕上不被遮挡
  ::ng-deep .ant-pagination {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;

    // 在小屏幕上调整布局
    @media (max-width: 768px) {
      justify-content: center;

      .ant-pagination-options {
        margin-left: 0;
        margin-top: 8px;
      }
    }
  }

  // 响应式调整
  @media (max-width: 576px) {
    padding: 12px 0;

    ::ng-deep .ant-pagination {
      font-size: 12px;

      .ant-pagination-item,
      .ant-pagination-prev,
      .ant-pagination-next {
        min-width: 28px;
        height: 28px;
        line-height: 26px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  // 小屏幕优化表格容器
  .table-container {
    margin-left: -16px;
    margin-right: -16px;

    ::ng-deep .ant-table-wrapper {
      min-width: 100%;
    }
  }
}

// 超小屏幕适配
@media (max-width: 576px) {
  .table-container {
    margin-left: -12px;
    margin-right: -12px;
    font-size: 12px;

    ::ng-deep {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
      }

      .ant-table-thead > tr > th {
        font-size: 12px;
      }

      .ant-btn {
        font-size: 12px;
        padding: 0 8px;
        height: 28px;

        .anticon {
          font-size: 12px;
        }
      }

      .ant-tag {
        font-size: 10px;
        padding: 0 4px;
        line-height: 1.4;
      }
    }
  }
}

.ant-table {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }
  
  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}

.ant-pagination {
  .ant-pagination-total-text {
    color: #666;
  }
}

.ant-modal {
  .ant-form-item-label > label {
    font-weight: 500;
  }
  
  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    text-align: center;
    padding: 20px;
    transition: border-color 0.3s;
    
    &:hover {
      border-color: #1890ff;
    }
  }
  
  .ant-upload-drag-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }
  
  .ant-upload-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
  }
  
  .ant-upload-hint {
    font-size: 14px;
    color: #999;
  }
}

.ant-statistic {
  .ant-statistic-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }
  
  .ant-statistic-content {
    font-size: 24px;
    font-weight: 600;
  }
}
