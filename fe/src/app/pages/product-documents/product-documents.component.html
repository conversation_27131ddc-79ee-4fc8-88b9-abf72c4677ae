<!-- 统计信息卡片 -->
<div nz-row [nzGutter]="16" style="margin-bottom: 24px;" *ngIf="statistics">
  <div nz-col [nzSpan]="8">
    <nz-card>
      <nz-statistic 
        nzTitle="产品总数" 
        [nzValue]="statistics.totalProducts"
        [nzValueStyle]="{ color: '#1890ff' }">
      </nz-statistic>
    </nz-card>
  </div>
  <!-- <div nz-col [nzSpan]="8">
    <nz-card>
      <nz-statistic 
        nzTitle="总下载次数" 
        [nzValue]="statistics.totalDownloads"
        [nzValueStyle]="{ color: '#52c41a' }">
      </nz-statistic>
    </nz-card>
  </div> -->
  <div nz-col [nzSpan]="8">
    <nz-card>
      <nz-statistic 
        nzTitle="系列数量" 
        [nzValue]="statistics.seriesStats.length"
        [nzValueStyle]="{ color: '#722ed1' }">
      </nz-statistic>
    </nz-card>
  </div>
</div>

<nz-card nzTitle="产品文档管理">
  <!-- 搜索和操作区域 -->
  <div nz-row [nzGutter]="16" style="margin-bottom: 16px;">
    <div nz-col [nzSpan]="6">
      <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
        <input type="text" nz-input placeholder="搜索产品名称" [(ngModel)]="searchName" (keyup.enter)="onSearch()" />
      </nz-input-group>
      <ng-template #suffixIconButton>
        <button nz-button nzType="primary" nzSearch (click)="onSearch()">
          <span nz-icon nzType="search"></span>
        </button>
      </ng-template>
    </div>
    <div nz-col [nzSpan]="6">
      <nz-select 
        nzPlaceHolder="选择系列筛选" 
        [(ngModel)]="searchSeriesId" 
        (ngModelChange)="onSearch()"
        nzAllowClear
        style="width: 100%;">
        <nz-option *ngFor="let series of seriesList" [nzValue]="series._id" [nzLabel]="series.name"></nz-option>
      </nz-select>
    </div>
    <div nz-col [nzSpan]="12" style="text-align: right;">
      <button nz-button nzType="primary" (click)="showUploadModal()">
        <span nz-icon nzType="upload"></span>
        上传产品文档
      </button>
    </div>
  </div>

  <!-- 产品文档列表表格 -->
  <div class="table-container">
    <nz-table
      #basicTable
      [nzData]="productDocumentsList"
      [nzLoading]="loading"
      [nzFrontPagination]="false"
      [nzShowPagination]="false"
      nzSize="middle"
      [nzScroll]="{ x: '1200px' }">
      <thead>
        <tr>
          <th nzWidth="60px">序号</th>
          <th>产品名称</th>
          <th>所属系列</th>
          <th>文档文件</th>
          <th>模型文件</th>
          <!-- <th nzWidth="100px">下载次数</th> -->
          <th nzWidth="180px">创建时间</th>
          <th nzWidth="300px" nzRight>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let product of productDocumentsList; let i = index">
          <td>{{ (pageIndex - 1) * pageSize + i + 1 }}</td>
          <td>
            <div>{{ product.name }}</div>
            <div *ngIf="product.description" style="color: #666; font-size: 12px; margin-top: 4px;">
              {{ product.description }}
            </div>
          </td>
          <td>
            <nz-tag nzColor="blue">{{ product.seriesName }}</nz-tag>
          </td>
          <td>
            <div style="font-size: 12px;">
              <div>{{ product.documentFile.originalName }}</div>
              <div style="color: #666;">
                {{ getFileTypeLabel(product.documentFile.originalName) }} | 
                {{ formatFileSize(product.documentFile.size) }}
              </div>
            </div>
          </td>
          <td>
            <div style="font-size: 12px;">
              <div>{{ product.modelFile.originalName }}</div>
              <div style="color: #666;">
                {{ getFileTypeLabel(product.modelFile.originalName) }} | 
                {{ formatFileSize(product.modelFile.size) }}
              </div>
            </div>
          </td>
          <!-- <td>{{ product.downloadCount }}</td> -->
          <td>{{ formatDate(product.created_at) }}</td>
          <td nzRight>
            <nz-space>
              <button 
                *nzSpaceItem 
                nz-button 
                nzType="link" 
                nzSize="small"
                nz-tooltip="下载文档文件"
                (click)="downloadFile(product, 'document')">
                <span nz-icon nzType="download"></span>
                文档
              </button>
              <button 
                *nzSpaceItem 
                nz-button 
                nzType="link" 
                nzSize="small"
                nz-tooltip="下载模型文件"
                (click)="downloadFile(product, 'model')">
                <span nz-icon nzType="download"></span>
                模型
              </button>
              <button *nzSpaceItem nz-button nzType="link" nzSize="small" (click)="showEditModal(product)">
                <span nz-icon nzType="edit"></span>
                编辑
              </button>
              <button 
                *nzSpaceItem 
                nz-button 
                nzType="link" 
                nzDanger 
                nzSize="small"
                nz-popconfirm
                nzPopconfirmTitle="确定要删除这个产品文档吗？"
                nzPopconfirmPlacement="topRight"
                (nzOnConfirm)="deleteProductDocument(product._id)">
                <span nz-icon nzType="delete"></span>
                删除
              </button>
            </nz-space>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>

  <!-- 独立的分页组件，避免被表格滚动遮挡 -->
  <div class="pagination-container">
    <nz-pagination
      nzShowSizeChanger
      nzShowQuickJumper
      nzPaginationType="small"
      [nzPageSizeOptions]="[10,20,30,40,50]"
      [nzPageIndex]="pageIndex"
      [nzPageSize]="pageSize"
      [nzTotal]="total"
      (nzPageIndexChange)="onPageIndexChange($event)"
      (nzPageSizeChange)="onPageSizeChange($event)">
    </nz-pagination>
  </div>
</nz-card>

<!-- 上传产品文档模态框 -->
<nz-modal
  [(nzVisible)]="isUploadModalVisible"
  nzTitle="上传产品文档"
  nzOkText="上传"
  nzCancelText="取消"
  [nzOkLoading]="uploading"
  [nzWidth]="600"
  (nzOnOk)="handleUploadOk()"
  (nzOnCancel)="handleUploadCancel()">

  <ng-container *nzModalContent>
    <form nz-form [formGroup]="uploadForm" nzLayout="vertical">
      <nz-form-item>
        <nz-form-label nzRequired>产品名称</nz-form-label>
        <nz-form-control nzErrorTip="请输入产品名称（最多100个字符）">
          <input nz-input formControlName="name" placeholder="请输入产品名称" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzRequired>所属系列</nz-form-label>
        <nz-form-control nzErrorTip="请选择产品系列">
          <nz-select formControlName="seriesId" nzPlaceHolder="请选择产品系列">
            <nz-option *ngFor="let series of seriesList" [nzValue]="series._id" [nzLabel]="series.name"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label>产品描述</nz-form-label>
        <nz-form-control nzErrorTip="描述最多500个字符">
          <textarea
            nz-input
            formControlName="description"
            placeholder="请输入产品描述（可选）"
            [nzAutosize]="{ minRows: 2, maxRows: 4 }">
          </textarea>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzRequired>文档文件</nz-form-label>
        <nz-form-control nzErrorTip="请选择文档文件">
          <nz-upload
            nzType="drag"
            [nzMultiple]="false"
            [nzFileList]="documentFileList"
            [nzBeforeUpload]="beforeDocumentUpload"
            [nzRemove]="removeDocumentFile"
            nzAccept=".pdf,.doc,.docx,.xls,.xlsx,.txt">
            <p class="ant-upload-drag-icon">
              <span nz-icon nzType="inbox"></span>
            </p>
            <p class="ant-upload-text">点击或拖拽文档文件到此区域上传</p>
            <p class="ant-upload-hint">支持 PDF、Word、Excel、文本文件</p>
          </nz-upload>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzRequired>模型文件</nz-form-label>
        <nz-form-control nzErrorTip="请选择模型文件">
          <nz-upload
            nzType="drag"
            [nzMultiple]="false"
            [nzFileList]="modelFileList"
            [nzBeforeUpload]="beforeModelUpload"
            [nzRemove]="removeModelFile"
            nzAccept=".dwg,.step,.stp,.iges,.igs,.obj,.stl,.3mf,.zip,.rar">
            <p class="ant-upload-drag-icon">
              <span nz-icon nzType="inbox"></span>
            </p>
            <p class="ant-upload-text">点击或拖拽模型文件到此区域上传</p>
            <p class="ant-upload-hint">支持 CAD图纸、3D模型、压缩文件等</p>
          </nz-upload>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-container>
</nz-modal>

<!-- 编辑产品文档模态框 -->
<nz-modal
  [(nzVisible)]="isEditModalVisible"
  nzTitle="编辑产品文档"
  nzOkText="更新"
  nzCancelText="取消"
  (nzOnOk)="handleEditOk()"
  (nzOnCancel)="handleEditCancel()">

  <ng-container *nzModalContent>
    <form nz-form [formGroup]="editForm" nzLayout="vertical">
      <nz-form-item>
        <nz-form-label nzRequired>产品名称</nz-form-label>
        <nz-form-control nzErrorTip="请输入产品名称（最多100个字符）">
          <input nz-input formControlName="name" placeholder="请输入产品名称" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzRequired>所属系列</nz-form-label>
        <nz-form-control nzErrorTip="请选择产品系列">
          <nz-select formControlName="seriesId" nzPlaceHolder="请选择产品系列">
            <nz-option *ngFor="let series of seriesList" [nzValue]="series._id" [nzLabel]="series.name"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label>产品描述</nz-form-label>
        <nz-form-control nzErrorTip="描述最多500个字符">
          <textarea
            nz-input
            formControlName="description"
            placeholder="请输入产品描述（可选）"
            [nzAutosize]="{ minRows: 2, maxRows: 4 }">
          </textarea>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-container>
</nz-modal>