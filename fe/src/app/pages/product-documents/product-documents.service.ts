import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface FileInfo {
  name: string;
  originalName: string;
  url: string;
  size: number;
}

export interface ProductDocument {
  _id: string;
  name: string;
  seriesId: string;
  seriesName: string;
  documentFile: FileInfo;
  modelFile: FileInfo;
  downloadCount: number;
  description?: string;
  created_at: number;
  updated_at: number;
}

export interface ProductDocumentListResponse {
  code: number;
  data: {
    list: ProductDocument[];
    total: number;
    ps: number;
    pn: number;
  };
  msg: string;
}

export interface ProductDocumentResponse {
  code: number;
  data: ProductDocument;
  msg: string;
}

export interface ProductDocumentStatistics {
  totalProducts: number;
  totalDownloads: number;
  seriesStats: Array<{
    _id: string;
    seriesName: string;
    count: number;
    downloads: number;
  }>;
}

export interface ProductDocumentStatisticsResponse {
  code: number;
  data: ProductDocumentStatistics;
  msg: string;
}

export interface CreateProductDocumentDto {
  name: string;
  seriesId: string;
  description?: string;
}

export interface UpdateProductDocumentDto {
  name?: string;
  seriesId?: string;
  description?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ProductDocumentsService {
  private apiUrl = '/product-documents';

  constructor(private http: HttpClient) {}

  // 获取产品文档列表
  getProductDocumentsList(pn: number = 1, ps: number = 10, name?: string, seriesId?: string): Observable<ProductDocumentListResponse> {
    let params = new HttpParams()
      .set('pn', pn.toString())
      .set('ps', ps.toString());
    
    if (name) {
      params = params.set('name', name);
    }
    if (seriesId) {
      params = params.set('seriesId', seriesId);
    }

    return this.http.get<ProductDocumentListResponse>(`${this.apiUrl}/list`, { params });
  }

  // 获取单个产品文档
  getProductDocument(id: string): Observable<ProductDocumentResponse> {
    return this.http.get<ProductDocumentResponse>(`${this.apiUrl}/${id}`);
  }

  // 上传产品文档（同时上传文档文件和模型文件）
  uploadProductDocument(formData: FormData): Observable<{ code: number; msg: string; data?: any }> {
    return this.http.post<{ code: number; msg: string; data?: any }>(`${this.apiUrl}/upload`, formData);
  }

  // 更新产品文档
  updateProductDocument(id: string, data: UpdateProductDocumentDto): Observable<{ code: number; msg: string }> {
    return this.http.patch<{ code: number; msg: string }>(`${this.apiUrl}/${id}`, data);
  }

  // 删除产品文档
  deleteProductDocument(id: string): Observable<{ code: number; msg: string }> {
    return this.http.delete<{ code: number; msg: string }>(`${this.apiUrl}/${id}`);
  }

  // 下载文件
  downloadFile(id: string, fileType: 'document' | 'model'): string {
    return `${this.apiUrl}/download/${id}/${fileType}`;
  }

  // 获取统计信息
  getStatistics(): Observable<ProductDocumentStatisticsResponse> {
    return this.http.get<ProductDocumentStatisticsResponse>(`${this.apiUrl}/statistics/overview`);
  }

  // 获取公开产品文档列表（无需认证）
  getPublicProductDocumentsList(seriesId?: string): Observable<{ code: number; data: ProductDocument[]; msg: string }> {
    let params = new HttpParams();
    if (seriesId) {
      params = params.set('seriesId', seriesId);
    }
    return this.http.get<{ code: number; data: ProductDocument[]; msg: string }>(`${this.apiUrl}/public/list`, { params });
  }

  // 格式化文件大小
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 获取文件类型标签
  getFileTypeLabel(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const typeMap: { [key: string]: string } = {
      'pdf': 'PDF文档',
      'doc': 'Word文档',
      'docx': 'Word文档',
      'xls': 'Excel表格',
      'xlsx': 'Excel表格',
      'txt': '文本文件',
      'zip': '压缩文件',
      'rar': '压缩文件',
      'dwg': 'CAD图纸',
      'step': '3D模型',
      'stp': '3D模型',
      'iges': '3D模型',
      'igs': '3D模型',
      'obj': '3D模型',
      'stl': '3D模型',
      '3mf': '3D模型'
    };
    return typeMap[ext || ''] || '其他文件';
  }
}
