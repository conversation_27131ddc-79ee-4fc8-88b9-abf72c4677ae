.search-form {
  background: #fafafa;
  padding: 24px;
  border-radius: 6px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    padding: 16px;
  }

  .search-buttons {
    text-align: right;

    @media (max-width: 768px) {
      text-align: center;
      margin-top: 16px;
    }

    button {
      margin-left: 8px;

      @media (max-width: 768px) {
        margin: 4px;
      }
    }
  }
}

.search-result-list {
    margin-top: 16px;
    border: 1px dashed #e9e9e9;
    border-radius: 6px;
    background-color: #fafafa;
    min-height: 200px;
    text-align: center;
    padding: 0 10px;
}



nz-table {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }
}

.table-container {
  overflow-x: auto;

  @media (max-width: 768px) {
    margin: 0 -16px;
    padding: 0 16px;
  }

  nz-table {
    min-width: 1200px; // 确保表格有最小宽度

    .ant-table-tbody > tr > td {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    // 在小屏幕下调整字体大小
    @media (max-width: 768px) {
      font-size: 12px;

      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
      }
    }
  }
}

.customer-form {
  .ant-form-item {
    margin-bottom: 16px;
  }
}
