import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { CustomerService, Customer, CustomerQuery } from './customer.service';

@Component({
  selector: 'app-customer',
  imports: [
    ReactiveFormsModule,
    CommonModule,
    NzButtonModule,
    NzFormModule,
    NzInputModule,
    NzTableModule,
    NzModalModule,
    NzPopconfirmModule,
    NzSelectModule,
    NzDatePickerModule,
    NzDividerModule,
    NzIconModule,
  ],
  templateUrl: './customer.component.html',
  styleUrl: './customer.component.scss'
})
export class CustomerComponent implements OnInit {
  searchForm: FormGroup;
  customerForm: FormGroup;
  customers: Customer[] = [];
  loading = false;
  total = 0;
  pageIndex = 1;
  pageSize = 10;
  isModalVisible = false;
  isEdit = false;
  currentCustomer: Customer | null = null;



  // 排序选项
  sortOptions = [
    { label: '创建时间', value: 'created_at' },
    { label: '更新时间', value: 'updated_at' },
    { label: '客户姓名', value: 'name' },
    { label: '手机号', value: 'phone' }
  ];

  constructor(
    private fb: FormBuilder,
    private customerService: CustomerService,
    private message: NzMessageService
  ) {
    this.searchForm = this.fb.group({
      name: [''],
      company: [''],
      phone: [''],
      email: [''],
      province: [''],
      products: [''],
      downloadedFile: [''],
      sortBy: ['created_at'],
      sortOrder: ['desc'],
      dateRange: [null]
    });

    this.customerForm = this.fb.group({
      _id: [''],
      name: ['', [Validators.required]],
      company: ['', [Validators.required]],
      phone: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      province: ['', [Validators.required]],
      products: ['', [Validators.required]],
      remark: [''],
      downloadedFile: ['']
    });
  }

  ngOnInit(): void {
    this.loadCustomers();
  }

  loadCustomers(): void {
    this.loading = true;
    const searchValues = this.searchForm.value;
    const filters: CustomerQuery = {
      name: searchValues.name,
      company: searchValues.company,
      phone: searchValues.phone,
      email: searchValues.email,
      province: searchValues.province,
      products: searchValues.products,
      downloadedFile: searchValues.downloadedFile,
      sortBy: searchValues.sortBy,
      sortOrder: searchValues.sortOrder
    };

    // 处理日期范围
    if (searchValues.dateRange && searchValues.dateRange.length === 2) {
      filters.startDate = searchValues.dateRange[0].toISOString().split('T')[0];
      filters.endDate = searchValues.dateRange[1].toISOString().split('T')[0];
    }

    this.customerService.getList(this.pageIndex, this.pageSize, filters).subscribe({
      next: (response) => {
        if (response.code === 0) {
          this.customers = response.data.list;
          this.total = response.data.total;
        } else {
          this.message.error('加载客户列表失败');
        }
        this.loading = false;
      },
      error: () => {
        this.message.error('加载客户列表失败');
        this.loading = false;
      }
    });
  }

  search(): void {
    this.pageIndex = 1;
    this.loadCustomers();
  }

  resetSearch(): void {
    this.searchForm.reset({
      name: '',
      company: '',
      phone: '',
      email: '',
      province: '',
      products: '',
      downloadedFile: '',
      sortBy: 'created_at',
      sortOrder: 'desc',
      dateRange: null
    });
    this.pageIndex = 1;
    this.loadCustomers();
  }

  onPageIndexChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadCustomers();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageIndex = 1;
    this.loadCustomers();
  }

  showAddModal(): void {
    this.isEdit = false;
    this.currentCustomer = null;
    this.customerForm.reset({
      _id: '',
      name: '',
      company: '',
      phone: '',
      email: '',
      province: '',
      products: '',
      remark: '',
      downloadedFile: ''
    });
    this.isModalVisible = true;
  }

  showEditModal(customer: Customer): void {
    this.isEdit = true;
    this.currentCustomer = customer;
    this.customerForm.patchValue(customer);
    this.isModalVisible = true;
  }

  handleModalOk(): void {
    if (this.customerForm.valid) {
      const customerData = this.customerForm.value;
      
      if (this.isEdit) {
        this.customerService.update(customerData).subscribe({
          next: (response) => {
            if (response.code === 0) {
              this.message.success('更新客户成功');
              this.isModalVisible = false;
              this.loadCustomers();
            } else {
              this.message.error(response.msg || '更新客户失败');
            }
          },
          error: () => {
            this.message.error('更新客户失败');
          }
        });
      } else {
        this.customerService.create(customerData).subscribe({
          next: (response) => {
            if (response.code === 0) {
              this.message.success('创建客户成功');
              this.isModalVisible = false;
              this.loadCustomers();
            } else {
              this.message.error(response.msg || '创建客户失败');
            }
          },
          error: () => {
            this.message.error('创建客户失败');
          }
        });
      }
    } else {
      Object.values(this.customerForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleModalCancel(): void {
    this.isModalVisible = false;
  }

  deleteCustomer(id: string): void {
    this.customerService.delete(id).subscribe({
      next: (response) => {
        if (response.code === 0) {
          this.message.success('删除客户成功');
          this.loadCustomers();
        } else {
          this.message.error(response.msg || '删除客户失败');
        }
      },
      error: () => {
        this.message.error('删除客户失败');
      }
    });
  }

  exportExcel(): void {
    const searchValues = this.searchForm.value;
    const filters: CustomerQuery = {
      name: searchValues.name,
      company: searchValues.company,
      phone: searchValues.phone,
      email: searchValues.email,
      province: searchValues.province,
      products: searchValues.products,
      downloadedFile: searchValues.downloadedFile,
      sortBy: searchValues.sortBy,
      sortOrder: searchValues.sortOrder
    };

    if (searchValues.dateRange && searchValues.dateRange.length === 2) {
      filters.startDate = searchValues.dateRange[0].toISOString().split('T')[0];
      filters.endDate = searchValues.dateRange[1].toISOString().split('T')[0];
    }

    this.customerService.exportExcel(filters).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `customers_${new Date().getTime()}.xlsx`;
        a.click();
        window.URL.revokeObjectURL(url);
        this.message.success('导出Excel成功');
      },
      error: () => {
        this.message.error('导出Excel失败');
      }
    });
  }

  exportCsv(): void {
    const searchValues = this.searchForm.value;
    const filters: CustomerQuery = {
      name: searchValues.name,
      company: searchValues.company,
      phone: searchValues.phone,
      email: searchValues.email,
      province: searchValues.province,
      products: searchValues.products,
      downloadedFile: searchValues.downloadedFile,
      sortBy: searchValues.sortBy,
      sortOrder: searchValues.sortOrder
    };

    if (searchValues.dateRange && searchValues.dateRange.length === 2) {
      filters.startDate = searchValues.dateRange[0].toISOString().split('T')[0];
      filters.endDate = searchValues.dateRange[1].toISOString().split('T')[0];
    }

    this.customerService.exportCsv(filters).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `customers_${new Date().getTime()}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
        this.message.success('导出CSV成功');
      },
      error: () => {
        this.message.error('导出CSV失败');
      }
    });
  }



  formatDate(timestamp: number): string {
    return new Date(timestamp).toLocaleString('zh-CN');
  }
}
