import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_URL } from '../../const';
import { Observable } from 'rxjs';

export interface Customer {
  _id?: string;
  name: string;
  company: string;
  phone: string;
  email: string;
  province: string;
  products: string;
  remark?: string;
  downloadedFile?: string;
  created_at: number;
  updated_at: number;
}

export interface CustomerQuery {
  pn?: number;
  ps?: number;
  name?: string;
  company?: string;
  phone?: string;
  email?: string;
  province?: string;
  products?: string;
  downloadedFile?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  startDate?: string;
  endDate?: string;
}

@Injectable({
  providedIn: 'root'
})
export class CustomerService {

  constructor(private http: HttpClient) { }

  // 获取客户列表
  getList(pageIndex: number, pageSize: number, filters: CustomerQuery): Observable<any> {
    let params = new HttpParams()
      .append('pn', <string><unknown>pageIndex)
      .append('ps', <string><unknown>pageSize);

    Object.keys(filters).forEach(key => {
      if (filters[key as keyof CustomerQuery] !== undefined && filters[key as keyof CustomerQuery] !== '') {
        params = params.append(key, filters[key as keyof CustomerQuery] as string);
      }
    });
    return this.http.get(`${API_URL}/customer/list`, { params });
  }

  // 创建客户
  create(customerData: Customer): Observable<any> {
    return this.http.post(`${API_URL}/customer/create`, customerData);
  }

  // 更新客户
  update(customerData: Customer): Observable<any> {
    return this.http.patch(`${API_URL}/customer/update`, customerData);
  }

  // 删除客户
  delete(id: string): Observable<any> {
    return this.http.delete(`${API_URL}/customer/${id}`);
  }

  // 获取单个客户
  getOne(id: string): Observable<any> {
    return this.http.get(`${API_URL}/customer/${id}`);
  }

  // 导出Excel
  exportExcel(filters: CustomerQuery): Observable<Blob> {
    let params = new HttpParams();
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof CustomerQuery] !== undefined && filters[key as keyof CustomerQuery] !== '') {
        params = params.append(key, filters[key as keyof CustomerQuery] as string);
      }
    });
    return this.http.get(`${API_URL}/customer/export/excel`, { 
      params, 
      responseType: 'blob' 
    });
  }

  // 导出CSV
  exportCsv(filters: CustomerQuery): Observable<Blob> {
    let params = new HttpParams();
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof CustomerQuery] !== undefined && filters[key as keyof CustomerQuery] !== '') {
        params = params.append(key, filters[key as keyof CustomerQuery] as string);
      }
    });
    return this.http.get(`${API_URL}/customer/export/csv`, { 
      params, 
      responseType: 'blob' 
    });
  }
}
