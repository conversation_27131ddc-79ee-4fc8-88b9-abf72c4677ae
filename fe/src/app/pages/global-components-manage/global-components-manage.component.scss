.ant-advanced-search-form {
    padding: 24px;
    background: #fbfbfb;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
}

button {
    margin-left: 8px;
}

.search-result-list {
    margin-top: 16px;
    border: 1px dashed #e9e9e9;
    border-radius: 6px;
    background-color: #fafafa;
    min-height: 200px;
    text-align: center;
    padding: 0 10px;
}

.page-model-edit-drawer {
    display: flex;
    height: 100%;

    .component-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
        width: 30%;
        height: 100%;
        border-right: solid #999 1px;
        overflow-y: auto;
        padding-right: 24px;

        .component-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
            border: solid #999 1px;
            border-radius: 12px;
            cursor: pointer;

            .item-name {
                font-size: 20px;
                line-height: 24px;
                padding: 4px 8px;
                word-wrap: break-word;
            }

            .item-img {
                width: 100%;
            }

            .item-remark {
                font-size: 14px;
                color: #999;
                margin-top: auto;
                padding: 4px 8px;
            }
        }
    }

    .page-model {
        width: 100%;
        padding: 0 0 0 24px;
        overflow: auto;

        .page-component-list {
            display: flex;
            flex-direction: column;
            gap: 40px;

            .page-component-item {
                display: flex;
                position: relative;
                min-height: 100px;
                cursor: move;

                &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    transition: all 0.3s ease-in-out;
                }

                &:hover::before {
                    background: rgba(0, 0, 0, 0.3);

                }

                .component-img {
                    width: 100%;
                }

                .component-remove {
                    display: none;
                    position: absolute;
                    top: 24px;
                    right: 24px;
                    cursor: pointer;
                }

                &:hover .component-remove {
                    display: block;
                }
                
            }

            
        }
    }
}
.cdk-drag-preview {
    opacity: 0.6;
    border: none;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
        0 8px 10px 1px rgba(0, 0, 0, 0.14),
        0 3px 14px 2px rgba(0, 0, 0, 0.12);

    .component-remove{
        display: none;
    };

    .component-img {
        width: 100%;
    }
}

.cdk-drag-placeholder {
    opacity: 0;
}

.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .page-component-item:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

// 拖拽排序相关样式
.element-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .element-name {
        margin: 0;
    }
}

.sort-mode-container {
    .sort-actions {
        margin-bottom: 16px;
        padding: 12px;
        background: #f5f5f5;
        border-radius: 6px;
        text-align: center;
    }

    .sortable-tree {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        padding: 16px;
        background: #fafafa;
    }
}

.sortable-level {
    min-height: 40px;

    &.cdk-drop-list-dragging {
        .sortable-node:not(.cdk-drag-placeholder) {
            transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
        }
    }
}

.sortable-node {
    margin-bottom: 8px;
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
    }

    &.cdk-drag-preview {
        opacity: 0.8;
        transform: rotate(2deg);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        border-color: #1890ff;
    }

    &.cdk-drag-placeholder {
        opacity: 0.5;
        background: #f0f0f0;
        border: 2px dashed #d9d9d9;

        .node-content {
            visibility: hidden;
        }
    }

    &.cdk-drag-animating {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }

    .node-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;

        .node-info {
            display: flex;
            align-items: center;
            flex: 1;

            .node-icon {
                margin-right: 8px;
                color: #666;
            }

            .node-name {
                font-weight: 500;
                color: #333;
                margin-right: 8px;
            }

            .node-key {
                color: #666;
                font-size: 12px;
                margin-right: 8px;
            }

            .node-type {
                color: #999;
                font-size: 11px;
                background: #f0f0f0;
                padding: 2px 6px;
                border-radius: 3px;
            }
        }

        .drag-handle {
            cursor: move;
            color: #999;
            padding: 4px;
            border-radius: 3px;
            transition: all 0.3s ease;

            &:hover {
                color: #1890ff;
                background: #f0f8ff;
            }
        }
    }

    .child-nodes {
        border-top: 1px solid #f0f0f0;
        padding-top: 8px;
        margin-top: 8px;
        background: #fafafa;
    }
}

.modal-content {

    .form-group-container {
        border: 1px dashed #9E9E9E;
        margin-bottom: 10px;
        // padding-bottom: 10px;
        position: relative;
    }

    .form-item-title {
        font-size: 16px;
        text-align: center;
        margin-bottom: 10px;
    }

    .delete-element-list {
        position: absolute;
        top: 5px;
        right: 5px;
    }

    .form-item {
        margin-bottom: 10px;
        position: relative;

        input {
            margin-bottom: 8px;
        }

        textarea {
            margin-bottom: 8px;
            font-family: inherit;
            line-height: 1.5;
        }

        nz-select {
            margin-bottom: 8px;
        }

        .delete-form-item-btn {
            position: absolute;
            right: 10px;
        }

        .param-input {
            width: 50px;
        }

        &.count-type {
            margin-top: 10px;
        }

    }

    .add-form-item-btn {
        display: block;
        margin: 0 auto;
    }
}

.object-children {
    margin-left: 20px;
    padding-left: 10px;
    border-left: 2px solid #e8e8e8;

    .element-data-item {
        margin-bottom: 6px;

        .element-data-name {
            cursor: pointer;
            color: #1890ff;

            &:hover {
                text-decoration: underline;
            }
        }

        .element-data-value {
            margin-left: 8px;
            color: #666;
            
            &.empty {
                color: #ff4d4f; // 红色，表示未填写
                font-style: italic;
                background-color: #fff2f0; // 浅红色背景
                border: 1px dashed #ffccc7; // 虚线边框
                padding: 2px 6px;
                border-radius: 4px;
            }
            
            &.filled {
                color: #52c41a; // 绿色，表示已填写
            }
        }
    }
}

// 多层list的层级显示样式
.list-container {
    position: relative;

    .list-level-indicator {
        margin-bottom: 8px;

        .level-badge {
            display: inline-block;
            padding: 2px 8px;
            background-color: #f0f0f0;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }
    }

    // 未填写内容提醒标识
    .unfilled-indicator {
      display: inline-block;
      min-width: 18px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      background-color: #ff4d4f;
      color: #ffffff;
      font-size: 12px;
      font-weight: bold;
      border-radius: 9px;
      margin-left: 6px;
      vertical-align: middle;
      box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
      position: relative;
      top: -1px;
    }

    // 为不同层级设置不同的背景色
    // &[style*="margin-left: 20px"] {
    //     background-color: #fafafa;
    // }

    // &[style*="margin-left: 40px"] {
    //     background-color: #f5f5f5;
    // }

    // &[style*="margin-left: 60px"] {
    //     background-color: #f0f0f0;
    // }
    
    .element-data-value {
        &.empty {
            color: #ff4d4f; // 红色，表示未填写
            font-style: italic;
            background-color: #fff2f0; // 浅红色背景
            border: 1px dashed #ffccc7; // 虚线边框
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        &.filled {
            color: #52c41a; // 绿色，表示已填写
        }
    }
    
    // List中元素的层级显示
    .element-data-item {
        margin-bottom: 6px;
        transition: all 0.3s ease;
        
        &:hover {
            background-color: rgba(24, 144, 255, 0.05);
            border-radius: 4px;
        }
        
        .element-data-name {
            cursor: pointer;
            color: #1890ff;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }

        .element-data-value {
            margin-left: 8px;
            color: #666;
            
            &.empty {
                color: #ff4d4f; // 红色，表示未填写
                font-style: italic;
                background-color: #fff2f0; // 浅红色背景
                border: 1px dashed #ffccc7; // 虚线边框
                padding: 2px 6px;
                border-radius: 4px;
            }
            
            &.filled {
                color: #52c41a; // 绿色，表示已填写
            }
        }
    }
}

.element-datas {
    .element-name {
        margin: 16px 24px;
    }

    .element-data-items {
        margin: 0 24px;

        .element-data-item {
            margin-bottom: 8px;
            
            .element-data-name {
                cursor: pointer;
            }
            
            .element-data-value {
                &.empty {
                    color: #ff4d4f; // 红色，表示未填写
                    font-style: italic;
                    background-color: #fff2f0; // 浅红色背景
                    border: 1px dashed #ffccc7; // 虚线边框
                    padding: 2px 6px;
                    border-radius: 4px;
                }
                
                &.filled {
                    color: #52c41a; // 绿色，表示已填写
                }
            }
        }
    }
}
