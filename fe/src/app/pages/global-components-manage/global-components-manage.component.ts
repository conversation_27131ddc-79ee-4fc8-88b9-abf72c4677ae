import { CommonModule } from '@angular/common';
import { Component, NgModule } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { ComponentsLibraryService } from '../components-library/components-library.service';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ClipboardModule } from 'ngx-clipboard';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { ImageUploadFormComponent } from '../../components/image-upload-form/image-upload-form.component';
import { NzImageModule } from 'ng-zorro-antd/image';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzTreeFlatDataSource, NzTreeFlattener, NzTreeViewModule } from 'ng-zorro-antd/tree-view';
import { SelectionModel } from '@angular/cdk/collections';
import { FlatTreeControl } from '@angular/cdk/tree';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';

import { NZ_TAB_SET, NzTabsModule } from 'ng-zorro-antd/tabs';
import { QuillModule } from 'ngx-quill';
import { FileUploadFormComponent } from '../../components/file-upload-form/file-upload-form.component';
import { VideoUploadFormComponent } from '../../components/video-upload-form/video-upload-form.component';
import { Al_home } from '../../const';

interface TreeNode {
  name: string;
  key: string;
  contentType?: string[];
  required?: boolean;
  children?: TreeNode[];
  defaultValue?: string;
  options?: {
    label: string;
    value: string;
  }[];
}

const TREE_DATA: TreeNode[] = [
  {
    name: '组件数据',
    key: 'data',
    contentType: ['object'],
  }
];

interface FlatNode {
  expandable: boolean;
  name: string;
  key: string;
  level: number;
  // contentType?: string[];
}

@Component({
  selector: 'app-global-components-manage',
  imports: [
    ReactiveFormsModule,
    CommonModule,
    NzButtonModule,
    NzFormModule,
    ClipboardModule,
    NzToolTipModule,
    NzInputModule,
    NzIconModule,
    NzModalModule,
    NzTableModule,
    NzDividerModule,
    NzTreeModule,
    NzCardModule,
    NzPopconfirmModule,
    NzSelectModule,
    ImageUploadFormComponent,
    FileUploadFormComponent,
    VideoUploadFormComponent,
    NzImageModule,
    NzTreeViewModule,
    NzDrawerModule,
    FormsModule,
    NzTagModule,
    NzTabsModule,
    QuillModule,
  ],
  providers: [{ provide: NZ_TAB_SET, useValue: {} }],
  templateUrl: './global-components-manage.component.html',
  styleUrl: './global-components-manage.component.scss'
})
export class GlobalComponentsManageComponent {

  // 常量
  RESOURCE_TYPE_OPTIONS = [
    {
      label: '文字',
      value: 'text'
    },
    {
      label: '选择器',
      value: 'select'
    },
    {
      label: '图片',
      value: 'image'
    },
    {
      label: '视频',
      value: 'video'
    },
    {
      label: '富文本',
      value: 'richText'
    },
    {
      label: '文件',
      value: 'file'
    },
    {
      label: '列表',
      value: 'list'
    },
    {
      label: '对象',
      value: 'object'
    },
    {
      label: '调色盘',
      value: 'palette'
    }
  ];

  DEFAULT_VALUE_TYPE_OPTIONS = [
    {
      label: '文字',
      value: 'text'
    },
    {
      label: '图片',
      value: 'image'
    },
    {
      label: '视频',
      value: 'video'
    },
    {
      label: '文件',
      value: 'file'
    },
    {
      label: '调色盘',
      value: 'palette'
    },
  ];

  ELEMENT_TYPE_OPTIONS = [
    {
      label: '单一',
      value: 'single'
    },
    {
      label: '多个',
      value: 'array'
    },
  ];

  REQUIRE_TYPE_OPTIONS = [
    {
      label: '必填',
      value: true
    },
    {
      label: '非必填',
      value: false
    }
  ]




  constructor(
    private fb: FormBuilder,
    private ComponentsLibraryService: ComponentsLibraryService,
    private messageService: NzMessageService
  ) {
    this.searchValidateForm = this.fb.group({});
    this.componentForm = this.fb.group({});
    this.elementDataItemForm = this.fb.group({});
  }

  // 生命周期钩子
  ngOnInit(): void {
    this.searchControlArray.map((control) => {
      this.searchValidateForm.addControl(control.name, this.fb.control(''));
    });
    this.getList(this.pageIndex, this.pageSize, {});
    this.getComponentList();
    this.initComponentForm();
    this.initElementDataItemForm();
  }

  al_home = Al_home;
  // #region 搜索
  listData: any = [];
  componentList: any = [];
  staticComponentList: string[] = [''];
  searchValidateForm: FormGroup;
  searchControlArray = [
    {
      name: 'name',
      labelName: '页面名称'
    }
  ];
  pageIndex: number = 1;
  pageSize: number = 1000;
  total: number = 0;
  listLoading: boolean = false;
  resetForm(): void {
    this.searchValidateForm.reset();
  }

  search(reset = false): void {
    let searchFilter: any = {};
    Object.keys(this.searchValidateForm.controls).forEach(k => {
      searchFilter[k] = this.searchValidateForm.controls[k]['value'];
    });
    if (reset) {
      searchFilter = {};
      this.pageIndex = 1;
    }
    this.getList(this.pageIndex, this.pageSize, searchFilter);
  }

  onPageIndexChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.search();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.search();
  }

  getComponentList(): void {
    this.ComponentsLibraryService.getList(1, 1000, {}).subscribe(res => {
      console.log(res);
      this.componentList = res.data.list;
    });
  }

  getList(pageIndex: number = 1, pageSize: number = 20, search: { [k: string]: string }): void {
    this.listLoading = true;
    this.ComponentsLibraryService.getGlobalComponents(pageIndex, pageSize, search).subscribe(res => {
      this.listData = res.data.list;
      this.staticComponentList = res.data.staticComponentList;
      this.total = res.data.total;
      this.pageIndex = res.data.pn;
      this.pageSize = res.data.ps;
      this.listLoading = false;
    });
  }

  // #endregion

  curEditComponent: any = null;
  creating: boolean = false;
  componentDrawerVisible: boolean = false;
  deleteComponentModalVisible: boolean = false;
  publishConfirmModalVisible: boolean = false;
  isOkLoading: boolean = false;
  componentForm: FormGroup;
  publishTargetId: string = ''; // 待发布的组件ID

  initComponentForm(): void {
    this.componentForm = this.fb.group({
      name: ['', Validators.required],
      label: ['', Validators.required],
      image: ['', Validators.required],
      remark: [''],
      elements: []
    });
  }
  
  selComponent(item: any): void {
    this.curEditComponent = item;
    this.curEditComponent._id = null;
    this.componentForm.patchValue(item);

    // 初始化_activeTabIndex属性
    this.initializeActiveTabIndices(this.curEditComponent.elements.children);
  }

  // 初始化tab索引属性
  initializeActiveTabIndices(list: any[]): void {
    list.forEach(item => {
      if (item.contentType && item.contentType.includes('list')) {
        // 确保每个list类型的元素都有_activeTabIndex属性
        if (item._activeTabIndex === undefined) {
          item._activeTabIndex = 0;
        }
        // 递归处理子元素
        if (item.value && Array.isArray(item.value)) {
          item.value.forEach((tab: any) => {
            if (Array.isArray(tab)) {
              this.initializeActiveTabIndices(tab);
            }
          });
        }
      }
      // 处理对象类型的子元素
      if (item.children) {
        this.initializeActiveTabIndices(item.children);
      }
    });
  }

  editComponent(item: any): void {
    this.creating = false;
    this.curEditComponent = this.copyObject(item);
    this.componentDrawerVisible = true;
    this.initComponentForm();
    // 如果有草稿数据，优先使用草稿数据
    const componentData = item.editingData || item;
    this.componentForm.patchValue(componentData);
    this.curEditComponent.elements = componentData.elements;
  }

  showDeleteComponentModal(component: any): void {
    this.curEditComponent = component;
    this.deleteComponentModalVisible = true;
  }

  hideDeleteComponentModal(): void {
    this.deleteComponentModalVisible = false;
    this.curEditComponent = null;
  }

  showComponentDrawer(): void {
    this.creating = true;
    this.componentDrawerVisible = true;
    this.curEditComponent = null;
    this.initComponentForm();
  }

  closeComponentDrawer(): void {
    this.componentDrawerVisible = false;
  }

  elementDataItemDrawerVisible: boolean = false;
  curElementDataItem: any = {};
  showElementDataItemDrawer(item: any): void {
    this.elementDataItemDrawerVisible = true;
    this.curElementDataItem = item;
    this.initElementDataItemForm();
    if (item.defaultValue) {
      this.elementDataItemForm.controls['value'].setValue(item.defaultValue);
    }
    this.elementDataItemForm.patchValue(item);
    this.elementDataItemForm.controls['contentTypeValue'].setValue(item.contentType[0]);
  }
  hideElementDataItemDrawer(): void {
    this.elementDataItemDrawerVisible = false;
  }

  elementDataItemForm: FormGroup;
  initElementDataItemForm(): void {
    this.elementDataItemForm = this.fb.group({
      value: ['', Validators.required],
      contentType: [['text'], Validators.required],
      options: [[]],
      contentTypeValue: [''],
    });
  }

  closeTab(parent: any, { index }: { index: number }, level: number): void {
    parent.value.splice(index, 1);
    // 如果关闭的tab在当前激活的tab之前或就是当前激活的tab，则需要调整激活的tab索引
    if (parent._activeTabIndex >= index && parent._activeTabIndex > 0) {
      parent._activeTabIndex = parent._activeTabIndex - 1;
    }
    // 如果列表为空，重置激活索引
    if (parent.value.length === 0) {
      parent._activeTabIndex = 0;
    }
  }

  // 保存草稿
  saveDraft(): void {
    if (this.curEditComponent && this.curEditComponent._id) {
      // 更新组件的草稿
      const draftData = {
        ...this.componentForm.value,
        elements: this.curEditComponent.elements
      };

      this.ComponentsLibraryService.saveDraft(this.curEditComponent._id, draftData).subscribe(res => {
        if (res.code === 0) {
          this.messageService.success('草稿保存成功，待发布');
          this.closeComponentDrawer();
          this.search();
        } else {
          this.messageService.error('草稿保存失败: ' + res.msg);
        }
      });
    } else {
      // 创建新组件时不能保存草稿，需要先创建组件
      this.messageService.warning('请先创建组件再保存草稿');
    }
  }

  // 显示发布确认框
  showPublishConfirm(id: string): void {
    this.publishTargetId = id;
    this.publishConfirmModalVisible = true;
  }

  // 隐藏发布确认框
  hidePublishConfirm(): void {
    this.publishConfirmModalVisible = false;
    this.publishTargetId = '';
  }

  // 确认发布草稿
  confirmPublish(): void {
    if (this.publishTargetId) {
      this.isOkLoading = true;
      this.ComponentsLibraryService.publishDraft(this.publishTargetId).subscribe(res => {
        if (res.code === 0) {
          this.messageService.success('发布成功');
          this.hidePublishConfirm();
          this.closeComponentDrawer();
          this.search();
        } else {
          this.messageService.error('发布失败: ' + res.msg);
        }
        this.isOkLoading = false;
      });
    }
  }

  submitComponentInfo(): void {
    console.log(this.componentForm.value);

    if (this.curEditComponent && this.curEditComponent._id) {
      this.componentForm.addControl('_id', this.fb.control(this.curEditComponent._id));
      this.componentForm.controls['elements'].setValue(this.curEditComponent.elements);
      this.ComponentsLibraryService.updateComponentConfig(this.componentForm.value).subscribe(res => {
        this.getList(this.pageIndex, this.pageSize, {});
      });
    } else {
      this.componentForm.addControl('isGlobal', this.fb.control(true));
      this.componentForm.controls['image'].setValue(this.curEditComponent.image);
      this.componentForm.controls['elements'].setValue(this.curEditComponent.elements);
      this.ComponentsLibraryService.createComponentConfig(this.componentForm.value).subscribe(res => {
        this.getList(this.pageIndex, this.pageSize, {});
      });
    }
    this.closeComponentDrawer();
  }

  deleteComponent(): void {
    this.isOkLoading = true;
    this.ComponentsLibraryService.deleteComponentConfig(this.curEditComponent._id).subscribe(res => {
      console.log(res);
      if (res.code === 0) {
        this.messageService.success('删除成功');
        this.hideDeleteComponentModal();
        this.getList(this.pageIndex, this.pageSize, {});
        this.isOkLoading = false;
      } else {
        this.messageService.error('删除失败,' + res.msg);
      }
    });
  }

  submitElementNode(): void {
    // if (this.elementForm.invalid) {
    //   // 递归检查所有子表单组
    //   this.validateAllFormFields(this.elementForm);
    //   this.messageService.error('请检查表单填写是否正确！');
    //   return;
    // }
    let contentType = this.elementDataItemForm.get('contentTypeValue')?.value;
    if (contentType === 'list') {
      this.curElementDataItem.value = this.curElementDataItem.value || [];
      this.curElementDataItem.value.push(this.copyObject(this.curElementDataItem.children));
      // 在添加新tab后，激活最新添加的tab
      setTimeout(() => {
        this.curElementDataItem._activeTabIndex = this.curElementDataItem.value.length - 1;
      }, 0);
      console.log(this.curElementDataItem.value);
    } else if (contentType === 'object') {
      // 对于object类型，确保children存在并且contentType正确设置
      this.curElementDataItem.contentType = [contentType];
      // object类型的value通常是undefined或者是children的引用
      if (!this.curElementDataItem.children) {
        this.curElementDataItem.children = [];
      }
    } else {
      this.curElementDataItem.value = this.elementDataItemForm.get('value')?.value;
      this.curElementDataItem.contentType = [contentType];
    }
    this.hideElementDataItemDrawer();
  }

  // 直接添加tab而不打开抽屉
  addNewTab(item: any): void {
    // 直接添加一个新的tab项
    item.value = item.value || [];
    item.value.push(this.copyObject(item.children));
    
    // 激活最新添加的tab
    setTimeout(() => {
      item._activeTabIndex = item.value.length - 1;
    }, 0);
  }

  // 检查tab中是否有未填写的内容
  hasUnfilledContent(tab: any[]): boolean {
    if (!Array.isArray(tab)) return false;
    
    for (const item of tab) {
      // 检查当前层级的元素
      if (this.isUnfilledItem(item)) {
        return true;
      }
      
      // 递归检查嵌套的list元素
      if (item.contentType && item.contentType.includes('list') && item.value) {
        if (Array.isArray(item.value)) {
          for (const subTab of item.value) {
            if (this.hasUnfilledContent(subTab)) {
              return true;
            }
          }
        }
      }
      
      // 递归检查嵌套的对象元素
      if (item.contentType && item.contentType.includes('object') && item.children) {
        if (this.hasUnfilledContent(item.children)) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  // 判断单个元素是否未填写
  isUnfilledItem(item: any): boolean {
    // 如果是list或object类型，不直接判断，由专门的方法处理
    if (item.contentType && 
        (item.contentType.includes('list') || item.contentType.includes('object'))) {
      return false;
    }
    
    // 对于普通类型，检查value是否存在且不为空
    return !(item.value || item.value === 0);
  }

  getElementTypeOptions(elementForm: FormGroup): any[] {
    let options = elementForm.controls['contentType'].value;
    return this.RESOURCE_TYPE_OPTIONS.filter(item => options.includes(item.value))
  }
  getElementContentOptions(elementForm: FormGroup): any[] {
    let options = elementForm.controls['options'].value;
    console.log(options);
    return options.map((item: string) => {
      return {
        label: item,
        value: item
      }
    })
  }

  getKeys(item: any) {
    return Object.keys(item);
  }

  copySuccess(): void {
    this.messageService.success('复制成功');
  }
  copyObject(obj: any): any {
    return JSON.parse(JSON.stringify(obj));
  }
}