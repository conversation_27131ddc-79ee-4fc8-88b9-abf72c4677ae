<form nz-form [formGroup]="searchValidateForm" class="ant-advanced-search-form">
    <div nz-row [nzGutter]="24">
        @for (control of searchControlArray; track control['name']) {
        <div nz-col [nzSpan]="8">
            <nz-form-item>
                <nz-form-label>{{ control['labelName'] }}</nz-form-label>
                <nz-form-control>
                    <input nz-input [placeholder]="control['labelName']" [formControlName]="control['name']"
                        [attr.id]="control['name']" />
                </nz-form-control>
            </nz-form-item>
        </div>
        }
    </div>
    <div nz-row>
        <div nz-col [nzSpan]="24" class="search-area">
            <button nz-button [nzType]="'primary'" (click)="search()">搜索</button>
            <button nz-button (click)="resetForm()">重置</button>
            <button nz-button [nzType]="'primary'" (click)="showComponentDrawer()">新增全局组件</button>
        </div>
    </div>
</form>

<div class="search-result-list">
    <nz-table #columnTable [nzData]="listData" [nzScroll]="{ x: '1000px' }">
        <thead>
            <tr>
                <th width="50px">编号</th>
                <th width="100px">名称</th>
                <th width="100px">组件label</th>
                <th width="200px">组件展示图</th>
                <th width="180px">创建时间</th>
                <th width="100px">ID</th>
                <th nzRight width="150px">操作</th>
            </tr>
        </thead>
        <tbody>
            @for (data of columnTable.data; track data['_id']) {
            <tr>
                <td width="50px">{{(pageIndex - 1) * pageSize + $index + 1}}</td>
                <td width="100px">{{data['name']}}</td>
                <td width="100px">{{data['label']}}</td>
                <td width="200px">
                    <img class="component-img" nz-image width="100%" [nzSrc]="data['image']" alt="" />
                </td>
                <td width="180px">{{data['created_at']|date:"yyyy-MM-dd HH:mm:ss"}}</td>
                <td width="100px">
                    <span ngxClipboard [cbContent]="data['_id']" (cbOnSuccess)="copySuccess()"
                        style="cursor: pointer; color: blue; text-decoration: underline;" nz-tooltip
                        nzTooltipTitle="点击复制ID">{{data['_id']}}</span>
                </td>
                <td nzRight width="150px">
                    <a (click)="editComponent(data)">编辑</a>
                    <ng-container *ngIf="!staticComponentList.includes(data.name)">
                        <nz-divider nzType="vertical"></nz-divider>
                        <a (click)="showDeleteComponentModal(data)">删除</a>
                    </ng-container>
                    <nz-divider nzType="vertical"></nz-divider>
                    <a [href]="al_home+'/globalComponentPreview/'+data['_id']" target="_blank">预览</a>
                    <ng-container *ngIf="data.hasDraft">
                        <nz-divider nzType="vertical"></nz-divider>
                        <a (click)="showPublishConfirm(data._id)" class="publish-draft-link">发布更新</a>
                    </ng-container>
                </td>
            </tr>
            }
        </tbody>
    </nz-table>
</div>

<nz-drawer [nzClosable]="true" [nzVisible]="componentDrawerVisible" nzPlacement="right" nzTitle="页面信息"
    [nzWidth]="'100vw'" [nzFooter]="footerTpl" [nzZIndex]="1" (nzOnClose)="closeComponentDrawer()">
    <div *nzDrawerContent class="page-model-edit-drawer">
        <div class="component-list" *ngIf="creating">
            <div class="component-item" *ngFor="let item of componentList; let i = index" (click)="selComponent(item)">
                <div class="item-name">{{item.name}}</div>
                <img class="item-img" [src]="item.image" alt="" />
                <div class="item-remark">{{item.remark}}</div>
            </div>
        </div>
        <div class="page-model">
            <div class="modal-content">
                <form nz-form [formGroup]="componentForm">
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="7" nzRequired>组件别名</nz-form-label>
                        <nz-form-control [nzSpan]="17">
                            <input nz-input formControlName="label" placeholder="请输入组件别名">
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="7" nzRequired>组件图片</nz-form-label>
                        <nz-form-control [nzSpan]="17">
                            <app-image-upload-form uploadUrl="/upload-file/image"
                                formControlName="image"></app-image-upload-form>
                        </nz-form-control>
                    </nz-form-item>
                    <!-- Remark -->
                    <nz-divider nzText="备注"></nz-divider>
                    <nz-form-item class="form-item">
                        <nz-form-label [nzSpan]="7">备注</nz-form-label>
                        <nz-form-control [nzSpan]="17">
                            <textarea style="height: 100px;" nz-input formControlName="remark" placeholder="请输入备注">
                                </textarea>
                        </nz-form-control>
                    </nz-form-item>
                </form>

                <nz-divider nzText="组件数据"></nz-divider>
                <div class="element-datas" *ngIf="curEditComponent">
                    <div class="element-header">
                        <h2 class="element-name">{{curEditComponent.label}}组件数据</h2>
                        <button nz-button nzType="primary" nzSize="small" (click)="toggleSortMode()"
                                *ngIf="!creating && !sortMode">
                            <span nz-icon nzType="drag"></span>
                            排序组件元素
                        </button>
                    </div>
                    <!-- 正常编辑模式 -->
                    <div class="element-data-items" *ngIf="!sortMode">
                        <div class="element-data-item" *ngFor="let item of curEditComponent.elements.children">
                            <span class="element-data-name"
                                (click)="showElementDataItemDrawer(item)">{{item.name}}</span>:
                            <span class="element-data-value"
                                [ngClass]="{ 'empty': !(item.value || item.value === 0), 'filled': (item.value || item.value === 0) }"
                                *ngIf="!item.contentType.includes('list') && !item.contentType.includes('object')">
                                {{item.value ||'未输入' }}
                            </span>
                            <ng-container *ngIf="item.contentType.includes('list')">
                                <ng-container *ngTemplateOutlet="recursiveTab; context: { $implicit: item, level: 0,}">
                                </ng-container>
                            </ng-container>
                            <ng-container *ngIf="item.contentType.includes('object')">
                                <ng-container
                                    *ngTemplateOutlet="objectTemplate; context: { $implicit: item, level: 0 }">
                                </ng-container>
                            </ng-container>
                        </div>
                    </div>

                    <!-- 拖拽排序模式 -->
                    <div class="sort-mode-container" *ngIf="sortMode">
                        <div class="sort-actions">
                            <button nz-button nzType="primary" (click)="saveSortOrder()">
                                <span nz-icon nzType="check"></span>
                                保存顺序
                            </button>
                            <button nz-button nzType="default" (click)="cancelSort()" style="margin-left: 8px;">
                                <span nz-icon nzType="close"></span>
                                取消
                            </button>
                        </div>
                        <div class="sortable-tree">
                            <ng-container *ngTemplateOutlet="sortableTreeNode; context: {
                                children: curEditComponent.elements.children,
                                level: 0,
                                path: 'root'
                            }"></ng-container>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <ng-template #recursiveTab let-item let-level="level">
        <div class="list-container" [style.margin-left.px]="20" [style.padding-left.px]="level > 0 ? 10 : 0"
            [style.border-left]="level > 0 ? '2px solid #e8e8e8' : 'none'">
            <nz-tabset [nzAnimated]="false" [nzSelectedIndex]="item._activeTabIndex || 0"
                (nzSelectedIndexChange)="item._activeTabIndex = $event" nzType="editable-card" (nzAdd)="addNewTab(item)"
                (nzClose)="closeTab(item, $event, 0)">
                @for (tab of item.value; let i = $index; track i) {
                <nz-tab nzClosable [nzTitle]="tabTitleTemplate">
                    <ng-template #tabTitleTemplate>
                        <span>{{ item.name }} {{ i }}</span>
                        <span *ngIf="hasUnfilledContent(tab)" class="unfilled-indicator" title="该Tab中有未填写的内容">!</span>
                    </ng-template>
                    <div class="element-data-item" *ngFor="let childItem of tab" [style.padding-left.px]="15">
                        <span class="element-data-name"
                            (click)="showElementDataItemDrawer(childItem)">{{childItem.name}}</span>:
                        <span class="element-data-value"
                            [ngClass]="{ 'empty': !(childItem.value || childItem.value === 0), 'filled': (childItem.value || childItem.value === 0) }"
                            *ngIf="!childItem.contentType.includes('list') && !childItem.contentType.includes('object')">
                            {{childItem.value ||'未输入' }}
                        </span>
                        <ng-container *ngIf="childItem.contentType.includes('list')">
                            <ng-container
                                *ngTemplateOutlet="recursiveTab; context: { $implicit: childItem, level: level + 1 }">
                            </ng-container>
                        </ng-container>
                        <ng-container *ngIf="childItem.contentType.includes('object')">
                            <ng-container
                                *ngTemplateOutlet="objectTemplate; context: { $implicit: childItem, level: (level || 0) + 1 }">
                            </ng-container>
                        </ng-container>
                    </div>
                </nz-tab>
                }
            </nz-tabset>
        </div>
    </ng-template>
    <ng-template #footerTpl>
        <div style="float: right">
            <button nz-button style="margin-right: 8px;" (click)="closeComponentDrawer()">Cancel</button>
            <button nz-button nzType="primary" (click)="saveDraft()"
                *ngIf="curEditComponent && curEditComponent._id">保存草稿</button>
            <button nz-button nzType="primary" (click)="showPublishConfirm(curEditComponent._id)"
                *ngIf="curEditComponent && curEditComponent._id && curEditComponent.hasDraft">发布</button>
            <button nz-button nzType="primary" (click)="submitComponentInfo()"
                *ngIf="!(curEditComponent && curEditComponent._id)">提交</button>
        </div>
    </ng-template>
</nz-drawer>

<nz-drawer [nzClosable]="true" [nzVisible]="elementDataItemDrawerVisible" nzPlacement="right"
    [nzTitle]="curElementDataItem.name" [nzWidth]="'500px'" [nzMaskClosable]="true" [nzFooter]="footerTpl2"
    [nzZIndex]="2" (nzOnClose)="hideElementDataItemDrawer()">
    <div *nzDrawerContent class="page-model-edit-drawer">
        <form nz-form [formGroup]="elementDataItemForm" style="width: 100%;">
            <nz-form-item class="form-item">
                <nz-form-label [nzSpan]="6" nzRequired>元素类型</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <nz-select nzPlaceHolder="选择元素类型" formControlName="contentTypeValue" style="width: 100%">
                        @for (option of getElementTypeOptions(elementDataItemForm); track $index) {
                        <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        }
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'text'">
                <nz-form-label [nzSpan]="6" nzRequired>元素内容</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <textarea nz-input formControlName="value" placeholder="请输入元素内容（支持换行和空格格式）" rows="4"
                        style="resize: vertical;"></textarea>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item"
                *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'richText'">
                <nz-form-label [nzSpan]="6" nzRequired>元素内容</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <quill-editor formControlName="value" [style]="{width: '100%', height: '300px'}"
                        [placeholder]="'请输入内容...'"></quill-editor>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'list'">
                <nz-form-label nzRequired>新增{{curElementDataItem.name}}列表</nz-form-label>
                <nz-form-control>
                    <button nz-button [nzType]="'primary'" (click)="submitElementNode()">新增列表</button>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'image'">
                <nz-form-label [nzSpan]="6" nzRequired>元素图片</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-image-upload-form uploadUrl="/upload-file/image"
                        formControlName="value"></app-image-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'video'">
                <nz-form-label [nzSpan]="6" nzRequired>元素视频</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-video-upload-form uploadUrl="/upload-file/video" formControlName="value">
                    </app-video-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'select'">
                <nz-form-label [nzSpan]="6" nzRequired>内容选择</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <nz-select nzPlaceHolder="选择元素类型" formControlName="value" style="width: 100%">
                        @for (option of getElementContentOptions(elementDataItemForm); track $index) {
                        <nz-option [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        }
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'file'">
                <nz-form-label [nzSpan]="6" nzRequired>元素文件</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <app-file-upload-form uploadUrl="/upload-file/file" formControlName="value">
                    </app-file-upload-form>
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'palette'">
                <nz-form-label [nzSpan]="6" nzRequired>选择颜色</nz-form-label>
                <nz-form-control [nzSpan]="18">
                    <input nz-input formControlName="value" placeholder="请输入颜色值或点击选择颜色" type="color" style="height: 40px;">
                </nz-form-control>
            </nz-form-item>
            <nz-form-item class="form-item" *ngIf="elementDataItemForm.controls['contentTypeValue'].value === 'object'">
                <nz-form-label nzRequired>编辑{{curElementDataItem.name}}对象</nz-form-label>
                <nz-form-control>
                    <button nz-button [nzType]="'primary'" (click)="submitElementNode()">确认对象类型</button>
                </nz-form-control>
            </nz-form-item>

        </form>
    </div>
    <ng-template #footerTpl2>
        <div style="float: right">
            <button nz-button style="margin-right: 8px;" (click)="hideElementDataItemDrawer()">Cancel</button>
            <button nz-button nzType="primary" (click)="submitElementNode()">提交</button>
        </div>
    </ng-template>
</nz-drawer>

<ng-template #objectTemplate let-item let-level="level">
    <div class="object-children" [style.margin-left.px]="20" [style.padding-left.px]="level > 0 ? 10 : 0"
        [style.border-left]="level > 0 ? '2px solid #e8e8e8' : 'none'">
        <div class="element-data-item" *ngFor="let childItem of item.children" [style.padding-left.px]="15">
            <span class="element-data-name" (click)="showElementDataItemDrawer(childItem)">{{childItem.name}}</span>:
            <span class="element-data-value"
                [ngClass]="{ 'empty': !(childItem.value || childItem.value === 0), 'filled': (childItem.value || childItem.value === 0) }"
                *ngIf="!childItem.contentType.includes('list') && !childItem.contentType.includes('object')">
                {{childItem.value ||'未输入' }}
            </span>
            <ng-container *ngIf="childItem.contentType.includes('list')">
                <ng-container
                    *ngTemplateOutlet="recursiveTab; context: { $implicit: childItem, level: (level || 0) + 1 }">
                </ng-container>
            </ng-container>
            <ng-container *ngIf="childItem.contentType.includes('object')">
                <ng-container
                    *ngTemplateOutlet="objectTemplate; context: { $implicit: childItem, level: (level || 0) + 1 }">
                </ng-container>
            </ng-container>
        </div>
    </div>
</ng-template>

<nz-modal [(nzVisible)]="deleteComponentModalVisible" nzTitle="删除组件" (nzOnCancel)="hideDeleteComponentModal()"
    (nzOnOk)="deleteComponent()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        @if (curEditComponent) {
        确定要删除<span class="component-name">{{ curEditComponent.name }}</span> 吗？
        }
    </div>
</nz-modal>

<nz-modal [(nzVisible)]="publishConfirmModalVisible" nzTitle="发布确认" (nzOnCancel)="hidePublishConfirm()"
    (nzOnOk)="confirmPublish()" [nzOkLoading]="isOkLoading">
    <div *nzModalContent>
        确定要发布此组件的更新吗？发布后用户将看到最新的内容。
    </div>
</nz-modal>

<!-- 可拖拽的树形节点模板 -->
<ng-template #sortableTreeNode let-children="children" let-level="level" let-path="path">
    <div class="sortable-level"
         cdkDropList
         [cdkDropListData]="children"
         (cdkDropListDropped)="dropInSameLevel($event, children)"
         [style.margin-left.px]="level * 20">
        <div class="sortable-node"
             *ngFor="let node of children; let i = index"
             cdkDrag
             [cdkDragData]="node">
            <div class="node-content">
                <div class="node-info">
                    <span class="node-icon" *ngIf="node.children && node.children.length > 0">
                        <span nz-icon nzType="folder" nzTheme="outline"></span>
                    </span>
                    <span class="node-icon" *ngIf="!node.children || node.children.length === 0">
                        <span nz-icon nzType="file-text" nzTheme="outline"></span>
                    </span>
                    <span class="node-name">{{ node.name }}</span>
                    <span class="node-key">({{ node.key }})</span>
                    <span class="node-type">{{ node.contentType?.join(', ') }}</span>
                </div>
                <div class="drag-handle" cdkDragHandle>
                    <span nz-icon nzType="drag" nzTheme="outline"></span>
                </div>
            </div>

            <!-- 递归显示子节点 -->
            <div class="child-nodes" *ngIf="node.children && node.children.length > 0">
                <ng-container *ngTemplateOutlet="sortableTreeNode; context: {
                    children: node.children,
                    level: level + 1,
                    path: getNodePath(node, path)
                }"></ng-container>
            </div>
        </div>
    </div>
</ng-template>