import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Document {
  _id: string;
  name: string;
  originalName: string;
  type: 'doc' | 'model' | 'other';
  size: number;
  url: string;
  downloadCount: number;
  created_at: number;
  updated_at: number;
}

export interface DocumentListResponse {
  list: Document[];
  total: number;
  ps: number;
  pn: number;
}

export interface ApiResponse<T> {
  code: number;
  data: T;
  msg: string;
}

export interface DocumentStatistics {
  totalDocuments: number;
  docCount: number;
  modelCount: number;
  otherCount: number;
  totalDownloads: number;
}

@Injectable({
  providedIn: 'root'
})
export class DocumentsService {
  private apiUrl = '/documents';

  constructor(private http: HttpClient) {}

  // 获取文档列表
  getList(pageIndex: number = 1, pageSize: number = 20, search: any = {}): Observable<ApiResponse<DocumentListResponse>> {
    const params = {
      pn: pageIndex.toString(),
      ps: pageSize.toString(),
      ...search
    };
    return this.http.get<ApiResponse<DocumentListResponse>>(`${this.apiUrl}/list`, { params });
  }

  // 获取单个文档
  getDocument(id: string): Observable<ApiResponse<Document>> {
    return this.http.get<ApiResponse<Document>>(`${this.apiUrl}/${id}`);
  }

  // 上传文档
  uploadDocument(file: File, name?: string, type?: string): Observable<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);
    if (name) {
      formData.append('name', name);
    }
    if (type) {
      formData.append('type', type);
    }
    return this.http.post<ApiResponse<any>>(`${this.apiUrl}/upload`, formData);
  }

  // 更新文档信息
  updateDocument(id: string, data: Partial<Document>): Observable<ApiResponse<any>> {
    return this.http.patch<ApiResponse<any>>(`${this.apiUrl}/${id}`, data);
  }

  // 删除文档
  deleteDocument(id: string): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`);
  }

  // 下载文档
  downloadDocument(id: string): void {
    window.open(`/api/documents/download/${id}`, '_blank');
  }

  // 获取统计信息
  getStatistics(): Observable<ApiResponse<DocumentStatistics>> {
    return this.http.get<ApiResponse<DocumentStatistics>>(`${this.apiUrl}/statistics/overview`);
  }

  // 格式化文件大小
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 获取文档类型标签颜色
  getTypeTagColor(type: string): string {
    switch (type) {
      case 'doc':
        return 'blue';
      case 'model':
        return 'green';
      case 'other':
        return 'orange';
      default:
        return 'default';
    }
  }

  // 获取文档类型中文名称
  getTypeLabel(type: string): string {
    switch (type) {
      case 'doc':
        return '文档';
      case 'model':
        return '模型';
      case 'other':
        return '其他';
      default:
        return '未知';
    }
  }
}
