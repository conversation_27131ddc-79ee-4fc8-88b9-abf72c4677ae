// 统计卡片样式
.statistics-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;

  .stat-card {
    flex: 1;
    min-width: 200px;
    
    ::ng-deep .ant-card-body {
      padding: 16px;
    }
    
    ::ng-deep .ant-statistic-title {
      color: #666;
      font-size: 14px;
    }
    
    ::ng-deep .ant-statistic-content {
      color: #1890ff;
      font-weight: 600;
    }
  }
}

// 搜索表单样式
.search-form {
  background: #fafafa;
  padding: 24px;
  border-radius: 6px;
  margin-bottom: 24px;
  
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  .ant-form-item:last-child {
    margin-bottom: 0;
  }
}

// 文档列表样式
.document-table-container {
  // 添加响应式容器，支持水平滚动
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: 24px;
  
  // 确保在小屏幕上也能正确显示表格
  ::ng-deep .ant-table-wrapper {
    min-width: 1200px;
  }
}

.document-list {
  .document-name {
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    
    &:hover {
      color: #1890ff;
    }
  }
  
  .document-id {
    .id-text {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #666;
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 4px;
      display: inline-block;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  // 表格行悬停效果
  ::ng-deep .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
  }
  
  // 操作按钮样式
  .ant-btn {
    margin-right: 8px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

// 上传模态框样式
::ng-deep .ant-modal {
  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    text-align: center;
    padding: 40px 20px;
    transition: border-color 0.3s;
    
    &:hover {
      border-color: #1890ff;
    }
    
    .ant-upload-drag-icon {
      font-size: 48px;
      color: #999;
      margin-bottom: 16px;
    }
    
    .ant-upload-text {
      font-size: 16px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .ant-upload-hint {
      font-size: 14px;
      color: #999;
      line-height: 1.5;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .statistics-cards {
    .stat-card {
      min-width: 100%;
    }
  }
  
  .search-form {
    padding: 16px;
    
    .ant-col {
      width: 100% !important;
      margin-bottom: 16px;
    }
    
    .ant-form-item:last-child {
      .ant-col {
        margin-bottom: 0;
      }
    }
  }
  
  // 小屏幕优化表格容器
  .document-table-container {
    margin-left: -16px;
    margin-right: -16px;
    
    ::ng-deep .ant-table-wrapper {
      min-width: 100%;
    }
  }
}

// 标签样式优化
::ng-deep .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  
  &.ant-tag-blue {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }
  
  &.ant-tag-green {
    background: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
  
  &.ant-tag-orange {
    background: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
  }
}

// 工具提示样式
::ng-deep .ant-tooltip {
  .ant-tooltip-inner {
    max-width: 300px;
    word-break: break-all;
  }
}

// 超小屏幕适配
@media (max-width: 576px) {
  .search-form {
    .ant-form-item {
      margin-bottom: 12px;
    }
  }
  
  .document-table-container {
    margin-left: -12px;
    margin-right: -12px;
    font-size: 12px;
    
    ::ng-deep {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
      }
      
      .ant-table-thead > tr > th {
        font-size: 12px;
      }
      
      .ant-btn {
        font-size: 12px;
        padding: 0 8px;
        height: 28px;
        
        .anticon {
          font-size: 12px;
        }
      }
      
      .ant-tag {
        font-size: 10px;
        padding: 0 4px;
        line-height: 1.4;
      }
    }
  }
}