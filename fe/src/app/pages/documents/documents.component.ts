import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzUploadModule, NzUploadFile } from 'ng-zorro-antd/upload';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { fromEvent, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

import { DocumentsService, Document, DocumentStatistics } from './documents.service';
import { TablePageSizeService } from '../../services/table-page-size.service';

@Component({
  selector: 'app-documents',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzSelectModule,
    NzTagModule,
    NzIconModule,
    NzPopconfirmModule,
    NzCardModule,
    NzStatisticModule,
    NzDividerModule,
    NzToolTipModule
  ],
  templateUrl: './documents.component.html',
  styleUrl: './documents.component.scss'
})
export class DocumentsComponent implements OnInit, OnDestroy {
  // 表单和数据
  searchForm: FormGroup;
  uploadForm: FormGroup;
  listData: Document[] = [];
  statistics: DocumentStatistics | null = null;
  
  // 分页
  pageIndex: number = 1;
  pageSize: number = 20;
  total: number = 0;
  listLoading: boolean = false;
  
  // 模态框
  uploadModalVisible: boolean = false;
  editModalVisible: boolean = false;
  uploadLoading: boolean = false;
  
  // 上传
  fileList: NzUploadFile[] = [];
  currentEditDocument: Document | null = null;
  
  // 响应式
  private resizeSubscription: Subscription | null = null;
  isMobile: boolean = false;
  
  // 选项
  documentTypeOptions = [
    { label: '全部', value: '' },
    { label: '文档', value: 'doc' },
    { label: '模型', value: 'model' },
    { label: '其他', value: 'other' }
  ];

  constructor(
    private fb: FormBuilder,
    private documentsService: DocumentsService,
    private messageService: NzMessageService,
    private tablePageSizeService: TablePageSizeService
  ) {
    this.searchForm = this.fb.group({
      name: [''],
      type: ['']
    });
    
    this.uploadForm = this.fb.group({
      name: [''],
      type: ['']
    });
    
    this.checkScreenSize();
  }

  ngOnInit(): void {
    this.pageSize = this.tablePageSizeService.getPageSize();
    this.getList();
    this.getStatistics();
    this.setupResizeListener();
  }

  ngOnDestroy(): void {
    if (this.resizeSubscription) {
      this.resizeSubscription.unsubscribe();
    }
  }

  // 设置窗口大小变化监听器
  private setupResizeListener(): void {
    this.resizeSubscription = fromEvent(window, 'resize')
      .pipe(debounceTime(300))
      .subscribe(() => {
        this.checkScreenSize();
      });
  }

  // 检查屏幕尺寸
  private checkScreenSize(): void {
    this.isMobile = window.innerWidth < 768;
  }

  // 获取文档列表
  getList(reset = false): void {
    if (reset) {
      this.pageIndex = 1;
    }
    
    this.listLoading = true;
    const searchFilter = this.searchForm.value;
    
    this.documentsService.getList(this.pageIndex, this.pageSize, searchFilter).subscribe({
      next: (res) => {
        if (res.code === 0) {
          this.listData = res.data.list;
          this.total = res.data.total;
          this.pageIndex = res.data.pn;
          this.pageSize = res.data.ps;
        }
        this.listLoading = false;
      },
      error: () => {
        this.listLoading = false;
        this.messageService.error('获取文档列表失败');
      }
    });
  }

  // 获取统计信息
  getStatistics(): void {
    this.documentsService.getStatistics().subscribe({
      next: (res) => {
        if (res.code === 0) {
          this.statistics = res.data;
        }
      },
      error: () => {
        this.messageService.error('获取统计信息失败');
      }
    });
  }

  // 搜索
  search(): void {
    this.getList(true);
  }

  // 重置搜索
  resetSearch(): void {
    this.searchForm.reset();
    this.getList(true);
  }

  // 分页变化
  onPageIndexChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.getList();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.tablePageSizeService.setPageSize(pageSize);
    this.getList(true);
  }

  // 显示上传模态框
  showUploadModal(): void {
    this.uploadModalVisible = true;
    this.uploadForm.reset();
    this.fileList = [];
  }

  // 隐藏上传模态框
  hideUploadModal(): void {
    this.uploadModalVisible = false;
    this.fileList = [];
  }

  // 文件上传变化
  handleUploadChange(info: any): void {
    this.fileList = [...info.fileList];
  }

  // 处理上传
  handleUpload(): void {
    if (this.fileList.length === 0) {
      this.messageService.error('请选择要上传的文件');
      return;
    }

    const formData = this.uploadForm.value;
    const file = this.fileList[0].originFileObj as File;

    this.uploadLoading = true;

    this.documentsService.uploadDocument(file, formData.name, formData.type).subscribe({
      next: (res) => {
        if (res.code === 0) {
          this.messageService.success('文档上传成功');
          this.hideUploadModal();
          this.getList();
          this.getStatistics();
        } else {
          this.messageService.error(res.msg || '上传失败');
        }
        this.uploadLoading = false;
      },
      error: () => {
        this.messageService.error('文档上传失败');
        this.uploadLoading = false;
      }
    });
  }

  // 自定义上传（阻止默认上传行为）
  customUpload = (): any => {
    return false;
  };

  // 下载文档
  downloadDocument(document: Document): void {
    this.documentsService.downloadDocument(document._id);
    this.messageService.success('开始下载文档');
    
    // 刷新列表以更新下载次数
    setTimeout(() => {
      this.getList();
    }, 1000);
  }

  // 删除文档
  deleteDocument(document: Document): void {
    this.documentsService.deleteDocument(document._id).subscribe({
      next: (res) => {
        if (res.code === 0) {
          this.messageService.success('删除成功');
          this.getList();
          this.getStatistics();
        } else {
          this.messageService.error(res.msg || '删除失败');
        }
      },
      error: () => {
        this.messageService.error('删除失败');
      }
    });
  }

  // 格式化文件大小
  formatFileSize(bytes: number): string {
    return this.documentsService.formatFileSize(bytes);
  }

  // 获取类型标签颜色
  getTypeTagColor(type: string): string {
    return this.documentsService.getTypeTagColor(type);
  }

  // 获取类型标签文本
  getTypeLabel(type: string): string {
    return this.documentsService.getTypeLabel(type);
  }

  // 格式化时间
  formatTime(timestamp: number): string {
    return new Date(timestamp).toLocaleString('zh-CN');
  }

  // 文件上传前的检查
  beforeUpload = (file: NzUploadFile): boolean => {
    const isLt50M = file.size! / 1024 / 1024 < 50;
    if (!isLt50M) {
      this.messageService.error('文件大小不能超过50MB!');
      return false;
    }
    return true;
  };
}