<!-- 统计卡片 -->
<div class="statistics-cards" *ngIf="statistics">
  <nz-card nzSize="small" class="stat-card">
    <nz-statistic nzTitle="总文档数" [nzValue]="statistics.totalDocuments" nzSuffix="个"></nz-statistic>
  </nz-card>
  <nz-card nzSize="small" class="stat-card">
    <nz-statistic nzTitle="文档类型" [nzValue]="statistics.docCount" nzSuffix="个"></nz-statistic>
  </nz-card>
  <nz-card nzSize="small" class="stat-card">
    <nz-statistic nzTitle="模型类型" [nzValue]="statistics.modelCount" nzSuffix="个"></nz-statistic>
  </nz-card>
  <nz-card nzSize="small" class="stat-card">
    <nz-statistic nzTitle="其他类型" [nzValue]="statistics.otherCount" nzSuffix="个"></nz-statistic>
  </nz-card>
  <nz-card nzSize="small" class="stat-card">
    <nz-statistic nzTitle="总下载次数" [nzValue]="statistics.totalDownloads" nzSuffix="次"></nz-statistic>
  </nz-card>
</div>

<nz-divider></nz-divider>

<!-- 搜索表单 -->
<div class="search-form">
  <form nz-form [formGroup]="searchForm" (ngSubmit)="search()">
    <div nz-row [nzGutter]="16">
      <div nz-col [nzSpan]="6">
        <nz-form-item>
          <nz-form-label>文档名称</nz-form-label>
          <nz-form-control>
            <input nz-input formControlName="name" placeholder="请输入文档名称" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col [nzSpan]="6">
        <nz-form-item>
          <nz-form-label>文档类型</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="type" nzPlaceHolder="请选择文档类型">
              <nz-option *ngFor="let option of documentTypeOptions" [nzLabel]="option.label" [nzValue]="option.value"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col [nzSpan]="12">
        <nz-form-item>
          <nz-form-control>
            <button nz-button nzType="primary" type="submit" [nzLoading]="listLoading">
              <span nz-icon nzType="search"></span>
              搜索
            </button>
            <button nz-button type="button" (click)="resetSearch()" style="margin-left: 8px;">
              重置
            </button>
            <button nz-button nzType="primary" type="button" (click)="showUploadModal()" style="margin-left: 8px;">
              <span nz-icon nzType="upload"></span>
              上传文档
            </button>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </form>
</div>

<!-- 文档列表 -->
<div class="document-table-container">
  <nz-table 
    #documentTable 
    nzShowSizeChanger 
    nzShowQuickJumper 
    [nzData]="listData" 
    [nzFrontPagination]="false"
    nzPaginationType="small" 
    [nzPageSizeOptions]="[10,20,30,40,50]" 
    [(nzPageSize)]="pageSize"
    [(nzPageIndex)]="pageIndex" 
    [nzTotal]="total" 
    [nzLoading]="listLoading"
    (nzPageIndexChange)="onPageIndexChange(pageIndex)" 
    (nzPageSizeChange)="onPageSizeChange(pageSize)"
    [nzScroll]="{ x: '1200px' }">
    
    <thead>
      <tr>
        <th width="60px">序号</th>
        <th width="200px">文档名称</th>
        <th width="120px">文档类型</th>
        <th width="100px">文件大小</th>
        <th width="100px">下载次数</th>
        <th width="180px">上传时间</th>
        <th width="200px">文档ID</th>
        <th nzRight width="200px">操作</th>
      </tr>
    </thead>
    
    <tbody>
      <tr *ngFor="let document of documentTable.data; let i = index">
        <td>{{ (pageIndex - 1) * pageSize + i + 1 }}</td>
        <td>
          <div class="document-name">
            <span nz-tooltip [nzTooltipTitle]="document.originalName">
              {{ document.name }}
            </span>
          </div>
        </td>
        <td>
          <nz-tag [nzColor]="getTypeTagColor(document.type)">
            {{ getTypeLabel(document.type) }}
          </nz-tag>
        </td>
        <td>{{ formatFileSize(document.size) }}</td>
        <td>
          <nz-tag nzColor="blue">{{ document.downloadCount }}</nz-tag>
        </td>
        <td>{{ formatTime(document.created_at) }}</td>
        <td>
          <div class="document-id">
            <span class="id-text">{{ document._id }}</span>
          </div>
        </td>
        <td nzRight> 
          <button 
            nz-button 
            nzType="primary" 
            nzSize="small" 
            (click)="downloadDocument(document)"
            nz-tooltip="下载文档">
            <span nz-icon nzType="download"></span>
            下载
          </button>
          <nz-popconfirm 
            nzPopconfirmTitle="确定要删除这个文档吗？" 
            (nzOnConfirm)="deleteDocument(document)"
            nzPopconfirmPlacement="topRight">
            <button 
              nz-button 
              nzDanger 
              nzSize="small" 
              nz-popconfirm
              style="margin-left: 8px;"
              nz-tooltip="删除文档">
              <span nz-icon nzType="delete"></span>
              删除
            </button>
          </nz-popconfirm>
        </td>
      </tr>
    </tbody>
  </nz-table>
</div>

<!-- 上传文档模态框 -->
<nz-modal 
  [(nzVisible)]="uploadModalVisible" 
  nzTitle="上传文档" 
  [nzOkLoading]="uploadLoading"
  (nzOnCancel)="hideUploadModal()"
  (nzOnOk)="handleUpload()"
  [nzOkDisabled]="fileList.length === 0">
  
  <ng-container *nzModalContent>
    <form nz-form [formGroup]="uploadForm">
      <nz-form-item>
        <nz-form-label nzRequired>选择文件</nz-form-label>
        <nz-form-control>
          <nz-upload
            nzType="drag"
            [nzMultiple]="false"
            [nzShowUploadList]="true"
            [nzBeforeUpload]="beforeUpload"
            [nzFileList]="fileList"
            (nzChange)="handleUploadChange($event)"
            [nzCustomRequest]="customUpload">
            <p class="ant-upload-drag-icon">
              <span nz-icon nzType="inbox"></span>
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">
              支持单个文件上传，文件大小不超过50MB<br>
              支持格式：PDF、Word、Excel、PowerPoint、文本文件、压缩包等
            </p>
          </nz-upload>
        </nz-form-control>
      </nz-form-item>
      
      <nz-form-item>
        <nz-form-label>文档名称</nz-form-label>
        <nz-form-control>
          <input nz-input formControlName="name" placeholder="留空则使用原文件名" />
        </nz-form-control>
      </nz-form-item>
      
      <nz-form-item>
        <nz-form-label>文档类型</nz-form-label>
        <nz-form-control>
          <nz-select formControlName="type" nzPlaceHolder="留空则自动识别">
            <nz-option nzLabel="文档" nzValue="doc"></nz-option>
            <nz-option nzLabel="模型" nzValue="model"></nz-option>
            <nz-option nzLabel="其他" nzValue="other"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-container>
</nz-modal>