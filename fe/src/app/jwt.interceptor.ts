import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { AuthService } from './auth.service';
import { NzMessageService } from 'ng-zorro-antd/message';

export const JWTInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router);
  const message = inject(NzMessageService);
  const authService = inject(AuthService);

  return next(req).pipe(
    catchError((error) => {
      if (error.status === 401) {
        if (error.error.statusCode !== 402) {
          authService.logout();
          message.error(error.error.message);
          setTimeout(() => {
            router.navigate(['/login']);
          }, 3000);
        }else{
          message.error(error.error.message);
        }
      }
      return throwError(() => error); // 抛出错误，供其他部分处理
    })
  );
};