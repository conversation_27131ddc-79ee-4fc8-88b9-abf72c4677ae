var express = require('express');
var request = require('request');
var app = express();

const prodServerUrl = 'http://localhost:3200'
const devServerUrl = 'http://localhost:3201'
const proxUrl = devServerUrl;
app.use('/', function (req, res, next) {
    console.log(req.url);
    const url = proxUrl + req.url;
    req.pipe(request(url)).on('error', function (err) {
        console.error(err);
        res.status(500).end();
    }).pipe(res);
});

app.listen(4208);
