const PROXY_CONFIG = [
    {
        context: ["/"],
        target: "http://localhost:3201",
        secure: false,
        logLevel: "debug",
        bypass: function (req) {
            // 排除静态资源
            if (req.url === "/" || req.url.startsWith("/@fs") || req.url.startsWith("/@vite") || req.url.match(/^\/[^\/]+\.(html|js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$/)) {
                return req.url; // 返回本地路径，跳过代理
            }
        }
    },
]

module.exports = PROXY_CONFIG;