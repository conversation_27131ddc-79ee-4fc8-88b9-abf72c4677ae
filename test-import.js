const axios = require('axios');

async function testImportFromUrl() {
  try {
    console.log('测试从URL导入文章功能...');
    
    // 测试一个简单的网页
    const testUrl = 'https://example.com';
    
    const response = await axios.post('http://localhost:3200/news/import-from-url', {
      url: testUrl
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('导入结果:', response.data);
    
    if (response.data.code === 0) {
      console.log('✅ 导入成功!');
      console.log(`文章ID: ${response.data.data.articleId}`);
      console.log(`标题: ${response.data.data.title}`);
      console.log(`图片数量: ${response.data.data.imageCount}/${response.data.data.totalImages}`);
    } else {
      console.log('❌ 导入失败:', response.data.msg);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testImportFromUrl();
