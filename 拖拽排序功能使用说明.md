# 组件管理拖拽排序功能使用说明

## 功能概述

在组件管理中新增了拖拽排序功能，允许用户在编辑组件时对组件元素进行拖拽排序。

## 功能特点

1. **切换按钮**: 在编辑组件时，可以点击"排序组件元素"按钮切换到排序模式
2. **树形结构**: 以递进的树形结构显示组件的所有元素
3. **拖拽排序**: 可以在同一层级内拖拽节点进行排序
4. **保存/取消**: 提供保存顺序和取消操作的按钮
5. **自定义实现**: 使用自定义的树形组件，不依赖现有的tree-view组件

## 使用步骤

### 1. 进入编辑模式
- 在组件管理页面，点击某个组件的"编辑"按钮
- 进入组件编辑抽屉

### 2. 切换到排序模式
- 在"组件数据"部分，点击右上角的"排序组件元素"按钮
- 界面会切换到拖拽排序模式

### 3. 进行拖拽排序
- 在树形结构中，每个节点都可以拖拽
- 只能在同一层级内进行拖拽排序
- 拖拽时会有视觉反馈，包括拖拽预览和占位符

### 4. 保存或取消
- **保存顺序**: 点击"保存顺序"按钮确认更改
- **取消**: 点击"取消"按钮恢复原始顺序

## 技术实现

### 核心技术
- **Angular CDK Drag & Drop**: 提供拖拽功能
- **递归模板**: 支持多层嵌套的树形结构
- **数据备份**: 支持取消操作的数据恢复

### 主要组件
1. **TypeScript 方法**:
   - `toggleSortMode()`: 切换排序模式
   - `dropInSameLevel()`: 处理同级拖拽
   - `saveSortOrder()`: 保存排序结果
   - `cancelSort()`: 取消排序

2. **HTML 模板**:
   - 排序按钮
   - 排序模式界面
   - 可拖拽的树形节点模板

3. **样式设计**:
   - 拖拽预览效果
   - 树形结构缩进
   - 拖拽指示器

## 限制说明

1. **同级拖拽**: 只能在同一父节点下的子节点之间进行拖拽
2. **层级保持**: 不能将节点拖拽到不同的层级
3. **结构完整性**: 保持原有的树形结构不被破坏

## 视觉效果

- **拖拽预览**: 拖拽时节点会有透明度变化和旋转效果
- **占位符**: 显示拖拽目标位置
- **悬停效果**: 鼠标悬停时节点边框变蓝
- **拖拽手柄**: 每个节点右侧有拖拽图标

## 注意事项

1. 排序功能只在编辑现有组件时可用，创建新组件时不显示
2. 进入排序模式后，原有的编辑功能会被隐藏
3. 取消操作会完全恢复到进入排序模式前的状态
4. 保存后的顺序会立即生效，但需要保存整个组件才能持久化
